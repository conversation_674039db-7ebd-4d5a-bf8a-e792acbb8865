# Echo Lab 项目架构

本文档概述了 Echo Lab 项目的整体架构设计，包括前端和后端组件的结构和交互方式。

## 系统概述

Echo Lab 是一个用于创建日语听力练习视频的应用程序，由前端编辑器和后端服务组成。用户可以通过编辑器创建内容，生成视频，并进行播放和学习。

### 主要功能

- 文本内容编辑和管理
- 文本序列处理和环节设置
- 音频生成和管理
- 视频生成和播放
- 用户认证和内容管理

### 技术栈

- **前端**：Vue 3 + Vite + Element Plus
- **后端**：Node.js + Express + MySQL
- **部署**：Nginx + Alibaba Cloud

## 设计理念

Echo Lab 采用声明式配置和运行时生成架构，主要设计理念包括：

1. **节点只存储配置，不存储数据**：节点只存储配置信息，数据在运行时生成
2. **配置与数据分离**：配置信息和生成的数据分开管理
3. **简化错误处理**：通过明确的数据流和状态管理简化错误处理
4. **明确组件职责**：每个组件都有明确的职责，避免职责混淆

### 声明式配置

声明式配置是 Echo Lab 的核心设计理念之一，它意味着：

- 节点配置是描述性的，而不是命令性的
- 配置描述"做什么"，而不是"怎么做"
- 配置是纯数据，可以序列化为 JSON
- 配置可以独立于应用程序存在和传输

这种设计使得节点配置可以轻松地保存、加载和共享，同时也使得系统更加灵活和可扩展。

### 运行时生成

运行时生成是 Echo Lab 的另一个核心设计理念，它意味着：

- 数据不会预先计算和存储
- 数据在需要时根据配置生成
- 生成的数据可以缓存，但不会持久化
- 配置变化时，数据会重新生成

这种设计使得系统更加高效和灵活，因为它只在需要时生成数据，并且可以根据配置的变化自动更新数据。

## 前端架构

前端采用 Vue 3 的 Composition API 和组件化设计，使用 Vite 作为构建工具，Element Plus 作为 UI 组件库。

### 目录结构

```
echo-lab/
├── src/
│   ├── assets/         # 静态资源
│   ├── components/     # 组件
│   ├── composables/    # 组合式函数
│   ├── config/         # 配置文件
│   ├── core/           # 核心功能
│   ├── router/         # 路由配置
│   ├── services/       # 服务层
│   ├── stores/         # 状态管理
│   ├── styles/         # 样式文件
│   ├── utils/          # 工具函数
│   └── views/          # 页面视图
```

### 核心模块

1. **编辑器模块**：基于节点和连接的可视化编辑器
2. **播放器模块**：视频播放和交互控制
3. **内容管理模块**：用户内容的管理和操作
4. **用户认证模块**：用户登录和权限控制

### 状态管理

使用 Pinia 进行状态管理，主要包括以下几个 store：

- **nodeStore**：管理节点数据和操作
- **editorStore**：管理编辑器状态和操作
- **playerStore**：管理播放器状态和操作
- **userStore**：管理用户信息和认证状态
- **contentStore**：管理内容列表和操作

## 后端架构

后端采用 Node.js + Express 框架，使用 MySQL 数据库存储数据，提供 RESTful API 接口。

### 目录结构

```
backend/
├── config/         # 配置文件
├── controllers/    # 控制器
├── database/       # 数据库配置
├── middleware/     # 中间件
├── migrations/     # 数据库迁移
├── models/         # 数据模型
├── routes/         # 路由定义
├── scripts/        # 脚本文件
├── services/       # 服务层
└── temp/           # 临时文件
```

### 核心模块

1. **用户模块**：用户认证和管理
2. **内容模块**：内容的创建、读取、更新和删除
3. **资源模块**：音频、翻译等资源的生成和管理
4. **TTS模块**：文本转语音服务集成
5. **存储模块**：文件存储和管理

### 数据库模型

- **User**：用户信息
- **Content**：内容信息
- **Resource**：资源信息
- **Feedback**：用户反馈

## 数据流

### 编辑器数据流

1. 用户在编辑器中创建和配置节点
2. 节点数据保存在前端状态中
3. 用户保存内容时，数据发送到后端
4. 后端处理并存储数据
5. 返回保存结果给前端

### 播放数据流

1. 用户请求播放内容
2. 前端从后端获取内容数据
3. 前端生成时间线数据
4. 播放器根据时间线数据播放内容

### 资源生成数据流

1. 用户请求生成资源（如音频、翻译）
2. 前端发送请求到后端
3. 后端调用相应的服务生成资源
4. 资源保存到存储系统
5. 资源URL返回给前端

## 系统集成

### 外部服务集成

- **Google TTS**：日语和英语文本转语音
- **Baidu TTS**：中文文本转语音
- **阿里云OSS**：文件存储
- **阿里云邮件服务**：邮件通知

### 内部服务集成

- **语言检测**：使用 cld3-asm 进行语言检测
- **音频处理**：处理和管理音频文件
- **视频生成**：生成视频文件

## 部署架构

- **前端**：部署在 Nginx 服务器上
- **后端**：部署在 Node.js 环境中
- **数据库**：MySQL 数据库
- **文件存储**：阿里云 OSS
- **域名**：echolab.club
- **统计分析**：Umami (stats.echolab.club)
