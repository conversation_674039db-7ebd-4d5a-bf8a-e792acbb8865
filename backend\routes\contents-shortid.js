/**
 * 内容API路由 (使用 ShortID)
 * 提供素材内容的保存、加载和管理功能
 */
const express = require("express");
const router = express.Router();
const db = require("../models");
const shortid = require("shortid");

/**
 * 获取所有素材内容
 * GET /api/contents
 */
router.get("/", async (req, res) => {
  try {
    const contents = await db.Content.findAll({
      attributes: [
        "id",
        "name",
        "description",
        "thumbnailUrl",
        "tags",
        "status",
        ["created_at", "createdAt"],
        ["updated_at", "updatedAt"],
      ],
      order: [["created_at", "DESC"]],
    });

    res.json({
      success: true,
      contents,
    });
  } catch (error) {
    console.error("获取素材列表失败:", error);
    res.status(500).json({
      success: false,
      error: `获取素材列表失败: ${error.message}`,
    });
  }
});

/**
 * 获取单个素材内容
 * GET /api/contents/:id
 */
router.get("/:id", async (req, res) => {
  try {
    const content = await db.Content.findByPk(req.params.id);

    if (!content) {
      return res.status(404).json({
        success: false,
        error: "素材不存在",
      });
    }

    res.json({
      success: true,
      content,
    });
  } catch (error) {
    console.error(`获取素材失败 (ID: ${req.params.id}):`, error);
    res.status(500).json({
      success: false,
      error: `获取素材失败: ${error.message}`,
    });
  }
});

/**
 * 创建新素材内容
 * POST /api/contents
 */
router.post("/", async (req, res) => {
  try {
    const { name, description, configJson, thumbnailUrl, userId, tags } =
      req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        error: "缺少必要参数: name",
      });
    }

    if (!configJson) {
      return res.status(400).json({
        success: false,
        error: "缺少必要参数: configJson",
      });
    }

    // 生成ShortID作为ID
    const id = shortid.generate();

    const content = await db.Content.create({
      id,
      name,
      description,
      configJson,
      thumbnailUrl,
      userId,
      tags,
      status: req.body.status || "draft",
    });

    res.status(201).json({
      success: true,
      content,
    });
  } catch (error) {
    console.error("创建素材失败:", error);
    res.status(500).json({
      success: false,
      error: `创建素材失败: ${error.message}`,
    });
  }
});

/**
 * 更新素材内容
 * PUT /api/contents/:id
 */
router.put("/:id", async (req, res) => {
  try {
    const { name, description, configJson, thumbnailUrl, tags } = req.body;

    const content = await db.Content.findByPk(req.params.id);

    if (!content) {
      return res.status(404).json({
        success: false,
        error: "素材不存在",
      });
    }

    // 准备更新数据
    const updateData = {
      name: name || content.name,
      description:
        description !== undefined ? description : content.description,
      configJson: configJson || content.configJson,
      thumbnailUrl:
        thumbnailUrl !== undefined ? thumbnailUrl : content.thumbnailUrl,
      tags: tags !== undefined ? tags : content.tags,
    };

    // 添加status字段
    if (req.body.status) {
      updateData.status = req.body.status;
    }

    // 更新素材
    await content.update(updateData);

    res.json({
      success: true,
      content,
    });
  } catch (error) {
    console.error(`更新素材失败 (ID: ${req.params.id}):`, error);
    res.status(500).json({
      success: false,
      error: `更新素材失败: ${error.message}`,
    });
  }
});

/**
 * 删除素材内容
 * DELETE /api/contents/:id
 */
router.delete("/:id", async (req, res) => {
  try {
    const content = await db.Content.findByPk(req.params.id);

    if (!content) {
      return res.status(404).json({
        success: false,
        error: "素材不存在",
      });
    }

    await content.destroy();

    res.json({
      success: true,
      message: "素材已删除",
    });
  } catch (error) {
    console.error(`删除素材失败 (ID: ${req.params.id}):`, error);
    res.status(500).json({
      success: false,
      error: `删除素材失败: ${error.message}`,
    });
  }
});

module.exports = router;
