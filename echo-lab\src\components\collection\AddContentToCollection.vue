<!--
  添加内容到合集组件
  用于选择和添加内容到指定合集
-->
<template>
  <div class="add-content-to-collection">
    <!-- 搜索和过滤 -->
    <div class="filter-section">
      <el-input v-model="searchQuery" placeholder="搜索内容..." clearable class="search-input" @input="handleSearch">
        <template #prefix>
          <el-icon><i-ep-search /></el-icon>
        </template>
      </el-input>

      <el-select v-model="sortBy" class="filter-item" @change="loadContents">
        <el-option label="更新时间" value="updated_at" />
        <el-option label="创建时间" value="created_at" />
        <el-option label="名称" value="name" />
      </el-select>

      <el-select v-model="sortOrder" class="filter-item" @change="loadContents">
        <el-option label="降序" value="DESC" />
        <el-option label="升序" value="ASC" />
      </el-select>
    </div>



    <!-- 内容列表 -->
    <div class="content-list" v-loading="loading">
      <div v-if="availableContents.length > 0" class="content-grid">
        <div v-for="content in availableContents" :key="content.id" class="content-item"
          :class="{ 'added': content.isAdded }" 
          @click="!content.isAdded && addSingleContent(content)">
          <!-- 状态 -->
          <div class="content-status">
            <el-tag v-if="content.isAdded" type="success" size="small">已添加</el-tag>
          </div>

          <!-- 缩略图 -->
          <div class="content-thumbnail">
            <div v-if="content.thumbnailUrl" class="thumbnail-wrapper">
              <ResponsiveImage :src="content.thumbnailUrl" alt="缩略图" />
            </div>
            <div v-else class="thumbnail-placeholder">
              <el-icon :size="24">
                <i-ep-video-play />
              </el-icon>
            </div>
          </div>

          <!-- 内容信息 -->
          <div class="content-info">
            <h4 class="content-title">{{ content.name }}</h4>
            <p class="content-description">{{ content.description || '暂无描述' }}</p>
            <div class="content-meta">
              <span class="content-date">{{ formatDate(content.updatedAt) }}</span>
              <div class="content-tags" v-if="content.tags">
                <el-tag v-for="tag in parseTags(content.tags)" :key="tag" size="small">
                  {{ tag }}
                </el-tag>
              </div>
            </div>
          </div>


        </div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!loading" class="empty-state">
        <el-empty description="没有可添加的内容" :image-size="100">
          <p>请确保您有已发布的内容，且未添加到当前合集中</p>
        </el-empty>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="pagination.total > 0" class="pagination-section">
      <el-pagination v-model:current-page="pagination.page" v-model:page-size="pagination.limit"
        :total="pagination.total" :page-sizes="[12, 24, 48]" layout="total, sizes, prev, pager, next"
        @size-change="handlePageSizeChange" @current-change="handlePageChange" />
    </div>


  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import contentService from '@/services/contentService';
import httpClient from '@/utils/httpClient';
import { API_ENDPOINTS } from '@/config/api';
import collectionService from '@/services/collectionService';
import { useContentStore } from '@/core/stores/contentStore';
import ResponsiveImage from '@/components/common/ResponsiveImage.vue';

// 简单的防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

const contentStore = useContentStore();

// Props
const props = defineProps({
  collectionId: {
    type: String,
    required: true
  },
  existingContentIds: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits(['content-added', 'close']);

// 响应式数据
const loading = ref(false);

const searchQuery = ref('');
const sortBy = ref('updated_at');
const sortOrder = ref('DESC');

const allContents = ref([]);
const pagination = ref({
  page: 1,
  limit: 12,
  total: 0,
});

// 计算属性 - 所有内容，标记状态
const availableContents = computed(() => {
  return allContents.value.map(content => {
    const isAdded = props.existingContentIds.includes(content.id);
    
    return {
      ...content,
      isAdded
    };
  });
});







// 加载公开内容
const loadContents = async () => {
  loading.value = true;
  try {
    const response = await httpClient.get(API_ENDPOINTS.CONTENTS.PUBLIC, {
      page: pagination.value.page,
      pageSize: pagination.value.limit,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value,
      search: searchQuery.value
    });
    
    if (response.success) {
      allContents.value = response.contents || [];
      pagination.value.total = response.pagination?.total || 0;
    } else {
      throw new Error(response.error || '获取内容列表失败');
    }
  } catch (error) {
    console.error('加载内容失败:', error);
    ElMessage.error(error.message || '加载内容失败');
  } finally {
    loading.value = false;
  }
};

// 添加单个内容
const addSingleContent = (content) => {
  emit('content-added', [content]);
};





// 处理搜索（防抖）
const debouncedSearch = debounce(() => {
  pagination.value.page = 1;
  loadContents();
}, 300);

const handleSearch = () => {
  debouncedSearch();
};

// 处理分页变化
const handlePageChange = (page) => {
  pagination.value.page = page;
  loadContents();
};

const handlePageSizeChange = (size) => {
  pagination.value.limit = size;
  pagination.value.page = 1;
  loadContents();
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN');
};

// 解析标签
const parseTags = (tagsStr) => {
  if (!tagsStr) return [];
  if (Array.isArray(tagsStr)) return tagsStr;
  return tagsStr.split(',').filter(tag => tag.trim()).slice(0, 3);
};







// 初始化
onMounted(() => {
  loadContents();
});
</script>

<style scoped>
.add-content-to-collection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-height: 80vh;
  min-height: 0;
}

.filter-section {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 12.5rem;
  /* 200px ÷ 16 */
}

.filter-item {
  width: 7.5rem;
  /* 120px ÷ 16 */
}



.content-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.content-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.content-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 0.0625rem solid #e4e7ed;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 0;
}

.content-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}



.content-status {
  flex-shrink: 0;
  min-width: 4rem;
}

.content-thumbnail {
  width: 4rem;
  height: 3rem;
  border-radius: 0.25rem;
  overflow: hidden;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.thumbnail-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.thumbnail-placeholder {
  color: #909399;
}

.content-info {
  flex: 1;
  min-width: 0;
}

.content-title {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-description {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.content-date {
  font-size: 0.75rem;
  color: #909399;
}

.content-tags {
  display: flex;
  gap: 0.25rem;
}

.quick-action {
  flex-shrink: 0;
}

.empty-state {
  padding: 3rem 1rem;
  text-align: center;
}

.pagination-section {
  display: flex;
  justify-content: center;
  flex-shrink: 0;
}

.bottom-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-top: 0.0625rem solid #e4e7ed;
  border-radius: 0 0 0.5rem 0.5rem;
  flex-shrink: 0;
}

.changes-info {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #606266;
}

.toolbar-buttons {
  display: flex;
  gap: 0.75rem;
}

.content-item.added {
  background: #f0f9ff;
  border-color: #b3d8ff;
}

.content-item.added:hover {
  background: #e6f7ff;
}

/* 使用rem单位，移除媒体查询 */
</style>
