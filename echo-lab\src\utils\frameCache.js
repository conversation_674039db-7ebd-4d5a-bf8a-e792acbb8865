/**
 * 帧图片缓存系统
 * 基于内容MD5缓存重复的帧图片，提高视频导出效率
 */

import { md5 } from "./md5.js";

/**
 * 帧缓存管理器
 */
export class FrameCache {
  constructor() {
    // 内存缓存 - 存储Canvas对象
    this.memoryCache = new Map();

    // 缓存统计
    this.stats = {
      hits: 0,
      misses: 0,
      totalFrames: 0,
      cacheSize: 0,
    };

    // 最大缓存大小（防止内存溢出）
    this.maxCacheSize = 100; // 最多缓存100个不同的帧
  }

  /**
   * 生成内容的缓存键
   * @param {Object} item - 时间线项目
   * @param {Object} options - 渲染选项
   * @returns {string} MD5哈希值
   */
  generateCacheKey(item, options = {}) {
    // 从item中获取渲染选项（如果存在）
    const renderOptions = item._renderOptions || {};

    // 构建影响帧内容的所有因素
    const cacheData = {
      // 内容相关
      content: item.content || "",
      annotation: item.annotation || "",
      language: item.language || "",
      contentType: item.contentType || item.type || "text",
      imageUrl: item.imageUrl || "",

      // 样式相关 - 优先使用item中的渲染选项
      fontSize: renderOptions.fontSize || options.fontSize || "",
      textColor: renderOptions.textColor || options.textColor || "",
      backgroundColor:
        renderOptions.backgroundColor || options.backgroundColor || "",
      textShadow: renderOptions.textShadow || options.textShadow || "",

      // 尺寸相关
      width: options.width || 0,
      height: options.height || 0,
      containerWidth: options.containerWidth || 0,
      containerHeight: options.containerHeight || 0,

      // 版权信息 - 优先使用item中的版权信息
      copyright:
        renderOptions.copyright || options.copyright
          ? {
              text: (renderOptions.copyright || options.copyright).text || "",
              position:
                (renderOptions.copyright || options.copyright).position || "",
            }
          : null,
    };

    // 生成MD5哈希
    const cacheKey = md5(JSON.stringify(cacheData));

    console.log(
      `生成缓存键: ${cacheKey.substring(0, 8)}... 内容: "${(
        item.content || ""
      ).substring(0, 20)}..."`
    );

    return cacheKey;
  }

  /**
   * 获取缓存的帧
   * @param {string} cacheKey - 缓存键
   * @returns {Canvas|null} 缓存的Canvas对象
   */
  get(cacheKey) {
    this.stats.totalFrames++;

    if (this.memoryCache.has(cacheKey)) {
      this.stats.hits++;
      const cachedCanvas = this.memoryCache.get(cacheKey);

      // 只在每10次命中时输出日志
      if (this.stats.hits % 10 === 0) {
        console.log(`缓存命中率: ${((this.stats.hits / this.stats.totalFrames) * 100).toFixed(1)}%`);
      }

      // 克隆Canvas以避免修改原始缓存
      return this.cloneCanvas(cachedCanvas);
    }

    this.stats.misses++;
    // 减少未命中日志输出

    return null;
  }

  /**
   * 设置缓存
   * @param {string} cacheKey - 缓存键
   * @param {Canvas} canvas - Canvas对象
   */
  set(cacheKey, canvas) {
    // 检查缓存大小限制
    if (this.memoryCache.size >= this.maxCacheSize) {
      // 删除最旧的缓存项（LRU策略）
      const firstKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(firstKey);
      console.log(`缓存已满，删除最旧项: ${firstKey.substring(0, 8)}...`);
    }

    // 克隆Canvas以避免外部修改影响缓存
    const clonedCanvas = this.cloneCanvas(canvas);
    this.memoryCache.set(cacheKey, clonedCanvas);
    this.stats.cacheSize = this.memoryCache.size;

    // 减少缓存日志输出
  }

  /**
   * 克隆Canvas对象
   * @param {Canvas} originalCanvas - 原始Canvas
   * @returns {Canvas} 克隆的Canvas
   */
  cloneCanvas(originalCanvas) {
    const clonedCanvas = document.createElement("canvas");
    clonedCanvas.width = originalCanvas.width;
    clonedCanvas.height = originalCanvas.height;

    const ctx = clonedCanvas.getContext("2d");
    ctx.drawImage(originalCanvas, 0, 0);

    return clonedCanvas;
  }

  /**
   * 清空缓存
   */
  clear() {
    this.memoryCache.clear();
    this.stats = {
      hits: 0,
      misses: 0,
      totalFrames: 0,
      cacheSize: 0,
    };
    console.log("帧缓存已清空");
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const hitRate =
      this.stats.totalFrames > 0
        ? ((this.stats.hits / this.stats.totalFrames) * 100).toFixed(1)
        : 0;

    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      memoryUsage: `${this.stats.cacheSize}/${this.maxCacheSize}`,
    };
  }

  /**
   * 打印缓存统计信息
   */
  printStats() {
    const stats = this.getStats();
    console.log("=== 帧缓存统计 ===");
    console.log(`总帧数: ${stats.totalFrames}`);
    console.log(`缓存命中: ${stats.hits}`);
    console.log(`缓存未命中: ${stats.misses}`);
    console.log(`命中率: ${stats.hitRate}`);
    console.log(`内存使用: ${stats.memoryUsage}`);
    console.log("==================");
  }
}

// 创建全局缓存实例
export const frameCache = new FrameCache();

/**
 * 带缓存的帧渲染函数
 * @param {Object} item - 时间线项目
 * @param {Function} renderFunction - 原始渲染函数
 * @param {Object} options - 渲染选项
 * @returns {Promise<Canvas>} 渲染的Canvas
 */
export async function renderFrameWithCache(item, renderFunction, options = {}) {
  // 生成缓存键
  const cacheKey = frameCache.generateCacheKey(item, options);

  // 尝试从缓存获取
  const cachedFrame = frameCache.get(cacheKey);
  if (cachedFrame) {
    return cachedFrame;
  }

  // 缓存未命中，执行实际渲染
  const renderedFrame = await renderFunction(item);

  // 将结果存入缓存
  frameCache.set(cacheKey, renderedFrame);

  return renderedFrame;
}

/**
 * 在导出开始时重置缓存统计
 */
export function resetCacheStats() {
  frameCache.stats = {
    hits: 0,
    misses: 0,
    totalFrames: 0,
    cacheSize: frameCache.memoryCache.size,
  };
  console.log("帧缓存统计已重置");
}

/**
 * 在导出结束时打印缓存统计
 */
export function printFinalCacheStats() {
  frameCache.printStats();
}
