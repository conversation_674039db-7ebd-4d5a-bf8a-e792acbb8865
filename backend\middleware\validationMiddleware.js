/**
 * 输入验证中间件
 * 使用Joi验证请求参数，防止恶意输入和注入攻击
 */
const Joi = require('joi');

/**
 * 创建验证中间件
 * @param {Object} schema Joi验证模式
 * @param {string} property 要验证的请求属性（body, params, query）
 * @returns {Function} Express中间件
 */
function validate(schema, property = 'body') {
  return (req, res, next) => {
    const data = req[property];
    const { error, value } = schema.validate(data, {
      abortEarly: false, // 返回所有错误
      stripUnknown: true, // 删除未知字段
    });

    if (error) {
      // 格式化错误信息
      const errorDetails = error.details.map((detail) => ({
        message: detail.message,
        path: detail.path,
        type: detail.type,
      }));

      return res.status(400).json({
        success: false,
        error: '请求参数验证失败',
        details: errorDetails,
        code: 'VALIDATION_ERROR',
      });
    }

    // 用验证后的值替换原始值
    req[property] = value;
    next();
  };
}

// 常用验证模式
const schemas = {
  // 用户认证相关
  auth: {
    // 发送验证码
    sendCode: Joi.object({
      email: Joi.string().email().required().messages({
        'string.email': '请输入有效的邮箱地址',
        'any.required': '邮箱地址不能为空',
      }),
      type: Joi.string().valid('login', 'register', 'reset_password').default('login').messages({
        'any.only': '验证码类型无效',
      }),
    }),

    // 验证验证码
    verifyCode: Joi.object({
      email: Joi.string().email().required().messages({
        'string.email': '请输入有效的邮箱地址',
        'any.required': '邮箱地址不能为空',
      }),
      code: Joi.string().length(6).pattern(/^\d+$/).required().messages({
        'string.length': '验证码必须是6位数字',
        'string.pattern.base': '验证码必须是6位数字',
        'any.required': '验证码不能为空',
      }),
      type: Joi.string().valid('login', 'register', 'reset_password').default('login').messages({
        'any.only': '验证码类型无效',
      }),
    }),

    // 更新用户信息
    updateUser: Joi.object({
      username: Joi.string().min(2).max(30).messages({
        'string.min': '用户名至少需要2个字符',
        'string.max': '用户名最多30个字符',
      }),
      settings: Joi.object().messages({
        'object.base': '用户设置必须是一个对象',
      }),
    }),
  },

  // TTS相关
  tts: {
    // 批量生成TTS
    batch: Joi.object({
      items: Joi.array().items(
        Joi.object({
          text: Joi.string().required().messages({
            'any.required': '文本内容不能为空',
          }),
          language: Joi.string().default('auto'),
          speed: Joi.number().min(0.5).max(2).default(1).messages({
            'number.min': '语速最小为0.5',
            'number.max': '语速最大为2',
          }),
          speaker: Joi.string(),
          voiceId: Joi.number().integer(),
        })
      ).min(1).required().messages({
        'array.min': '至少需要一个音频项',
        'any.required': '音频项数组不能为空',
      }),
      ignoreCache: Joi.boolean().default(false),
    }),
  },

  // 内容相关
  content: {
    // 创建内容
    create: Joi.object({
      name: Joi.string().min(1).max(100).required().messages({
        'string.min': '内容名称不能为空',
        'string.max': '内容名称最多100个字符',
        'any.required': '内容名称不能为空',
      }),
      description: Joi.string().allow('').max(500).messages({
        'string.max': '内容描述最多500个字符',
      }),
      tags: Joi.array().items(Joi.string()).default([]),
      status: Joi.string().valid('draft', 'published', 'archived').default('draft').messages({
        'any.only': '内容状态无效',
      }),
      config: Joi.object().required().messages({
        'any.required': '内容配置不能为空',
      }),
    }),

    // 更新内容
    update: Joi.object({
      name: Joi.string().min(1).max(100).messages({
        'string.min': '内容名称不能为空',
        'string.max': '内容名称最多100个字符',
      }),
      description: Joi.string().allow('').max(500).messages({
        'string.max': '内容描述最多500个字符',
      }),
      tags: Joi.array().items(Joi.string()),
      status: Joi.string().valid('draft', 'published', 'archived').messages({
        'any.only': '内容状态无效',
      }),
      config: Joi.object(),
      thumbnailUrl: Joi.string().uri().allow('').messages({
        'string.uri': '缩略图URL格式无效',
      }),
    }),
  },
};

module.exports = {
  validate,
  schemas,
};
