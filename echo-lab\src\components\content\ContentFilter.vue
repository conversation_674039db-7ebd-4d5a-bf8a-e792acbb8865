<template>
  <div class="content-filter">
    <div class="filter-row">
      <!-- 等级过滤 -->
      <div class="filter-item">
        <el-select
          v-model="localFilters.tags"
          placeholder="选择等级"
          multiple
          clearable
          @change="handleFilterChange"
          class="tag-select"
        >
          <el-option label="N5" value="N5" />
          <el-option label="N4" value="N4" />
          <el-option label="N3" value="N3" />
          <el-option label="N2" value="N2" />
          <el-option label="N1" value="N1" />
          <el-option label="N1" value="N1" />
        </el-select>
      </div>



    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

// 简单的防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

const props = defineProps({
  filters: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:filters', 'clear']);

// 本地过滤器状态
const localFilters = ref({ 
  tags: props.filters.tags || []
});





// 过滤器变化处理
const handleFilterChange = () => {
  emit('update:filters', { ...localFilters.value });
};









// 监听外部过滤器变化
watch(() => props.filters, (newFilters) => {
  localFilters.value = {
    tags: newFilters.tags || []
  };
}, { deep: true });
</script>

<style scoped>
.content-filter {
  background: transparent;
  padding: 0;
  margin: 0;
}

.filter-row {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  flex-wrap: wrap;
}

.filter-item {
  flex-shrink: 0;
}

.search-item {
  flex: 1;
  min-width: 12rem;
}

.search-input {
  max-width: 20rem;
}

.tag-select {
  min-width: 8rem;
}

.sort-select {
  min-width: 6rem;
}

.sort-order-group {
  display: flex;
}



/* 移动端适配 */
@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-item {
    width: 100%;
  }
}
</style>