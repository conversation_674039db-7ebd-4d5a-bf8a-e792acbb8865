# 音频切割功能升级方案

## 🎯 当前状况

目前的音频切割功能使用自定义Canvas实现，虽然基本功能完整，但在专业性和用户体验方面还有提升空间。

## 🚀 推荐升级方案

### **方案一：WaveSurfer.js (强烈推荐)**

**WaveSurfer.js** 是业界最成熟的音频波形图库，被众多专业音频应用使用。

#### **优势**：
- ✅ **专业级波形图**：高质量的音频可视化
- ✅ **丰富的交互**：拖拽、缩放、区域选择
- ✅ **插件生态**：regions, timeline, cursor, minimap等
- ✅ **性能优异**：支持大文件和长音频
- ✅ **移动端支持**：响应式设计，触摸友好
- ✅ **活跃维护**：持续更新，社区活跃

#### **安装步骤**：

```bash
# 1. 安装核心库
npm install wavesurfer.js

# 2. 安装插件（可选）
npm install wavesurfer.js/dist/plugins/regions.js
npm install wavesurfer.js/dist/plugins/timeline.js
npm install wavesurfer.js/dist/plugins/cursor.js
```

#### **基础使用示例**：

```javascript
import WaveSurfer from 'wavesurfer.js'
import RegionsPlugin from 'wavesurfer.js/dist/plugins/regions.js'
import TimelinePlugin from 'wavesurfer.js/dist/plugins/timeline.js'

// 创建 WaveSurfer 实例
const wavesurfer = WaveSurfer.create({
  container: '#waveform',
  waveColor: '#409eff',
  progressColor: '#67c23a',
  cursorColor: '#f56c6c',
  barWidth: 2,
  barRadius: 3,
  responsive: true,
  height: 200,
  plugins: [
    RegionsPlugin.create({
      dragSelection: {
        slop: 5
      }
    }),
    TimelinePlugin.create()
  ]
})

// 加载音频
wavesurfer.loadBlob(audioBlob)

// 监听事件
wavesurfer.on('ready', () => {
  console.log('音频加载完成')
})

wavesurfer.on('region-created', (region) => {
  console.log('创建区域:', region)
})
```

### **方案二：@wavesurfer/vue**

WaveSurfer.js 官方的 Vue 3 组件封装。

```bash
npm install @wavesurfer/vue
```

```vue
<template>
  <WaveSurfer
    :url="audioUrl"
    :options="wavesurferOptions"
    @ready="onReady"
    @region-created="onRegionCreated"
  />
</template>

<script setup>
import WaveSurfer from '@wavesurfer/vue'

const wavesurferOptions = {
  height: 200,
  waveColor: '#409eff',
  progressColor: '#67c23a'
}
</script>
```

### **方案三：vue-wavesurfer**

社区维护的 Vue 组件封装。

```bash
npm install vue-wavesurfer
```

## 🔧 实施建议

### **阶段一：并行开发**
1. 保留现有的自定义实现
2. 创建基于 WaveSurfer.js 的新组件
3. 在设置中提供切换选项

### **阶段二：功能对比**
1. 用户体验测试
2. 性能对比测试
3. 功能完整性验证

### **阶段三：平滑迁移**
1. 默认使用 WaveSurfer.js 版本
2. 保留自定义版本作为备选
3. 收集用户反馈

## 📋 功能对比

| 功能 | 自定义实现 | WaveSurfer.js |
|------|------------|---------------|
| 波形图质量 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 交互体验 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 性能表现 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 移动端支持 | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| 开发维护 | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| 自定义程度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

## 🎨 WaveSurfer.js 特色功能

### **1. 区域选择 (Regions)**
- 拖拽选择音频区域
- 区域颜色自定义
- 区域标签和元数据
- 区域播放和编辑

### **2. 时间轴 (Timeline)**
- 精确的时间刻度显示
- 自适应时间间隔
- 可自定义样式

### **3. 光标 (Cursor)**
- 实时播放位置指示
- 点击定位播放
- 自定义光标样式

### **4. 缩放和导航**
- 鼠标滚轮缩放
- 拖拽平移
- 缩放级别控制

### **5. 响应式设计**
- 自适应容器大小
- 移动端触摸支持
- 高DPI屏幕优化

## 💡 实施代码示例

### **完整的音频切割组件**

```vue
<template>
  <div class="audio-cutting-wavesurfer">
    <div ref="waveformRef" class="waveform-container"></div>
    
    <div class="controls">
      <el-button @click="playPause">
        {{ isPlaying ? '暂停' : '播放' }}
      </el-button>
      <el-button @click="addRegion">添加区域</el-button>
      <el-button @click="clearRegions">清除区域</el-button>
      <el-button @click="exportRegions">导出片段</el-button>
    </div>
    
    <div class="regions-list">
      <div v-for="region in regions" :key="region.id" class="region-item">
        <span>{{ formatTime(region.start) }} - {{ formatTime(region.end) }}</span>
        <el-button size="small" @click="playRegion(region)">播放</el-button>
        <el-button size="small" type="danger" @click="removeRegion(region)">删除</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import WaveSurfer from 'wavesurfer.js'
import RegionsPlugin from 'wavesurfer.js/dist/plugins/regions.js'
import TimelinePlugin from 'wavesurfer.js/dist/plugins/timeline.js'

const waveformRef = ref(null)
const isPlaying = ref(false)
const regions = ref([])

let wavesurfer = null
let regionsPlugin = null

onMounted(() => {
  initWaveSurfer()
})

onUnmounted(() => {
  if (wavesurfer) {
    wavesurfer.destroy()
  }
})

function initWaveSurfer() {
  regionsPlugin = RegionsPlugin.create({
    dragSelection: {
      slop: 5
    }
  })
  
  wavesurfer = WaveSurfer.create({
    container: waveformRef.value,
    waveColor: '#409eff',
    progressColor: '#67c23a',
    cursorColor: '#f56c6c',
    barWidth: 2,
    barRadius: 3,
    responsive: true,
    height: 200,
    plugins: [
      regionsPlugin,
      TimelinePlugin.create({
        height: 20,
        insertPosition: 'beforebegin'
      })
    ]
  })
  
  // 事件监听
  wavesurfer.on('play', () => { isPlaying.value = true })
  wavesurfer.on('pause', () => { isPlaying.value = false })
  
  regionsPlugin.on('region-created', (region) => {
    regions.value.push(region)
  })
  
  regionsPlugin.on('region-removed', (region) => {
    const index = regions.value.findIndex(r => r.id === region.id)
    if (index > -1) {
      regions.value.splice(index, 1)
    }
  })
}

function playPause() {
  wavesurfer.playPause()
}

function addRegion() {
  const duration = wavesurfer.getDuration()
  const start = Math.random() * duration * 0.8
  const end = start + Math.random() * duration * 0.2
  
  regionsPlugin.addRegion({
    start,
    end,
    color: 'rgba(255, 0, 0, 0.3)',
    drag: true,
    resize: true
  })
}

function clearRegions() {
  regionsPlugin.clearRegions()
  regions.value = []
}

function playRegion(region) {
  region.play()
}

function removeRegion(region) {
  region.remove()
}

async function exportRegions() {
  // 导出音频片段的逻辑
  for (const region of regions.value) {
    const audioBuffer = await wavesurfer.getDecodedData()
    const regionBuffer = extractRegionBuffer(audioBuffer, region.start, region.end)
    const blob = audioBufferToWav(regionBuffer)
    // 上传 blob...
  }
}

function formatTime(seconds) {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}
</script>
```

## 🎯 总结

**强烈建议升级到 WaveSurfer.js**，理由：

1. **专业性**：业界标准的音频处理库
2. **用户体验**：更流畅的交互和更美观的界面
3. **维护成本**：减少自定义代码的维护负担
4. **功能丰富**：开箱即用的专业功能
5. **社区支持**：活跃的社区和丰富的文档

升级后，音频切割功能将达到专业音频编辑软件的水准！
