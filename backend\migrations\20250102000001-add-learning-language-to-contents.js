"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 添加学习语言字段
    await queryInterface.addColumn("contents", "learning_language", {
      type: Sequelize.STRING(10),
      allowNull: false,
      defaultValue: "ja", // 默认为日语
      comment: "内容的目标学习语言，如ja、en、zh-CN等",
      after: "tags", // 在tags字段后面添加
    });

    // 添加索引以提高查询性能
    await queryInterface.addIndex("contents", ["learning_language"], {
      name: "idx_contents_learning_language",
    });

    // 将所有现有内容的学习语言设置为日语
    await queryInterface.sequelize.query(
      "UPDATE contents SET learning_language = 'ja' WHERE learning_language IS NULL OR learning_language = ''"
    );
  },

  async down(queryInterface, Sequelize) {
    // 删除索引
    await queryInterface.removeIndex("contents", "idx_contents_learning_language");
    
    // 删除字段
    await queryInterface.removeColumn("contents", "learning_language");
  },
};
