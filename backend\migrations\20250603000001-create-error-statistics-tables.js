'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 创建错误日志记录表
    await queryInterface.createTable('error_logs', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      error_hash: {
        type: Sequelize.STRING(32),
        allowNull: false,
        comment: '错误唯一标识(基于message+stack的MD5)'
      },
      error_type: {
        type: Sequelize.ENUM('javascript', 'vue', 'network', 'resource', 'promise'),
        allowNull: false,
        comment: '错误类型'
      },
      message: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: '错误消息'
      },
      stack: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '错误堆栈'
      },
      url: {
        type: Sequelize.STRING(500),
        allowNull: false,
        comment: '发生错误的页面URL'
      },
      line_number: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '错误行号'
      },
      column_number: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '错误列号'
      },
      filename: {
        type: Sequelize.STRING(500),
        allowNull: true,
        comment: '错误文件名'
      },
      user_agent: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '用户代理字符串'
      },
      user_id: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '用户ID（如果已登录）'
      },
      session_id: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: '会话ID'
      },
      ip_address: {
        type: Sequelize.STRING(45),
        allowNull: true,
        comment: 'IP地址'
      },
      browser_info: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '浏览器信息'
      },
      device_info: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '设备信息'
      },
      context_data: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '错误上下文数据'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // 创建错误汇总统计表
    await queryInterface.createTable('error_summaries', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      error_hash: {
        type: Sequelize.STRING(32),
        allowNull: false,
        unique: true,
        comment: '错误唯一标识'
      },
      error_type: {
        type: Sequelize.ENUM('javascript', 'vue', 'network', 'resource', 'promise'),
        allowNull: false
      },
      message: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: '错误消息'
      },
      stack_sample: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '错误堆栈示例'
      },
      first_seen: {
        type: Sequelize.DATE,
        allowNull: false,
        comment: '首次发现时间'
      },
      last_seen: {
        type: Sequelize.DATE,
        allowNull: false,
        comment: '最后发现时间'
      },
      occurrence_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
        comment: '发生次数'
      },
      affected_users: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '影响用户数'
      },
      status: {
        type: Sequelize.ENUM('new', 'acknowledged', 'resolved', 'ignored'),
        allowNull: false,
        defaultValue: 'new',
        comment: '处理状态'
      },
      priority: {
        type: Sequelize.ENUM('low', 'medium', 'high', 'critical'),
        allowNull: false,
        defaultValue: 'medium',
        comment: '优先级'
      },
      assigned_to: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '分配给谁处理'
      },
      resolution_notes: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '解决方案备注'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 创建错误按日统计表
    await queryInterface.createTable('error_statistics', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
        comment: '统计日期'
      },
      error_hash: {
        type: Sequelize.STRING(32),
        allowNull: false,
        comment: '错误标识'
      },
      error_type: {
        type: Sequelize.ENUM('javascript', 'vue', 'network', 'resource', 'promise'),
        allowNull: false
      },
      occurrence_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '当日发生次数'
      },
      affected_users: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '当日影响用户数'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // 创建索引
    await queryInterface.addIndex('error_logs', ['error_hash'], { name: 'idx_error_hash' });
    await queryInterface.addIndex('error_logs', ['error_type'], { name: 'idx_error_type' });
    await queryInterface.addIndex('error_logs', ['url'], { name: 'idx_url' });
    await queryInterface.addIndex('error_logs', ['user_id'], { name: 'idx_user_id' });
    await queryInterface.addIndex('error_logs', ['created_at'], { name: 'idx_created_at' });

    await queryInterface.addIndex('error_summaries', ['error_type'], { name: 'idx_summary_error_type' });
    await queryInterface.addIndex('error_summaries', ['status'], { name: 'idx_summary_status' });
    await queryInterface.addIndex('error_summaries', ['priority'], { name: 'idx_summary_priority' });
    await queryInterface.addIndex('error_summaries', ['first_seen'], { name: 'idx_summary_first_seen' });
    await queryInterface.addIndex('error_summaries', ['last_seen'], { name: 'idx_summary_last_seen' });

    await queryInterface.addIndex('error_statistics', ['date', 'error_hash'], { 
      name: 'idx_date_hash', 
      unique: true 
    });
    await queryInterface.addIndex('error_statistics', ['date'], { name: 'idx_stats_date' });
    await queryInterface.addIndex('error_statistics', ['error_type'], { name: 'idx_stats_error_type' });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('error_statistics');
    await queryInterface.dropTable('error_summaries');
    await queryInterface.dropTable('error_logs');
  }
};
