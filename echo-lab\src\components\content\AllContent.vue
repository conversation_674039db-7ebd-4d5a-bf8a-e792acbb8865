<!--
  全部内容组件
  显示所有内容并提供完整的过滤功能
-->
<template>
  <div class="all-content">
    <!-- 内容标题和过滤器 -->
    <div v-if="!hideHeader" class="content-header">
      <h2 class="content-title">
        <el-icon class="title-icon">
          <i-ep-document />
        </el-icon>
        全部内容
      </h2>
      <ContentFilter
        v-if="!hideFilter"
        :filters="filters"
        @update:filters="$emit('update-filters', $event)"
        @clear="$emit('clear-filters')"
      />
    </div>

    <!-- 内容网格 -->
    <div class="content-grid" :class="{ 'mobile-layout': isMobile }">
      <!-- 加载状态 -->
      <div v-if="loading && contents.length === 0" class="grid-loading">
        <el-skeleton :rows="2" animated />
        <el-skeleton :rows="2" animated />
      </div>

      <!-- 内容为空 -->
      <div v-else-if="contents.length === 0" class="grid-empty">
        <el-empty description="暂无内容" :image-size="100" />
      </div>

      <!-- 内容网格 -->
      <div v-else class="grid-container">
        <PublicContentCard 
          v-for="content in contents" 
          :key="content.id" 
          :content="content"
          class="grid-item"
        />
      </div>

      <!-- 加载更多 -->
      <div v-if="hasMore" class="load-more-section">
        <el-button 
          type="primary" 
          size="large" 
          :loading="loading" 
          @click="$emit('load-more')"
          class="load-more-btn"
        >
          {{ loading ? '加载中...' : '加载更多' }}
        </el-button>
      </div>

      <!-- 内容统计信息 -->
      <div v-if="total > 0" class="content-stats">
        <span class="stats-text">
          已显示 {{ contents.length }} / {{ total }} 个内容
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { isMobileDevice } from '@/utils/deviceDetector';
import PublicContentCard from '@/components/content/PublicContentCard.vue';
import ContentFilter from '@/components/content/ContentFilter.vue';

const props = defineProps({
  contents: { type: Array, default: () => [] },
  loading: Boolean,
  hasMore: Boolean,
  total: { type: Number, default: 0 },
  filters: { type: Object, default: () => ({}) },
  hideHeader: { type: Boolean, default: false },
  hideFilter: { type: Boolean, default: false }
});

defineEmits(['update-filters', 'clear-filters', 'load-more']);

const isMobile = computed(() => isMobileDevice());
</script>

<style scoped>
.all-content {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.06);
  border: 0.0625rem solid #f0f2f5;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 0.125rem solid #f0f2f5;
}

.content-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.title-icon {
  font-size: 1.125rem;
  color: #409eff;
}

.content-grid {
  width: 100%;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(18rem, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.grid-item {
  height: 100%;
}

.grid-loading {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(18rem, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.grid-empty {
  padding: 3rem 0;
  text-align: center;
}

.load-more-section {
  margin-top: 2rem;
  display: flex;
  justify-content: center;
}

.load-more-btn {
  min-width: 8rem;
  border-radius: 2rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.load-more-btn:hover {
  transform: translateY(-0.125rem);
  box-shadow: 0 0.5rem 1rem rgba(64, 158, 255, 0.3);
}

.content-stats {
  margin-top: 1rem;
  text-align: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 0.5rem;
}

.stats-text {
  font-size: 0.875rem;
  color: #606266;
}

/* 移动端优化 */
.mobile-layout .grid-container {
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.mobile-layout .grid-loading {
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.mobile-layout .grid-empty {
  padding: 2rem 0;
}

.mobile-layout .load-more-section {
  margin-top: 1.5rem;
}

.mobile-layout .load-more-btn {
  min-width: 7rem;
  font-size: 0.875rem;
}

.mobile-layout .content-stats {
  margin-top: 0.75rem;
  padding: 0.5rem;
}

.mobile-layout .stats-text {
  font-size: 0.75rem;
}

.mobile-layout .all-content {
  padding: 0.75rem;
  border-radius: 0.5rem;
}

.mobile-layout .content-header {
  margin-bottom: 0.75rem;
  padding-bottom: 0.375rem;
  flex-direction: column;
  align-items: stretch;
  gap: 0.75rem;
}

.mobile-layout .content-title {
  font-size: 1.125rem;
}
</style>