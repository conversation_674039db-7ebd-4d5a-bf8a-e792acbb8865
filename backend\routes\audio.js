/**
 * 音频管理路由
 * 处理音频记录的创建、更新、查询和删除
 * 简化版本：只支持 audio_source 字段
 */
const express = require("express");
const router = express.Router();
const multer = require("multer");
const { nanoid } = require("nanoid");
const db = require("../models");
const { Op } = require("sequelize");
const ossService = require("../services/ossService");
const audioSpeedService = require("../services/audioSpeedService");
const { pipeline } = require("stream");
const { promisify } = require("util");
const axios = require("axios");

// 异步 pipeline
const pipelineAsync = promisify(pipeline);

// 配置multer用于处理音频文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 限制50MB
  },
  fileFilter: (req, file, cb) => {
    // 检查文件类型
    const allowedMimes = [
      "audio/mpeg",
      "audio/wav",
      "audio/mp3",
      "audio/mp4",
      "audio/m4a",
      "audio/webm",
      "audio/ogg",
    ];

    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error("不支持的音频文件格式"), false);
    }
  },
});

/**
 * 合并上传和保存音频记录（用于音频切割）
 * POST /api/audio/upload-and-save
 */
router.post(
  "/upload-and-save",
  upload.fields([
    { name: "audioFiles", maxCount: 50 },
    { name: "audioMetadata", maxCount: 1 },
  ]),
  async (req, res) => {
    try {
      const audioFiles = req.files?.audioFiles || [];
      const { audioMetadata } = req.body;

      if (!audioFiles || audioFiles.length === 0) {
        return res.status(400).json({
          success: false,
          error: "未提供音频文件",
        });
      }

      if (!audioMetadata) {
        return res.status(400).json({
          success: false,
          error: "缺少音频元数据",
        });
      }

      // 解析JSON字符串（FormData会将对象转为字符串）
      let parsedMetadata;
      try {
        parsedMetadata =
          typeof audioMetadata === "string"
            ? JSON.parse(audioMetadata)
            : audioMetadata;
      } catch (parseError) {
        return res.status(400).json({
          success: false,
          error: "音频元数据格式错误",
        });
      }

      if (audioFiles.length !== parsedMetadata.length) {
        return res.status(400).json({
          success: false,
          error: "音频文件数量与元数据数量不匹配",
        });
      }

      const results = [];
      const errors = [];

      // 使用事务确保数据一致性
      const transaction = await db.sequelize.transaction();

      try {
        for (let i = 0; i < audioFiles.length; i++) {
          const file = audioFiles[i];
          const metadata = parsedMetadata[i];

          try {
            // 验证必需字段
            const {
              textSegmentId,
              text,
              language,
              speed = 1.0,
              voice_db_id,
              duration,
              audioSource = "manual",
            } = metadata;

            if (
              !textSegmentId ||
              !text ||
              !language ||
              !voice_db_id ||
              duration === undefined
            ) {
              throw new Error(`文件 ${i + 1}: 缺少必需的元数据字段`);
            }

            // 生成唯一ID
            const audioId = `audio_manual_${nanoid()}`;

            // 从MIME类型获取文件扩展名
            const mimeToExt = {
              "audio/mpeg": "mp3",
              "audio/wav": "wav",
              "audio/mp3": "mp3",
              "audio/mp4": "mp4",
              "audio/m4a": "m4a",
              "audio/webm": "webm",
              "audio/ogg": "ogg",
            };

            const format = mimeToExt[file.mimetype] || "wav";
            const ossPath = `audio/manual/${audioId}.${format}`;

            // 上传到OSS
            const ossResult = await ossService.put(ossPath, file.buffer, {
              mime: file.mimetype,
            });

            // 计算MD5哈希
            const md5Hash = generateMd5Hash(text, language, speed, voice_db_id);

            // 保存到数据库
            const audioRecord = await db.Audio.create(
              {
                text,
                language,
                speed,
                voice_db_id,
                ossUrl: ossResult.url,
                ossKey: ossPath,
                duration,
                audioSource,
                md5Hash,
              },
              { transaction }
            );

            results.push({
              success: true,
              textSegmentId,
              audio: {
                id: audioRecord.id,
                url: ossResult.url,
                duration: audioRecord.duration,
                audioSource: audioRecord.audioSource,
              },
            });
          } catch (itemError) {
            console.error(`处理音频文件 ${i + 1} 失败:`, itemError);
            errors.push({
              index: i,
              textSegmentId: metadata?.textSegmentId,
              error: itemError.message,
            });
            results.push({
              success: false,
              textSegmentId: metadata?.textSegmentId,
              error: itemError.message,
            });
          }
        }

        // 提交事务
        await transaction.commit();

        res.json({
          success: true,
          results,
          errors: errors.length > 0 ? errors : undefined,
          summary: {
            total: audioFiles.length,
            successful: results.filter((r) => r.success).length,
            failed: errors.length,
          },
        });
      } catch (transactionError) {
        // 回滚事务
        await transaction.rollback();
        throw transactionError;
      }
    } catch (error) {
      console.error("合并上传和保存失败:", error);
      res.status(500).json({
        success: false,
        error: "合并上传和保存失败: " + error.message,
      });
    }
  }
);

/**
 * 音频倍速处理API
 * GET /api/audio/speed?path=audio/ja/xxx.mp3&speed=1.2
 */
router.get("/speed", async (req, res) => {
  const { path: audioPath, speed } = req.query;

  try {
    // 参数验证
    if (!audioPath || !speed) {
      return res.status(400).json({
        success: false,
        error: "缺少必需参数: path 和 speed",
      });
    }

    const speedFloat = parseFloat(speed);
    if (isNaN(speedFloat) || speedFloat < 0.5 || speedFloat > 2.0) {
      return res.status(400).json({
        success: false,
        error: "speed 参数必须在 0.5 到 2.0 之间",
      });
    }

    // 如果是原速，直接代理到原音频
    if (Math.abs(speedFloat - 1.0) < 0.01) {
      const originalUrl = `https://echolab.oss-cn-hongkong.aliyuncs.com/${audioPath}`;
      try {
        const response = await axios.get(originalUrl, {
          responseType: "stream",
          timeout: 30000,
        });
        if (response.status === 200) {
          res.setHeader(
            "Content-Type",
            response.headers["content-type"] || "audio/mpeg"
          );
          if (response.headers["content-length"]) {
            res.setHeader("Content-Length", response.headers["content-length"]);
          }
          await pipelineAsync(response.data, res);
          return;
        }
      } catch (proxyError) {
        console.error("代理原音频失败:", proxyError);
      }
    }

    console.log(`处理音频倍速请求: ${audioPath}, 倍速: ${speedFloat}x`);

    // 1. 检查缓存
    if (audioSpeedService.hasCachedFile(audioPath, speedFloat)) {
      console.log(`返回缓存的倍速音频: ${audioPath}_${speedFloat}x`);
      const cachedStream = audioSpeedService.getCachedFileStream(
        audioPath,
        speedFloat
      );
      res.setHeader("Content-Type", "audio/mpeg");
      await pipelineAsync(cachedStream, res);
      return;
    }

    // 2. 缓存不存在，处理音频
    console.log(`缓存不存在，开始处理音频倍速: ${audioPath}`);

    // 从 OSS 获取原音频
    const originalUrl = `https://echolab.oss-cn-hongkong.aliyuncs.com/${audioPath}`;
    const audioBuffer = await audioSpeedService.downloadAudio(originalUrl);

    // 处理倍速
    const processedBuffer = await audioSpeedService.processAudioSpeed(
      audioBuffer,
      speedFloat
    );

    // 保存到缓存
    await audioSpeedService.saveCachedFile(
      audioPath,
      speedFloat,
      processedBuffer
    );

    // 返回处理后的音频
    res.setHeader("Content-Type", "audio/mpeg");
    res.setHeader("Content-Length", processedBuffer.length);
    res.send(processedBuffer);

    console.log(`音频倍速处理完成: ${audioPath}_${speedFloat}x`);
  } catch (error) {
    console.error("音频倍速处理失败:", error);

    // 降级处理：尝试返回原音频
    try {
      const originalUrl = `https://echolab.oss-cn-hongkong.aliyuncs.com/${audioPath}`;
      const response = await axios.get(originalUrl, {
        responseType: "stream",
        timeout: 30000,
      });

      if (response.status === 200) {
        console.log("降级返回原音频");
        res.setHeader(
          "Content-Type",
          response.headers["content-type"] || "audio/mpeg"
        );
        if (response.headers["content-length"]) {
          res.setHeader("Content-Length", response.headers["content-length"]);
        }
        await pipelineAsync(response.data, res);
      } else {
        res.status(404).json({
          success: false,
          error: "音频文件不存在",
        });
      }
    } catch (fallbackError) {
      console.error("降级处理也失败:", fallbackError);
      res.status(500).json({
        success: false,
        error: "音频服务不可用",
      });
    }
  }
});

// 工具函数

function generateMd5Hash(text, language, speed, voiceDbId) {
  const crypto = require("crypto");
  const data = `${text}|${language}|${speed}|${voiceDbId}`; // 使用voice_db_id和统一的分隔符
  return crypto.createHash("md5").update(data).digest("hex");
}

module.exports = router;
