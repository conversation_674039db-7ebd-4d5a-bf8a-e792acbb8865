/**
 * 管理员功能权限API路由
 * 提供功能权限的管理功能
 */
const express = require('express');
const router = express.Router();
const db = require('../../models');
const { authenticate } = require('../../middleware/authMiddleware');
const { adminOnly } = require('../../middleware/adminMiddleware');

/**
 * 获取所有功能标志
 * GET /api/admin/feature-permissions/flags
 */
router.get('/flags', authenticate, adminOnly, async (req, res) => {
  try {
    const flags = await db.FeatureFlag.findAll({
      order: [['featureKey', 'ASC']]
    });

    res.json({
      success: true,
      flags
    });
  } catch (error) {
    console.error('获取功能标志失败:', error);
    res.status(500).json({
      success: false,
      error: `获取功能标志失败: ${error.message}`
    });
  }
});

/**
 * 更新功能标志
 * PUT /api/admin/feature-permissions/flags/:id
 */
router.put('/flags/:id', authenticate, adminOnly, async (req, res) => {
  try {
    const { isEnabled, description } = req.body;
    const flag = await db.FeatureFlag.findByPk(req.params.id);

    if (!flag) {
      return res.status(404).json({
        success: false,
        error: '功能标志不存在'
      });
    }

    // 更新功能标志
    await flag.update({
      isEnabled: isEnabled !== undefined ? isEnabled : flag.isEnabled,
      description: description !== undefined ? description : flag.description
    });

    res.json({
      success: true,
      flag
    });
  } catch (error) {
    console.error('更新功能标志失败:', error);
    res.status(500).json({
      success: false,
      error: `更新功能标志失败: ${error.message}`
    });
  }
});

/**
 * 获取用户功能权限
 * GET /api/admin/feature-permissions/users
 */
router.get('/users', authenticate, adminOnly, async (req, res) => {
  try {
    const permissions = await db.FeaturePermission.findAll({
      include: [
        {
          model: db.User,
          as: 'user',
          attributes: ['id', 'email', 'username']
        }
      ],
      order: [['featureKey', 'ASC'], ['userId', 'ASC']]
    });

    res.json({
      success: true,
      permissions
    });
  } catch (error) {
    console.error('获取用户功能权限失败:', error);
    res.status(500).json({
      success: false,
      error: `获取用户功能权限失败: ${error.message}`
    });
  }
});

/**
 * 添加用户功能权限
 * POST /api/admin/feature-permissions/users
 */
router.post('/users', authenticate, adminOnly, async (req, res) => {
  try {
    const { userId, featureKey } = req.body;

    if (!userId || !featureKey) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数'
      });
    }

    // 检查用户是否存在
    const user = await db.User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: '用户不存在'
      });
    }

    // 检查功能标志是否存在
    const flag = await db.FeatureFlag.findOne({
      where: { featureKey }
    });
    if (!flag) {
      return res.status(404).json({
        success: false,
        error: '功能标志不存在'
      });
    }

    // 创建或更新权限
    const [permission, created] = await db.FeaturePermission.findOrCreate({
      where: { userId, featureKey },
      defaults: { userId, featureKey }
    });

    res.json({
      success: true,
      permission,
      created
    });
  } catch (error) {
    console.error('添加用户功能权限失败:', error);
    res.status(500).json({
      success: false,
      error: `添加用户功能权限失败: ${error.message}`
    });
  }
});

/**
 * 删除用户功能权限
 * DELETE /api/admin/feature-permissions/users/:id
 */
router.delete('/users/:id', authenticate, adminOnly, async (req, res) => {
  try {
    const permission = await db.FeaturePermission.findByPk(req.params.id);

    if (!permission) {
      return res.status(404).json({
        success: false,
        error: '权限不存在'
      });
    }

    await permission.destroy();

    res.json({
      success: true,
      message: '权限已删除'
    });
  } catch (error) {
    console.error('删除用户功能权限失败:', error);
    res.status(500).json({
      success: false,
      error: `删除用户功能权限失败: ${error.message}`
    });
  }
});

module.exports = router;
