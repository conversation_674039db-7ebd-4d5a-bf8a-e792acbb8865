/**
 * 管理操作日志模型
 * 用于记录管理员的操作日志
 */
module.exports = (sequelize, DataTypes) => {
  const AdminLog = sequelize.define(
    "AdminLog",
    {
      // 主键ID
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },

      // 管理员ID
      adminId: {
        type: DataTypes.STRING(21),
        allowNull: false,
        field: "admin_id",
      },

      // 管理员邮箱
      adminEmail: {
        type: DataTypes.STRING(255),
        allowNull: false,
        field: "admin_email",
      },

      // 操作类型
      action: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },

      // 操作目标类型（如 user, content 等）
      targetType: {
        type: DataTypes.STRING(50),
        allowNull: true,
        field: "target_type",
      },

      // 操作目标ID
      targetId: {
        type: DataTypes.STRING(50),
        allowNull: true,
        field: "target_id",
      },

      // 操作详情（JSON格式）
      details: {
        type: DataTypes.JSON,
        allowNull: true,
      },

      // 操作前数据（JSON格式）
      beforeData: {
        type: DataTypes.JSON,
        allowNull: true,
        field: "before_data",
      },

      // 操作后数据（JSON格式）
      afterData: {
        type: DataTypes.JSON,
        allowNull: true,
        field: "after_data",
      },

      // 操作IP地址
      ipAddress: {
        type: DataTypes.STRING(50),
        allowNull: true,
        field: "ip_address",
      },

      // 操作结果
      result: {
        type: DataTypes.ENUM("success", "failure"),
        defaultValue: "success",
      },

      // 操作结果消息
      message: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      // 表名
      tableName: "admin_logs",
      // 时间戳
      timestamps: true,
      underscored: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      // 索引
      indexes: [
        {
          fields: ["admin_id"],
        },
        {
          fields: ["action"],
        },
        {
          fields: ["target_type", "target_id"],
        },
        {
          fields: ["created_at"],
        },
      ],
    }
  );

  return AdminLog;
};
