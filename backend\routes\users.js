/**
 * 用户空间API路由
 * 提供用户信息和用户内容的查询接口
 */
const express = require("express");
const router = express.Router();
const db = require("../models");

/**
 * 获取用户基本信息
 * GET /api/users/:id
 */
router.get("/:id", async (req, res) => {
  try {
    const { id } = req.params;

    const user = await db.User.findByPk(id, {
      attributes: ["id", "username", "created_at"],
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: "用户不存在",
      });
    }

    res.json({
      success: true,
      user,
    });
  } catch (error) {
    console.error("获取用户信息失败:", error);
    res.status(500).json({
      success: false,
      error: `获取用户信息失败: ${error.message}`,
    });
  }
});

/**
 * 获取用户的公开内容列表
 * GET /api/users/:id/contents
 */
router.get("/:id/contents", async (req, res) => {
  try {
    const { id } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const sortBy = req.query.sortBy || "updated_at";
    const sortOrder = req.query.sortOrder || "DESC";

    // 检查用户是否存在
    const user = await db.User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: "用户不存在",
      });
    }

    // 获取用户的公开内容
    const { count, rows } = await db.Content.findAndCountAll({
      where: {
        userId: id,
        status: "published",
      },
      attributes: [
        "id",
        "name",
        "description",
        ["thumbnail_url", "thumbnailUrl"],
        "tags",
        "status",
        ["created_at", "createdAt"],
        ["updated_at", "updatedAt"],
        [
          db.sequelize.literal(
            `(SELECT COUNT(*) FROM content_views WHERE content_views.content_id = Content.id)`
          ),
          "viewCount"
        ],
      ],
      include: [
        {
          model: db.User,
          as: "creator",
          attributes: ["id", "username"],
        },
      ],
      order: [[sortBy, sortOrder]],
      limit: limit,
      offset: (page - 1) * limit,
    });

    res.json({
      success: true,
      contents: rows,
      pagination: {
        total: count,
        page,
        limit,
        totalPages: Math.ceil(count / limit),
      },
    });
  } catch (error) {
    console.error("获取用户内容失败:", error);
    res.status(500).json({
      success: false,
      error: `获取用户内容失败: ${error.message}`,
    });
  }
});

/**
 * 获取用户的公开合集列表
 * GET /api/users/:id/collections
 */
router.get("/:id/collections", async (req, res) => {
  try {
    const { id } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const sortBy = req.query.sortBy || "updated_at";
    const sortOrder = req.query.sortOrder || "DESC";

    // 检查用户是否存在
    const user = await db.User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: "用户不存在",
      });
    }

    // 获取用户的公开合集
    const { count, rows } = await db.Collection.findAndCountAll({
      where: {
        userId: id,
        isPublic: true,
        status: "published",
      },
      include: [
        {
          model: db.CollectionItem,
          as: "items",
          include: [
            {
              model: db.Content,
              as: "content",
              attributes: ["id", "name", ["thumbnail_url", "thumbnailUrl"]],
            },
          ],
        },
        {
          model: db.User,
          as: "creator",
          attributes: ["id", "username"],
        },
      ],
      order: [[sortBy, sortOrder]],
      limit: limit,
      offset: (page - 1) * limit,
    });

    // 计算每个合集的内容数量
    const collections = rows.map((collection) => {
      const collectionData = collection.toJSON();
      collectionData.itemCount = collection.items ? collection.items.length : 0;
      return collectionData;
    });

    res.json({
      success: true,
      collections,
      pagination: {
        total: count,
        page,
        limit,
        totalPages: Math.ceil(count / limit),
      },
    });
  } catch (error) {
    console.error("获取用户合集失败:", error);
    res.status(500).json({
      success: false,
      error: `获取用户合集失败: ${error.message}`,
    });
  }
});

/**
 * 获取用户统计信息
 * GET /api/users/:id/stats
 */
router.get("/:id/stats", async (req, res) => {
  try {
    const { id } = req.params;

    // 检查用户是否存在
    const user = await db.User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: "用户不存在",
      });
    }

    // 统计用户的内容和合集数量
    const [contentCount, collectionCount] = await Promise.all([
      db.Content.count({
        where: {
          userId: id,
          status: "published",
        },
      }),
      db.Collection.count({
        where: {
          userId: id,
          isPublic: true,
          status: "published",
        },
      }),
    ]);

    res.json({
      success: true,
      stats: {
        contentCount,
        collectionCount,
      },
    });
  } catch (error) {
    console.error("获取用户统计信息失败:", error);
    res.status(500).json({
      success: false,
      error: `获取用户统计信息失败: ${error.message}`,
    });
  }
});

module.exports = router;
