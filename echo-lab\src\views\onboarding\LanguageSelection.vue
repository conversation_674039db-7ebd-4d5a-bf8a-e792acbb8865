<!--
  语言选择页面
  用户首次访问时选择学习语言
-->
<template>
  <div class="language-selection-page">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <img src="@/assets/logo.jpg" alt="Echo Lab" class="logo" />
        <h1 class="app-title">Echo Lab</h1>
        <p class="app-subtitle">请选择你要学习的语言</p>
      </div>

      <!-- 主要内容 -->
      <div class="page-content">

        <div class="language-options">
          <div v-for="lang in SUPPORTED_LANGUAGES" :key="lang.value" class="language-card"
            :class="{ 'selected': selectedLanguage === lang.value }" @click="handleLanguageSelect(lang.value)">
            <div class="language-flag">{{ getLanguageFlag(lang.value) }}</div>
            <div class="language-info">
              <h3 class="language-name">{{ lang.label }}</h3>
              <p class="language-desc">{{ getLanguageDescription(lang.value) }}</p>
            </div>
            <div class="selection-indicator">
              <el-icon v-if="selectedLanguage === lang.value" class="check-icon">
                <i-ep-check />
              </el-icon>
            </div>
          </div>
        </div>

        <div class="page-actions">
          <el-button type="primary" size="large" @click="handleNext" :disabled="!selectedLanguage" class="next-button">
            下一步：选择水平
            <el-icon class="el-icon--right">
              <i-ep-arrow-right />
            </el-icon>
          </el-button>
        </div>
      </div>

      <!-- 页面底部 -->
      <div class="page-footer">
        <p class="footer-text">
          选择语言后，你可以随时在设置中更改
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useLanguageStore } from '@/stores/languageStore';
import { SUPPORTED_LANGUAGES } from '@/config/languages';
import { getLanguageFlag } from '@/config/languageLevels';
import { ElMessage } from 'element-plus';

const router = useRouter();
const languageStore = useLanguageStore();

// 选择的语言
const selectedLanguage = ref(null);

// 语言描述
const languageDescriptions = {
  ja: '学习日语，探索日本文化和动漫世界',
  en: '提升英语水平，连接全球机会',
  'zh-CN': '学习中文，了解中华文化',
  'zh-TW': '學習繁體中文，體驗台灣文化'
};

/**
 * 获取语言描述
 */
function getLanguageDescription(languageCode) {
  return languageDescriptions[languageCode] || '开始你的语言学习之旅';
}

/**
 * 处理语言选择
 */
function handleLanguageSelect(languageCode) {
  selectedLanguage.value = languageCode;
}

/**
 * 处理下一步
 */
function handleNext() {
  if (!selectedLanguage.value) {
    ElMessage.warning('请先选择一种学习语言');
    return;
  }

  // 保存选择的语言（内部会自动清理不匹配的等级）
  const success = languageStore.setLearningLanguage(selectedLanguage.value);

  if (!success) {
    ElMessage.error('保存语言设置失败，请重试');
    return;
  }

  console.log('语言选择完成:', selectedLanguage.value);

  // 跳转到等级选择页面
  router.push({
    path: '/onboarding/level',
    query: { language: selectedLanguage.value }
  });
}
</script>

<style scoped>
.language-selection-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.page-container {
  max-width: 48rem;
  width: 100%;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.page-header {
  text-align: center;
  padding: 3rem 2rem 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.logo {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  margin-bottom: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.app-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.app-subtitle {
  font-size: 1rem;
  color: #7f8c8d;
  margin: 0;
}

.page-content {
  padding: 2rem;
}

.language-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(20rem, 1fr));
  gap: 1rem;
  margin-bottom: 3rem;
  margin-top: 1rem;
}

.language-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  border: 2px solid #e9ecef;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
}

.language-card:hover {
  border-color: #409eff;
  background: #f8faff;
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(64, 158, 255, 0.1);
}

.language-card.selected {
  border-color: #409eff;
  background: #ecf5ff;
  box-shadow: 0 0 0 1px #409eff;
}

.language-flag {
  font-size: 3rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.language-info {
  flex: 1;
}

.language-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.language-desc {
  font-size: 0.875rem;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.4;
}

.selection-indicator {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.check-icon {
  color: #409eff;
  font-size: 1.5rem;
}

.page-actions {
  text-align: center;
}

.next-button {
  min-width: 12rem;
  height: 3rem;
  font-size: 1rem;
  font-weight: 600;
}

.page-footer {
  text-align: center;
  padding: 1.5rem 2rem;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.footer-text {
  font-size: 0.875rem;
  color: #6c757d;
  margin: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .language-selection-page {
    padding: 1rem;
  }

  .page-header {
    padding: 2rem 1rem 1.5rem;
  }

  .logo {
    width: 3rem;
    height: 3rem;
  }

  .app-title {
    font-size: 1.5rem;
  }

  .language-options {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .language-card {
    padding: 1rem;
  }

  .language-flag {
    font-size: 2.5rem;
    margin-right: 0.75rem;
  }

  .language-name {
    font-size: 1.125rem;
  }

  .language-desc {
    font-size: 0.8rem;
  }

  .page-content {
    padding: 1.5rem;
  }

  .page-footer {
    padding: 1rem;
  }
}
</style>
