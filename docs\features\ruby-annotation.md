# 振り仮名标注功能

本文档详细说明了 Echo Lab 中振り仮名标注功能的实现和使用方法。

## 概述

振り仮名（Furigana）是日语中在汉字上方或旁边标注假名的一种方式，用于指示汉字的读音。Echo Lab 提供了自动为日语文本添加振り仮名的功能，帮助学习者理解日语文本中汉字的读音。

## 功能特点

- 自动为日语文本中的汉字添加振り仮名
- 支持多种振り仮名格式
- 支持缓存标注结果以提高性能
- 支持手动修正标注结果

## 实现方式

Echo Lab 使用 kuromoji.js 库进行日语分词和读音分析，然后根据分析结果生成带有振り仮名的文本。

### 分词和读音分析

```javascript
// 使用 kuromoji.js 进行分词和读音分析
async function analyzeText(text) {
  // 初始化分词器
  const tokenizer = await getTokenizer();
  
  // 分词
  const tokens = tokenizer.tokenize(text);
  
  // 分析每个词的读音
  const readings = tokens.map(token => {
    return {
      surface: token.surface_form,
      reading: token.reading,
      pos: token.pos,
      isKanji: hasKanji(token.surface_form)
    };
  });
  
  return readings;
}

// 检查文本是否包含汉字
function hasKanji(text) {
  return /[\u4e00-\u9faf]/.test(text);
}
```

### 生成振り仮名

```javascript
// 生成带有振り仮名的文本
function generateFurigana(readings) {
  let result = '';
  
  readings.forEach(reading => {
    if (reading.isKanji && reading.reading) {
      // 将片假名转换为平假名
      const hiragana = katakanaToHiragana(reading.reading);
      
      // 添加带有振り仮名的文本
      result += `${reading.surface}（${hiragana}）`;
    } else {
      // 直接添加文本
      result += reading.surface;
    }
  });
  
  return result;
}

// 将片假名转换为平假名
function katakanaToHiragana(katakana) {
  return katakana.replace(/[\u30a1-\u30f6]/g, match => {
    return String.fromCharCode(match.charCodeAt(0) - 0x60);
  });
}
```

### API 实现

```javascript
// 振り仮名标注 API
async function generateAnnotation(req, res) {
  const { text, language, method, ignoreCache } = req.body;
  
  // 检查参数
  if (!text || !language) {
    return res.status(400).json({ message: '缺少必要参数' });
  }
  
  // 只处理日语文本
  if (language !== 'ja') {
    return res.status(400).json({ message: '只支持日语文本' });
  }
  
  try {
    // 计算文本的 MD5 值作为缓存键
    const textMd5 = crypto.createHash('md5').update(text).digest('hex');
    
    // 检查缓存
    if (!ignoreCache) {
      const cachedAnnotation = await Resource.findOne({
        where: {
          type: 'annotation',
          textMd5,
          language
        }
      });
      
      if (cachedAnnotation) {
        return res.json({ annotation: cachedAnnotation.content });
      }
    }
    
    // 分析文本
    const readings = await analyzeText(text);
    
    // 生成振り仮名
    const annotation = generateFurigana(readings);
    
    // 保存到缓存
    await Resource.create({
      type: 'annotation',
      textMd5,
      text,
      language,
      content: annotation
    });
    
    // 返回结果
    return res.json({ annotation });
  } catch (error) {
    console.error('生成标注失败:', error);
    return res.status(500).json({ message: '服务器错误' });
  }
}
```

## 前端实现

### 资源管理节点

资源管理节点提供了振り仮名标注的配置和生成功能：

```javascript
// 资源管理节点中的标注配置
const annotationConfig = {
  enabled: true,
  method: 'furigana'
};

// 生成标注
async function generateAnnotations(sequence, resources) {
  // 如果未启用标注，返回
  if (!this.params.annotation.enabled) {
    return;
  }
  
  // 获取标注方法
  const method = this.params.annotation.method;
  
  // 为每个内容项生成标注
  for (const item of sequence) {
    // 只处理日语内容
    if (item.language !== 'ja') {
      continue;
    }
    
    try {
      // 调用标注服务
      const annotation = await this.callAnnotationService(item.content, method);
      
      // 保存标注
      resources.annotations[item.id] = annotation;
    } catch (error) {
      console.error(`生成标注失败: ${error.message}`);
    }
  }
}
```

### 播放器组件

播放器组件负责显示带有振り仮名的文本：

```vue
<!-- 播放器组件中的振り仮名显示 -->
<template>
  <div class="content-display">
    <div class="content-text" v-if="currentItem && !showAnnotation">
      {{ currentItem.content }}
    </div>
    <div class="content-text annotation" v-if="currentItem && showAnnotation">
      {{ getAnnotation(currentItem.id) }}
    </div>
  </div>
</template>

<script setup>
// 获取标注
function getAnnotation(itemId) {
  const resources = useResourceStore();
  return resources.annotations[itemId] || currentItem.content;
}
</script>
```

## 振り仮名格式

Echo Lab 支持以下振り仮名格式：

### 括号格式

括号格式使用括号标注汉字的读音：

```
今日（きょう）は良（よ）い天気（てんき）ですね。
```

### HTML Ruby 格式

HTML Ruby 格式使用 HTML `<ruby>` 标签标注汉字的读音：

```html
<ruby>今日<rt>きょう</rt></ruby>は<ruby>良<rt>よ</rt></ruby>い<ruby>天気<rt>てんき</rt></ruby>ですね。
```

## 使用方法

### 在资源管理节点中启用振り仮名标注

1. 在编辑器中创建或选择资源管理节点
2. 在标注设置中勾选"启用标注"
3. 选择标注方法为"振り仮名"
4. 点击"生成所有资源"按钮

### 在播放器中显示振り仮名

1. 在播放器设置中勾选"显示标注"
2. 播放内容时将显示带有振り仮名的文本

## 最佳实践

1. **选择性标注**：
   - 只为包含生僻汉字的文本添加振り仮名
   - 避免为简单的文本添加不必要的振り仮名

2. **检查标注质量**：
   - 自动标注可能存在错误，特别是对于多音字
   - 检查并手动修正标注结果

3. **性能优化**：
   - 使用缓存减少重复生成
   - 批量生成标注以减少 API 调用

## 常见问题

### 标注不正确

问题：自动生成的振り仮名不正确。

解决方案：
- 检查原始文本是否正确
- 手动修正标注结果
- 对于特殊读音的词汇，考虑使用手动标注

### 标注不完整

问题：某些汉字没有标注振り仮名。

解决方案：
- 检查汉字是否在词典中
- 对于生僻字或专有名词，可能需要手动添加标注
- 更新词典库以支持更多汉字

### 性能问题

问题：生成标注速度较慢。

解决方案：
- 使用缓存减少重复生成
- 批量生成标注以减少 API 调用
- 优化分词和读音分析算法
