/**
 * 请求日志中间件
 * 记录所有API请求的详细信息，便于安全审计和问题排查
 */
const { nanoid } = require('nanoid');
const fs = require('fs');
const path = require('path');
const morgan = require('morgan');
const rfs = require('rotating-file-stream');

// 确保日志目录存在
const logDirectory = path.join(__dirname, '../logs');
fs.existsSync(logDirectory) || fs.mkdirSync(logDirectory, { recursive: true });

// 创建日志轮转流
const accessLogStream = rfs.createStream('access.log', {
  interval: '1d', // 每天轮转
  path: logDirectory,
  size: '10M', // 或者按大小轮转
  compress: 'gzip', // 压缩旧日志
  maxFiles: 14, // 保留14天的日志
});

// 自定义日志格式
morgan.token('id', (req) => req.id);
morgan.token('body', (req) => {
  const body = { ...req.body };
  
  // 敏感字段脱敏
  if (body.password) body.password = '******';
  if (body.code) body.code = '******';
  if (body.token) body.token = '******';
  
  return JSON.stringify(body);
});
morgan.token('user-id', (req) => (req.user ? req.user.id : '-'));
morgan.token('error', (req) => (req.error ? req.error.message : '-'));

// 创建请求ID中间件
function requestId(options = {}) {
  const {
    header = 'X-Request-ID',
    propertyName = 'id',
    generator = () => nanoid(10),
  } = options;

  return (req, res, next) => {
    // 使用请求头中的ID或生成新ID
    const id = req.headers[header.toLowerCase()] || generator();
    
    // 将ID添加到请求对象
    req[propertyName] = id;
    
    // 将ID添加到响应头
    res.setHeader(header, id);
    
    next();
  };
}

// 创建日志中间件
function createLogger(options = {}) {
  const {
    format = ':id :remote-addr - :user-id [:date[iso]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" :response-time ms :body',
    skip = (req, res) => false,
  } = options;

  return [
    // 添加请求ID
    requestId(),
    
    // 控制台日志
    morgan(format, {
      skip,
    }),
    
    // 文件日志
    morgan(format, {
      skip,
      stream: accessLogStream,
    }),
    
    // 记录错误
    (err, req, res, next) => {
      req.error = err;
      next(err);
    },
  ];
}

// 预定义的日志中间件
const loggers = {
  // 标准API日志
  api: createLogger({
    skip: (req, res) => {
      // 跳过静态资源和健康检查
      return req.path.startsWith('/public/') || req.path === '/health';
    },
  }),
  
  // 错误日志
  error: (err, req, res, next) => {
    // 记录错误详情
    console.error(`[Error] 请求ID: ${req.id}, 路径: ${req.method} ${req.originalUrl}, 错误:`, err);
    
    // 继续错误处理
    next(err);
  },
};

module.exports = loggers;
