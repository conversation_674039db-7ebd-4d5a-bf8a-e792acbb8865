<!--
  节点画布组件
  用于显示和交互节点
-->
<template>
  <div class="node-canvas-container" ref="canvasContainerRef" @mousedown="startPanCanvas" @wheel="handleZoom">
    <div class="canvas-toolbar">
      <div class="zoom-controls">
        <el-button-group>
          <el-button type="primary" size="small" @click="zoomIn">
            <el-icon>
              <i-ep-zoom-in />
            </el-icon>
          </el-button>
          <el-button type="primary" size="small" @click="zoomOut">
            <el-icon>
              <i-ep-zoom-out />
            </el-icon>
          </el-button>
          <el-button type="primary" size="small" @click="resetCanvas">
            <el-icon>
              <i-ep-refresh />
            </el-icon>
          </el-button>
        </el-button-group>
      </div>
      <div class="clear-button">
        <el-button type="danger" @click="clearCanvas">
          清空画布
        </el-button>
      </div>
    </div>

    <div class="canvas" ref="canvasRef" :style="canvasStyle">
      <div v-for="nodeId in nodeIds" :key="nodeId" class="node-wrapper" :style="getNodeStyle(nodeId)"
        @mousedown="startDragNode($event, nodeId)" :class="{ 'selected': nodeId === selectedNodeId }"
        @click.stop="selectNode(nodeId)" :data-node-id="nodeId">
        <component :is="getNodeComponent(nodeId)" :nodeId="nodeId" @node-updated="handleNodeUpdated" />
      </div>

      <svg class="connections-layer">
        <g>
          <path v-for="connection in connections" :key="`${connection.source}-${connection.target}`"
            :d="getConnectionPath(connection)" class="connection-path"
            :class="{ 'selected': isConnectionSelected(connection) }"
            @click.stop="selectConnection(connection, $event)" />
        </g>
        <svg class="temp-connection-layer" v-if="isCreatingConnection">
          <path :d="getTempConnectionPath()" class="temp-connection-path" />
        </svg>
      </svg>
    </div>

    <!-- 添加节点按钮 -->
    <div class="add-node-button">
      <el-dropdown trigger="click" @command="addNode">
        <el-button type="primary" circle>
          <el-icon>
            <i-ep-plus />
          </el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item v-for="(config, type) in nodeTypes" :key="type" :command="type"
              :disabled="disabledNodeTypes[type]" :title="disabledNodeTypes[type] ? '每个项目只能有一个资源管理节点' : ''">
              <el-icon v-if="config.icon">
                <component :is="config.icon" />
              </el-icon>
              <span>{{ config.label }}</span>
              <span v-if="disabledNodeTypes[type]" class="disabled-reason">(已存在)</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <!-- 删除按钮已移除 -->
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useNodeStore } from '@/core/stores/nodeStore';
import nodeFactory from '@/core/factories/NodeFactory';
import { nodeComponents } from '@/components/nodes';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入

const props = defineProps({
  width: {
    type: [Number, String],
    default: '100%'
  },
  height: {
    type: [Number, String],
    default: '100%'
  }
});

const emit = defineEmits(['node-selected', 'node-added', 'node-deleted', 'connection-created', 'connection-deleted', 'node-updated']);

const nodeStore = useNodeStore();

// 画布引用
const canvasContainerRef = ref(null);
const canvasRef = ref(null);

// 画布状态
const scale = ref(1);
const offset = ref({ x: 0, y: 0 });

// 拖拽状态
const isDragging = ref(false);
const dragNodeId = ref(null);
const dragStartPos = ref({ x: 0, y: 0 });
const dragStartMousePos = ref({ x: 0, y: 0 });

// 平移状态
const isPanning = ref(false);
const panStartPos = ref({ x: 0, y: 0 });

// 连接状态
const isCreatingConnection = ref(false);
const connectionSourceId = ref(null);
const connectionTargetPos = ref({ x: 0, y: 0 });

// 选中状态
const selectedNodeId = ref(null);
const selectedConnection = ref(null);

// 节点类型
const nodeTypes = computed(() => {
  const types = nodeFactory.getAllNodeTypes();
  return types;
});

// 计算哪些节点类型应该被禁用
const disabledNodeTypes = computed(() => {
  const result = {};

  // 检查是否已存在资源管理节点
  const hasResourceNode = nodeStore.getAllNodes.some(node => node.type === 'resource');

  // 如果已存在资源管理节点，禁用资源管理节点类型
  if (hasResourceNode) {
    result['resource'] = true;
  }

  return result;
});

// 节点ID列表
const nodeIds = computed(() => {
  const nodes = nodeStore.getAllNodes;
  return nodes.map(node => node.id);
});

// 连接列表
const connections = computed(() => {
  const result = [];
  const nodes = nodeStore.getAllNodes;

  nodes.forEach(node => {
    if (node.sourceIds && node.sourceIds.length > 0) {
      node.sourceIds.forEach(sourceId => {
        result.push({
          source: sourceId,
          target: node.id
        });
      });
    }
  });

  return result;
});

// 画布样式
const canvasStyle = computed(() => {
  return {
    transform: `scale(${scale.value}) translate(${offset.value.x}px, ${offset.value.y}px)`,
    width: '5000px',
    height: '5000px'
  };
});

// 初始化
onMounted(() => {
  // 添加事件监听
  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);

  // 初始化画布位置
  centerCanvas();
});

// 清理
onUnmounted(() => {
  // 移除事件监听
  document.removeEventListener('mousemove', onMouseMove);
  document.removeEventListener('mouseup', onMouseUp);
});

// 获取节点样式
function getNodeStyle(nodeId) {
  const node = nodeStore.getNode(nodeId);
  if (!node) return {};

  return {
    position: 'absolute',
    left: `${node.position.x}px`,
    top: `${node.position.y}px`,
    zIndex: selectedNodeId.value === nodeId ? 100 : 10
  };
}

// 获取节点组件
function getNodeComponent(nodeId) {
  const node = nodeStore.getNode(nodeId);
  if (!node) return null;

  return nodeComponents[node.type] || null;
}

// 获取连接路径
function getConnectionPath(connection) {
  const sourceNode = nodeStore.getNode(connection.source);
  const targetNode = nodeStore.getNode(connection.target);

  if (!sourceNode || !targetNode) return '';

  // 计算连接点位置
  const sourcePos = getOutputPosition(sourceNode);
  const targetPos = getInputPosition(targetNode);

  // 计算控制点
  const dx = targetPos.x - sourcePos.x;
  const controlX = Math.abs(dx) * 0.5;

  // 生成路径
  return `M ${sourcePos.x} ${sourcePos.y} C ${sourcePos.x + controlX} ${sourcePos.y}, ${targetPos.x - controlX} ${targetPos.y}, ${targetPos.x} ${targetPos.y}`;
}

// 获取临时连接路径
function getTempConnectionPath() {
  if (!isCreatingConnection.value || !connectionSourceId.value) return '';

  const sourceNode = nodeStore.getNode(connectionSourceId.value);
  if (!sourceNode) return '';

  // 计算连接点位置
  const sourcePos = getOutputPosition(sourceNode);
  const targetPos = connectionTargetPos.value;

  // 计算控制点
  const dx = targetPos.x - sourcePos.x;
  const controlX = Math.abs(dx) * 0.5;

  // 生成路径
  return `M ${sourcePos.x} ${sourcePos.y} C ${sourcePos.x + controlX} ${sourcePos.y}, ${targetPos.x - controlX} ${targetPos.y}, ${targetPos.x} ${targetPos.y}`;
}

// 获取输入连接点位置
function getInputPosition(node) {
  const nodeElement = document.querySelector(`[data-node-id="${node.id}"]`);
  if (!nodeElement) return { x: node.position.x, y: node.position.y + 50 };

  const rect = nodeElement.getBoundingClientRect();
  const canvasRect = canvasRef.value.getBoundingClientRect();

  return {
    x: (rect.left - canvasRect.left) / scale.value,
    y: (rect.top - canvasRect.top + rect.height / 2) / scale.value
  };
}

// 获取输出连接点位置
function getOutputPosition(node) {
  const nodeElement = document.querySelector(`[data-node-id="${node.id}"]`);
  if (!nodeElement) return { x: node.position.x + 300, y: node.position.y + 50 };

  const rect = nodeElement.getBoundingClientRect();
  const canvasRect = canvasRef.value.getBoundingClientRect();

  return {
    x: (rect.right - canvasRect.left) / scale.value,
    y: (rect.top - canvasRect.top + rect.height / 2) / scale.value
  };
}

// 开始拖拽节点
function startDragNode(event, nodeId) {
  // 检查是否点击了允许拖动的区域
  const isDragHandle = event.target.closest('.drag-handle');
  const isNodeHeader = event.target.closest('.node-header') &&
    !event.target.closest('.el-button') &&
    !event.target.closest('.node-actions');

  // 如果不是拖动区域或节点头部，则不触发拖动
  if (!isDragHandle && !isNodeHeader) {
    // 仍然选中节点，但不触发拖动
    selectNode(nodeId);
    return;
  }

  // 触发拖动
  isDragging.value = true;
  dragNodeId.value = nodeId;

  const node = nodeStore.getNode(nodeId);
  dragStartPos.value = { ...node.position };
  dragStartMousePos.value = { x: event.clientX, y: event.clientY };

  // 选中节点
  selectNode(nodeId);

  // 阻止事件冒泡
  event.stopPropagation();
}

// 开始平移画布
function startPanCanvas(event) {
  // 如果点击的是节点或正在创建连接，不触发平移
  if (event.target.closest('.node-wrapper') || isCreatingConnection.value) {
    return;
  }

  isPanning.value = true;
  panStartPos.value = {
    x: event.clientX,
    y: event.clientY,
    offsetX: offset.value.x,
    offsetY: offset.value.y
  };

  // 设置光标样式
  document.body.style.cursor = 'grabbing';

  // 如果点击的是画布空白区域，取消选中
  if (!event.target.closest('.node-wrapper') && !event.target.closest('.connection-path')) {
    deselectAll();
  }

  // 阻止事件冒泡
  event.stopPropagation();
}

// 鼠标移动
function onMouseMove(event) {
  // 处理节点拖拽
  if (isDragging.value && dragNodeId.value) {
    const dx = event.clientX - dragStartMousePos.value.x;
    const dy = event.clientY - dragStartMousePos.value.y;

    const newPosition = {
      x: dragStartPos.value.x + dx / scale.value,
      y: dragStartPos.value.y + dy / scale.value
    };

    nodeStore.updateNodePosition(dragNodeId.value, newPosition);
  }

  // 处理画布平移
  if (isPanning.value) {
    const dx = event.clientX - panStartPos.value.x;
    const dy = event.clientY - panStartPos.value.y;

    offset.value = {
      x: panStartPos.value.offsetX + dx / scale.value,
      y: panStartPos.value.offsetY + dy / scale.value
    };
  }

  // 处理连接创建
  if (isCreatingConnection.value) {
    const canvasRect = canvasRef.value.getBoundingClientRect();

    connectionTargetPos.value = {
      x: (event.clientX - canvasRect.left) / scale.value,
      y: (event.clientY - canvasRect.top) / scale.value
    };
  }
}

// 鼠标松开
function onMouseUp(event) {
  // 处理节点拖拽结束
  if (isDragging.value) {
    isDragging.value = false;
    dragNodeId.value = null;
  }

  // 处理画布平移结束
  if (isPanning.value) {
    isPanning.value = false;
    document.body.style.cursor = 'default';
  }

  // 处理连接创建结束
  if (isCreatingConnection.value) {
    // 检查是否释放在节点上
    const targetElement = document.elementFromPoint(event.clientX, event.clientY);
    const targetNodeElement = targetElement?.closest('.node-wrapper');

    if (targetNodeElement) {
      const targetNodeId = targetNodeElement.getAttribute('data-node-id');

      if (targetNodeId && targetNodeId !== connectionSourceId.value) {
        // 创建连接
        createConnection(connectionSourceId.value, targetNodeId);
      }
    }

    // 重置连接状态
    isCreatingConnection.value = false;
    connectionSourceId.value = null;
  }
}

// 缩放防抖
let zoomTimeout = null;
const zoomThreshold = 50; // 毫秒

// 处理缩放
function handleZoom(event) {
  // 阻止默认滚动行为
  event.preventDefault();

  // 如果已经有一个缩放操作在进行中，取消它
  if (zoomTimeout) {
    clearTimeout(zoomTimeout);
  }

  // 使用防抖动机制延迟执行缩放
  zoomTimeout = setTimeout(() => {
    // 计算缩放方向
    const direction = event.deltaY < 0 ? 1 : -1;

    // 计算缩放因子 - 减小因子使缩放更平滑
    const factor = 0.05; // 从0.1减小到0.05
    const newScale = scale.value * (1 + direction * factor);

    // 限制缩放范围 - 设置更严格的限制
    if (newScale >= 0.3 && newScale <= 1.5) {
      // 获取鼠标相对于画布的位置
      const canvasRect = canvasRef.value.getBoundingClientRect();
      const mouseX = (event.clientX - canvasRect.left) / scale.value - offset.value.x;
      const mouseY = (event.clientY - canvasRect.top) / scale.value - offset.value.y;

      // 计算新的偏移量，保持鼠标位置不变
      const newOffsetX = offset.value.x - mouseX * (newScale - scale.value) / newScale;
      const newOffsetY = offset.value.y - mouseY * (newScale - scale.value) / newScale;

      // 更新缩放和偏移
      scale.value = newScale;
      offset.value = { x: newOffsetX, y: newOffsetY };
    }

    zoomTimeout = null;
  }, zoomThreshold);
}

// 放大
function zoomIn() {
  // 使用更小的缩放步长
  const newScale = scale.value * 1.1;

  // 应用相同的缩放限制
  if (newScale <= 1.5) {
    scale.value = newScale;
  } else {
    scale.value = 1.5; // 限制最大缩放
  }
}

// 缩小
function zoomOut() {
  // 使用更小的缩放步长
  const newScale = scale.value / 1.1;

  // 应用相同的缩放限制
  if (newScale >= 0.3) {
    scale.value = newScale;
  } else {
    scale.value = 0.3; // 限制最小缩放
  }
}

// 重置画布
function resetCanvas() {
  scale.value = 1;
  centerCanvas();
}

// 居中画布
function centerCanvas() {
  if (!canvasContainerRef.value) return;

  const containerRect = canvasContainerRef.value.getBoundingClientRect();

  offset.value = {
    x: containerRect.width / 2 / scale.value - 2500,
    y: containerRect.height / 2 / scale.value - 2500
  };
}

// 选中节点
function selectNode(nodeId) {
  selectedNodeId.value = nodeId;
  selectedConnection.value = null;

  emit('node-selected', nodeId);
}

// 选中连接
function selectConnection(connection, e) {
  selectedConnection.value = connection;
  selectedNodeId.value = null;

  // 阻止事件冒泡
  if (e) e.stopPropagation();
}

// 取消选中
function deselectAll() {
  selectedNodeId.value = null;
  selectedConnection.value = null;

  emit('node-selected', null);
}

// 检查连接是否被选中
function isConnectionSelected(connection) {
  if (!selectedConnection.value) return false;

  return selectedConnection.value.source === connection.source &&
    selectedConnection.value.target === connection.target;
}

// 添加节点
function addNode(type) {
  // 如果节点类型被禁用，显示提示信息并返回
  if (disabledNodeTypes.value[type]) {
    ElMessage.warning('每个项目只能有一个资源管理节点');
    return;
  }

  // 计算新节点位置
  const containerRect = canvasContainerRef.value.getBoundingClientRect();
  const centerX = containerRect.width / 2 / scale.value - offset.value.x;
  const centerY = containerRect.height / 2 / scale.value - offset.value.y;

  try {
    // 创建节点
    const nodeId = nodeStore.createNode(type, {}, [], { x: centerX - 150, y: centerY - 100 });

    // 选中新节点
    selectNode(nodeId);

    // 发送事件
    emit('node-added', nodeId);

    ElMessage.success(`已添加${nodeTypes.value[type]?.label || type}节点`);
  } catch (error) {
    console.error('创建节点失败:', error);
    ElMessage.error(`创建节点失败: ${error.message}`);
  }
}

// 创建连接
function createConnection(sourceId, targetId) {
  try {
    // 连接节点
    const result = nodeStore.connectNodes(sourceId, targetId);

    if (result) {
      // 发送事件
      emit('connection-created', { source: sourceId, target: targetId });

      ElMessage.success('连接创建成功');
    } else {
      ElMessage.error('无法创建连接');
    }
  } catch (error) {
    console.error('创建连接失败:', error);
    ElMessage.error(`创建连接失败: ${error.message}`);
  }
}

// 处理节点更新事件
function handleNodeUpdated(nodeId) {
  // 转发事件到父组件
  emit('node-updated', nodeId);

  // 如果需要，可以在这里添加其他处理逻辑
}

// 删除选中的节点或连接
function deleteSelected() {
  if (selectedNodeId.value) {
    // 删除节点
    ElMessageBox.confirm('确定要删除选中的节点吗？', '确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const nodeId = selectedNodeId.value;
      nodeStore.removeNode(nodeId);
      selectedNodeId.value = null;

      // 发送事件
      emit('node-deleted', nodeId);

      ElMessage.success('节点已删除');
    }).catch(() => {
      // 取消操作
    });
  } else if (selectedConnection.value) {
    // 删除连接
    ElMessageBox.confirm('确定要删除选中的连接吗？', '确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const { source, target } = selectedConnection.value;
      nodeStore.disconnectNodes(source, target);
      selectedConnection.value = null;

      // 发送事件
      emit('connection-deleted', { source, target });

      ElMessage.success('连接已删除');
    }).catch(() => {
      // 取消操作
    });
  }
}

// 清空画布
function clearCanvas() {
  ElMessageBox.confirm('确定要清空画布吗？此操作不可恢复。', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    nodeStore.clearAllNodes();
    selectedNodeId.value = null;
    ElMessage.success('画布已清空');
  }).catch(() => {
    // 取消操作
  });
}
</script>

<style scoped>
.node-canvas-container {
  position: relative;
  width: v-bind('props.width');
  height: v-bind('props.height');
  overflow: hidden;
  background-color: #f0f2f5;
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.1) 0.0625rem, transparent 0.0625rem),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 0.0625rem, transparent 0.0625rem);
  background-size: 1.25rem 1.25rem;
}

.canvas-toolbar {
  position: absolute;
  top: 1rem;
  left: 1rem;
  right: 1rem;
  z-index: 100;
  display: flex;
  justify-content: space-between;
}

.zoom-controls {
  display: flex;
}

.clear-button {
  display: flex;
}

.canvas {
  position: absolute;
  transform-origin: top left;
  background-color: transparent;
}

.node-wrapper {
  position: absolute;
  z-index: 1;
  user-select: none;
  transition: transform 0.1s ease;
}

.node-wrapper.selected {
  z-index: 2;
}

.connections-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.connection-path {
  fill: none;
  stroke: #909399;
  stroke-width: 0.125rem;
  stroke-dasharray: 0.5rem 0.25rem;
  pointer-events: stroke;
  cursor: pointer;
}

.connection-path.selected {
  stroke: #409EFF;
  stroke-width: 0.1875rem;
}

.temp-connection-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.temp-connection-path {
  fill: none;
  stroke: #409EFF;
  stroke-width: 0.125rem;
  stroke-dasharray: 0.5rem 0.25rem;
}

.add-node-button {
  position: absolute;
  bottom: 1.5rem;
  right: 1.5rem;
  z-index: 100;
}

.disabled-reason {
  font-size: 0.75rem;
  color: #909399;
  margin-left: 0.5rem;
}

.el-dropdown-menu__item.is-disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

/* 删除按钮样式已移除 */
</style>
