/**
 * 节点处理器索引
 * 导出所有节点处理器和注册函数
 */

import textContentNodeProcessor from "./textContentNodeProcessor";
import textSequenceNodeProcessor from "./textSequenceNodeProcessor";
import videoConfigNodeProcessor from "./videoConfigNodeProcessor";
import resourceNodeProcessor from "./resourceNodeProcessor";

// 导出所有节点处理器
export const nodeProcessors = {
  textContent: textContentNodeProcessor,
  textSequence: textSequenceNodeProcessor,
  videoConfig: videoConfigNodeProcessor,
  resource: resourceNodeProcessor,
};

// 导出节点处理器注册函数
export function registerNodeProcessors(nodeFactory) {
  // 注册文本内容节点
  nodeFactory.registerNodeType(
    "textContent",
    {
      label: "文本内容节点",
      icon: "Document",
      color: "#67C23A",
      defaultParams: {
        text: "",
        language: "ja",
        mode: "paragraph",
        splitBy: "\n",
        segments: [],
        enableSpeakerParsing: true,
      },
      acceptTargetTypes: ["textSequence", "resource"],
    },
    textContentNodeProcessor
  );

  // 注册资源管理节点
  nodeFactory.registerNodeType(
    "resource",
    {
      label: "资源管理节点",
      icon: "Management",
      color: "#8E44AD",
      defaultParams: {
        annotations: {},
        translations: {},
        targets: [],
      },
      acceptSourceTypes: ["textContent"],
      acceptTargetTypes: ["videoConfig"],
    },
    resourceNodeProcessor
  );

  // 注册文本序列节点
  nodeFactory.registerNodeType(
    "textSequence",
    {
      label: "文本序列节点",
      icon: "Sort",
      color: "#909399",
      defaultParams: {
        // 移除历史遗留字段，只保留必要的sections数组
        sections: [],
      },
      acceptSourceTypes: ["textContent"], // 只接受文本内容节点
      acceptTargetTypes: ["videoConfig"],
    },
    textSequenceNodeProcessor
  );

  // 注册视频配置节点
  nodeFactory.registerNodeType(
    "videoConfig",
    {
      label: "视频配置节点",
      icon: "VideoCamera",
      color: "#FF9800",
      defaultParams: {
        cover: {
          imageUrl: "",
          duration: 1, // 修改默认值为1秒
        },
        copyright: {
          text: "https://echolab.club",
          position: "topLeft", // 修改默认值为左上
        },
        contentOrder: [],
      },
      acceptSourceTypes: ["textSequence"],
    },
    videoConfigNodeProcessor
  );
}

export default nodeProcessors;
