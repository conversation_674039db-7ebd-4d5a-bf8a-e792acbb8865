/**
 * 内容API路由
 * 提供素材内容的保存、加载和管理功能
 */
const express = require("express");
const router = express.Router();
const db = require("../models");
const crypto = require("crypto");
const { authenticate } = require("../middleware/authMiddleware");
const {
  checkFeaturePermission,
} = require("../middleware/featurePermissionMiddleware");
const authService = require("../services/authService");

// 自定义 nanoid 函数，生成 21 位随机 ID
function nanoid() {
  return crypto
    .randomBytes(16)
    .toString("base64")
    .replace(/[+/=]/g, "")
    .substring(0, 21);
}

/**
 * 获取所有素材内容
 * GET /api/contents
 */
router.get(
  "/",
  authenticate,
  checkFeaturePermission("content_creation"),
  async (req, res) => {
    try {
      const contents = await db.Content.findAll({
        attributes: [
          "id",
          "name",
          "description",
          "thumbnailUrl",
          "tags",
          "status",
          ["created_at", "createdAt"],
          ["updated_at", "updatedAt"],
        ],
        order: [["created_at", "DESC"]],
      });

      res.json({
        success: true,
        contents,
      });
    } catch (error) {
      console.error("获取素材列表失败:", error);
      res.status(500).json({
        success: false,
        error: `获取素材列表失败: ${error.message}`,
      });
    }
  }
);

/**
 * 获取单个素材内容（包含观看次数）
 * GET /api/contents/:id
 * 注意：此接口有条件认证，未登录用户只能访问已发布的内容
 */
router.get("/:id", async (req, res) => {
  try {
    // 检查是否有认证信息
    const authHeader = req.headers.authorization;
    const isAuthenticated = authHeader && authHeader.startsWith("Bearer ");

    // 根据认证状态决定查询条件
    let content;

    if (isAuthenticated) {
      try {
        // 尝试验证令牌
        const token = authHeader.split(" ")[1];
        const result = await authService.verifyToken(token);

        if (result.success) {
          // 已认证用户可以访问所有内容
          content = await db.Content.findByPk(req.params.id);

          // 如果是草稿内容，检查用户是否有权限访问
          if (content && content.status === "draft") {
            // 检查用户是否为内容所有者或管理员
            if (
              content.userId !== result.decoded.id &&
              result.decoded.role !== "admin"
            ) {
              // 非所有者且非管理员，只能访问已发布内容
              return res.status(403).json({
                success: false,
                error: "权限不足，您不是内容所有者",
              });
            }
          }
        } else {
          // 令牌无效，视为未认证用户
          content = await db.Content.findOne({
            where: {
              id: req.params.id,
              status: "published",
            },
          });
        }
      } catch (error) {
        // 令牌验证出错，视为未认证用户
        console.error("令牌验证失败:", error);
        content = await db.Content.findOne({
          where: {
            id: req.params.id,
            status: "published",
          },
        });
      }
    } else {
      // 未认证用户只能访问已发布的内容
      content = await db.Content.findOne({
        where: {
          id: req.params.id,
          status: "published",
        },
      });
    }

    if (!content) {
      return res.status(404).json({
        success: false,
        error: "内容不存在或未发布",
      });
    }

    // 获取观看次数
    const viewCount = await db.ContentView.count({
      where: { contentId: req.params.id }
    });

    res.json({
      success: true,
      content: {
        ...content.toJSON(),
        viewCount,
      },
    });
  } catch (error) {
    console.error(`获取素材失败 (ID: ${req.params.id}):`, error);
    res.status(500).json({
      success: false,
      error: `获取素材失败: ${error.message}`,
    });
  }
});

/**
 * 创建新素材内容
 * POST /api/contents
 */
router.post(
  "/",
  authenticate,
  checkFeaturePermission("content_creation"),
  async (req, res) => {
    try {
      const { name, description, configJson, thumbnailUrl, tags, learningLanguage } = req.body;

      if (!name) {
        return res.status(400).json({
          success: false,
          error: "缺少必要参数: name",
        });
      }

      if (!configJson) {
        return res.status(400).json({
          success: false,
          error: "缺少必要参数: configJson",
        });
      }

      // 生成nanoid作为ID
      const id = nanoid();

      // 从认证中间件获取用户ID
      const userId = req.user.id;

      // 从请求头获取用户学习语言作为默认值
      const defaultLearningLanguage = req.headers['x-user-language'] || 'ja';

      const content = await db.Content.create({
        id,
        name,
        description,
        configJson,
        thumbnailUrl,
        userId, // 使用从JWT令牌中获取的用户ID
        tags,
        learningLanguage: learningLanguage || defaultLearningLanguage, // 使用传入的语言或用户默认语言
        status: req.body.status || "draft",
      });

      res.status(201).json({
        success: true,
        content,
      });
    } catch (error) {
      console.error("创建素材失败:", error);
      res.status(500).json({
        success: false,
        error: `创建素材失败: ${error.message}`,
      });
    }
  }
);

/**
 * 更新素材内容
 * PUT /api/contents/:id
 */
router.put(
  "/:id",
  authenticate,
  checkFeaturePermission("content_creation"),
  async (req, res) => {
    try {
      const { name, description, configJson, thumbnailUrl, tags, learningLanguage } = req.body;

      const content = await db.Content.findByPk(req.params.id);

      if (!content) {
        return res.status(404).json({
          success: false,
          error: "素材不存在",
        });
      }

      // 检查用户是否为内容所有者或管理员
      if (
        content.userId &&
        content.userId !== req.user.id &&
        req.user.role !== "admin"
      ) {
        return res.status(403).json({
          success: false,
          error: "权限不足，您不是内容所有者",
        });
      }

      // 准备更新数据
      const updateData = {
        name: name || content.name,
        description:
          description !== undefined ? description : content.description,
        configJson: configJson || content.configJson,
        thumbnailUrl:
          thumbnailUrl !== undefined ? thumbnailUrl : content.thumbnailUrl,
        tags: tags !== undefined ? tags : content.tags,
        learningLanguage: learningLanguage !== undefined ? learningLanguage : content.learningLanguage,
        userId: content.userId || req.user.id, // 如果内容没有所有者，设置为当前用户
      };

      // 添加status字段
      if (req.body.status) {
        updateData.status = req.body.status;
      }

      // 更新素材
      await content.update(updateData);

      res.json({
        success: true,
        content,
      });
    } catch (error) {
      console.error(`更新素材失败 (ID: ${req.params.id}):`, error);
      res.status(500).json({
        success: false,
        error: `更新素材失败: ${error.message}`,
      });
    }
  }
);

/**
 * 删除素材内容
 * DELETE /api/contents/:id
 */
router.delete("/:id", authenticate, async (req, res) => {
  try {
    const content = await db.Content.findByPk(req.params.id);

    if (!content) {
      return res.status(404).json({
        success: false,
        error: "素材不存在",
      });
    }

    // 检查用户是否为内容所有者或管理员
    if (
      content.userId &&
      content.userId !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return res.status(403).json({
        success: false,
        error: "权限不足，您不是内容所有者",
      });
    }

    await content.destroy();

    res.json({
      success: true,
      message: "素材已删除",
    });
  } catch (error) {
    console.error(`删除素材失败 (ID: ${req.params.id}):`, error);
    res.status(500).json({
      success: false,
      error: `删除素材失败: ${error.message}`,
    });
  }
});

/**
 * 发布内容（上线）
 * PUT /api/contents/:id/publish
 */
router.put("/:id/publish", authenticate, async (req, res) => {
  try {
    const content = await db.Content.findByPk(req.params.id);

    if (!content) {
      return res.status(404).json({
        success: false,
        error: "素材不存在",
      });
    }

    // 检查用户是否为内容所有者或管理员
    if (
      content.userId &&
      content.userId !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return res.status(403).json({
        success: false,
        error: "权限不足，您不是内容所有者",
      });
    }

    // 更新状态为已发布
    await content.update({
      status: "published",
      userId: content.userId || req.user.id, // 如果内容没有所有者，设置为当前用户
    });

    res.json({
      success: true,
      content,
      message: "内容已发布",
    });
  } catch (error) {
    console.error(`发布内容失败 (ID: ${req.params.id}):`, error);
    res.status(500).json({
      success: false,
      error: `发布内容失败: ${error.message}`,
    });
  }
});

/**
 * 下架内容
 * PUT /api/contents/:id/unpublish
 */
router.put("/:id/unpublish", authenticate, async (req, res) => {
  try {
    const content = await db.Content.findByPk(req.params.id);

    if (!content) {
      return res.status(404).json({
        success: false,
        error: "素材不存在",
      });
    }

    // 检查用户是否为内容所有者或管理员
    if (
      content.userId &&
      content.userId !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return res.status(403).json({
        success: false,
        error: "权限不足，您不是内容所有者",
      });
    }

    // 更新状态为草稿
    await content.update({
      status: "draft",
      userId: content.userId || req.user.id, // 如果内容没有所有者，设置为当前用户
    });

    res.json({
      success: true,
      content,
      message: "内容已下架",
    });
  } catch (error) {
    console.error(`下架内容失败 (ID: ${req.params.id}):`, error);
    res.status(500).json({
      success: false,
      error: `下架内容失败: ${error.message}`,
    });
  }
});

/**
 * 记录内容观看
 * POST /api/contents/:id/views
 */
router.post("/:id/views", async (req, res) => {
  try {
    const { viewDuration = 0 } = req.body;
    const contentId = req.params.id;
    
    // 获取用户信息（如果已登录）
    let userId = null;
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith("Bearer ")) {
      try {
        const token = authHeader.split(" ")[1];
        const result = await authService.verifyToken(token);
        if (result.success) {
          userId = result.decoded.id;
        }
      } catch (error) {
        // 忽略认证错误，继续记录观看
      }
    }

    // 获取IP地址
    const ipAddress = req.ip || req.connection.remoteAddress;

    // 记录观看
    await db.ContentView.create({
      contentId,
      userId,
      ipAddress,
      viewDuration,
    });

    res.json({
      success: true,
      message: "观看记录已保存",
    });
  } catch (error) {
    console.error(`记录观看失败 (ID: ${req.params.id}):`, error);
    res.status(500).json({
      success: false,
      error: `记录观看失败: ${error.message}`,
    });
  }
});

module.exports = router;
