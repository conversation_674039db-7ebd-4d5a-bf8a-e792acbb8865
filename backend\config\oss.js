/**
 * 阿里云OSS配置文件
 * 使用环境变量配置OSS连接
 */
require("dotenv").config();
const OSS = require("ali-oss");

// 验证必要的环境变量
const requiredEnvVars = [
  "OSS_REGION",
  "OSS_ACCESS_KEY",
  "OSS_SECRET_KEY",
  "OSS_BUCKET",
  "OSS_ENDPOINT",
];
const missingEnvVars = requiredEnvVars.filter(
  (varName) => !process.env[varName]
);

if (missingEnvVars.length > 0) {
  console.warn(`警告: 缺少OSS配置环境变量: ${missingEnvVars.join(", ")}`);
  console.warn("OSS功能可能无法正常工作。请在.env文件中设置这些变量。");
}

// 创建OSS客户端实例
const ossClient = new OSS({
  region: process.env.OSS_REGION, // 例如：'oss-cn-hongkong'
  accessKeyId: process.env.OSS_ACCESS_KEY,
  accessKeySecret: process.env.OSS_SECRET_KEY,
  bucket: process.env.OSS_BUCKET, // 存储桶名称
  endpoint: process.env.OSS_ENDPOINT, // 例如：'oss-cn-hongkong.aliyuncs.com'
  secure: true, // 使用HTTPS
});

module.exports = ossClient;
