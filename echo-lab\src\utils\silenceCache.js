const silenceCache = new Map();

export function getCachedSilenceUrl(duration) {
  const key = Math.round(duration * 1000) / 1000;
  
  if (silenceCache.has(key)) {
    return silenceCache.get(key);
  }

  const samples = Math.ceil(44100 * duration);
  const buffer = new ArrayBuffer(44 + samples * 2);
  const view = new DataView(buffer);
  
  // WAV header
  'RIFF'.split('').forEach((c, i) => view.setUint8(i, c.charCodeAt(0)));
  view.setUint32(4, 36 + samples * 2, true);
  'WAVE'.split('').forEach((c, i) => view.setUint8(8 + i, c.charCodeAt(0)));
  'fmt '.split('').forEach((c, i) => view.setUint8(12 + i, c.charCodeAt(0)));
  view.setUint32(16, 16, true);
  view.setUint16(20, 1, true);
  view.setUint16(22, 1, true);
  view.setUint32(24, 44100, true);
  view.setUint32(28, 88200, true);
  view.setUint16(32, 2, true);
  view.setUint16(34, 16, true);
  'data'.split('').forEach((c, i) => view.setUint8(36 + i, c.charCodeAt(0)));
  view.setUint32(40, samples * 2, true);
  
  const blob = new Blob([buffer], { type: "audio/wav" });
  const url = URL.createObjectURL(blob);
  
  silenceCache.set(key, url);
  return url;
}


