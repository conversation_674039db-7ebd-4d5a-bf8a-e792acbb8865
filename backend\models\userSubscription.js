/**
 * 用户订阅模型
 * 记录用户的订阅历史和状态
 */
module.exports = (sequelize, DataTypes) => {
  const UserSubscription = sequelize.define(
    "UserSubscription",
    {
      // 用户ID
      userId: {
        type: DataTypes.STRING(21),
        allowNull: false,
        field: 'user_id',
        comment: '用户ID'
      },
      
      // 订阅等级
      level: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: '订阅等级'
      },
      
      // 开始日期
      startDate: {
        type: DataTypes.DATE,
        allowNull: false,
        field: 'start_date',
        comment: '开始日期'
      },
      
      // 结束日期
      endDate: {
        type: DataTypes.DATE,
        allowNull: true,
        field: 'end_date',
        comment: '结束日期，NULL表示永久'
      },
      
      // 订阅状态
      status: {
        type: DataTypes.ENUM('active', 'expired', 'cancelled'),
        allowNull: false,
        defaultValue: 'active',
        comment: '订阅状态'
      },
      
      // 支付ID
      paymentId: {
        type: DataTypes.STRING(100),
        allowNull: true,
        field: 'payment_id',
        comment: '支付ID'
      }
    },
    {
      tableName: "user_subscriptions",
      underscored: true,
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      indexes: [
        {
          fields: ['user_id']
        },
        {
          fields: ['level']
        },
        {
          fields: ['status']
        },
        {
          fields: ['end_date']
        }
      ]
    }
  );

  UserSubscription.associate = function(models) {
    // 与用户表关联
    UserSubscription.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
  };

  return UserSubscription;
};
