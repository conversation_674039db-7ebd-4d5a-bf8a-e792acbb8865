# Echo Lab 编辑器 JSON 数据结构

本文档详细说明了 Echo Lab 编辑器生成的 JSON 数据结构，包括节点数据、连接数据、资源数据和项目整体结构。

## 整体结构

Echo Lab 编辑器生成的 JSON 数据主要包含以下几个部分：

```json
{
  "nodes": {
    // 节点数据，键为节点ID，值为节点对象
  },
  "nodeTypeCounters": {
    // 节点类型计数器，用于生成节点ID
  },
  "resources": {
    // 资源数据，包括标注、翻译和音频资源
  }
}
```

## 节点数据结构

每个节点都是一个声明式配置对象，具有以下基本结构：

```json
{
  "id": "textContent_123abc",  // 节点唯一标识符（类型_UUID）
  "type": "textContent",       // 节点类型
  "customName": "日语文本",     // 节点自定义名称（可选）
  "sourceIds": ["textContent_456def"], // 源节点ID数组
  "position": { "x": 100, "y": 200 },  // 节点在画布上的位置
  "params": {                  // 节点参数，特定于节点类型
    // 特定于节点类型的参数
  },
  "_timestamp": 1623456789000  // 节点最后更新时间戳
}
```

## 常见节点类型及其参数

### 文本内容节点 (textContent)

```json
{
  "id": "textContent_123abc",
  "type": "textContent",
  "params": {
    "text": "今日は良い天気ですね。\n日本語の勉強は楽しいです。",
    "mode": "paragraph",  // 分句模式：paragraph（段落）、sentence（句子）、none（不分句）
    "splitBy": "\n",      // 分隔符，用于分句
    "segments": [         // 分句结果
      {
        "id": "seg_abc123",
        "content": "今日は良い天気ですね。",
        "language": "ja"
      },
      {
        "id": "seg_def456",
        "content": "日本語の勉強は楽しいです。",
        "language": "ja"
      }
    ],
    "segmentsCustomized": false  // 是否自定义分句
  },
  "sourceIds": [],
  "position": {"x": 100, "y": 100}
}
```

### 文本序列节点 (textSequence)

```json
{
  "id": "textSequence_123abc",
  "type": "textSequence",
  "params": {
    "sequence": [  // 序列数据
      {
        "id": "seq_abc123",
        "sourceId": "seg_abc123",
        "content": "今日は良い天気ですね。",
        "language": "ja",
        "type": "text"
      },
      {
        "id": "seq_def456",
        "sourceId": "seg_def456",
        "content": "日本語の勉強は楽しいです。",
        "language": "ja",
        "type": "text"
      }
    ],
    "sections": [  // 环节数据
      {
        "id": "section_read_123",
        "title": "通读环节: 基于序列",
        "type": "readthrough",
        "description": "默认通读环节",
        "processingMode": "sequence",  // 处理方式：sequence（基于序列）、source（基于源节点）
        "userEditable": true,
        "enabled": true,
        "config": {
          "speed": 1.0,  // 播放速度
          "interval": 1000  // 停顿时长（毫秒）
        }
      },
      {
        "id": "section_repeat_456",
        "title": "重复练习: 基于序列",
        "type": "repeat",
        "description": "重复练习环节",
        "processingMode": "sequence",
        "userEditable": true,
        "enabled": true,
        "config": {
          "speed": 1.0,  // 播放速度
          "interval": 1000,  // 停顿时长（毫秒）
          "repeatCount": 3,  // 重复次数
          "insertTranslation": true,  // 是否插入翻译
          "translationLanguage": "zh-CN"  // 翻译语言
        }
      }
    ]
  },
  "sourceIds": ["textContent_123abc"],
  "position": {"x": 300, "y": 100}
}
```

### 资源管理节点 (resource)

```json
{
  "id": "resource_123abc",
  "type": "resource",
  "params": {
    "annotation": {
      "enabled": true,
      "method": "furigana"  // 标注方法
    },
    "translation": {
      "enabled": true,
      "targets": ["zh-CN", "en-US"]  // 目标语言
    },
    "audio": {
      "enabled": true,
      "service": "auto",  // 语音服务
      "voice": "auto"     // 语音声音
    }
  },
  "sourceIds": ["textSequence_123abc"],
  "position": {"x": 500, "y": 100}
}
```

### 视频配置节点 (videoConfig)

```json
{
  "id": "videoConfig_123abc",
  "type": "videoConfig",
  "params": {
    "background": {
      "type": "color",  // 背景类型：color（颜色）、image（图片）
      "color": "#000000",  // 背景颜色
      "opacity": 1.0  // 不透明度
    },
    "size": {
      "width": 1280,  // 视频宽度
      "height": 720   // 视频高度
    },
    "metadata": {
      "title": "日语学习视频",
      "description": "基础日语对话练习",
      "tags": ["日语", "学习", "对话"]
    },
    "font": {
      "family": "sans-serif",  // 字体系列
      "size": 32,              // 字体大小
      "color": "#FFFFFF",      // 字体颜色
      "weight": "normal",      // 字体粗细
      "shadow": true,          // 是否启用阴影
      "shadowColor": "#000000" // 阴影颜色
    },
    "layout": {
      "position": "center",  // 内容位置
      "maxWidth": 800,       // 内容最大宽度
      "padding": 40          // 内容内边距
    }
  },
  "sourceIds": ["resource_123abc"],
  "position": {"x": 700, "y": 100}
}
```

## 连接数据结构

连接数据不是直接存储在 JSON 中的，而是通过节点的 `sourceIds` 字段隐式表示。每个节点的 `sourceIds` 数组包含了该节点的所有输入源节点的 ID。

例如，如果节点 A 的 ID 是 "textContent_123"，节点 B 的 ID 是 "textSequence_456"，且 B 连接了 A 作为输入源，那么 B 的 `sourceIds` 数组中会包含 A 的 ID：

```json
{
  "id": "textSequence_456",
  "type": "textSequence",
  "sourceIds": ["textContent_123"],
  // 其他字段...
}
```

## 资源数据结构

资源数据包括标注、翻译和音频资源，存储在 JSON 的 `resources` 字段中：

```json
"resources": {
  "annotations": {
    "seg_abc123": "今日（きょう）は良（よ）い天気（てんき）ですね。"
  },
  "translations": {
    "zh-CN": {
      "seg_abc123": "今天天气真好。",
      "seg_def456": "学习日语很有趣。"
    },
    "en-US": {
      "seg_abc123": "The weather is nice today.",
      "seg_def456": "Learning Japanese is fun."
    }
  },
  "audioItems": [
    {
      "id": "audio_abc123",
      "text": "今日は良い天気ですね。",
      "language": "ja",
      "audioUrl": "https://example.com/audio/abc123.mp3",
      "duration": 2.5  // 音频时长（秒）
    },
    {
      "id": "audio_def456",
      "text": "日本語の勉強は楽しいです。",
      "language": "ja",
      "audioUrl": "https://example.com/audio/def456.mp3",
      "duration": 3.0
    }
  ]
}
```

## 文本序列节点的特定数据结构

### 序列数据 (Sequence)

序列是一系列有序的文本内容项的集合，每个内容项通常包含以下属性：

```json
{
  "id": "seq_abc123",  // 序列项ID
  "sourceId": "seg_abc123",  // 源分句ID
  "content": "今日は良い天気ですね。",  // 文本内容
  "language": "ja",  // 语言代码
  "type": "text"  // 内容类型
}
```

### 环节数据 (Sections)

环节定义了内容的播放方式，包括通读和重复练习：

#### 通读环节

```json
{
  "id": "section_read_123",
  "title": "通读环节: 基于序列",
  "type": "readthrough",
  "description": "默认通读环节",
  "processingMode": "sequence",  // 处理方式：sequence（基于序列）、source（基于源节点）
  "sourceNodeId": null,  // 源节点ID，仅当processingMode为source时有效
  "userEditable": true,
  "enabled": true,
  "config": {
    "speed": 1.0,  // 播放速度
    "interval": 1000  // 停顿时长（毫秒）
  }
}
```

#### 重复练习环节

```json
{
  "id": "section_repeat_456",
  "title": "重复练习: 基于序列",
  "type": "repeat",
  "description": "重复练习环节",
  "processingMode": "sequence",
  "sourceNodeId": null,
  "userEditable": true,
  "enabled": true,
  "config": {
    "speed": 1.0,  // 播放速度
    "interval": 1000,  // 停顿时长（毫秒）
    "repeatCount": 3,  // 重复次数
    "insertTranslation": true,  // 是否插入翻译
    "translationLanguage": "zh-CN"  // 翻译语言
  }
}
```

## 完整示例

以下是一个完整的 JSON 数据结构示例，包含了文本内容节点、文本序列节点、资源管理节点和视频配置节点：

```json
{
  "nodes": {
    "textContent_123abc": {
      "id": "textContent_123abc",
      "type": "textContent",
      "customName": "日语文本",
      "params": {
        "text": "今日は良い天気ですね。\n日本語の勉強は楽しいです。",
        "mode": "paragraph",
        "splitBy": "\n",
        "segments": [
          {
            "id": "seg_abc123",
            "content": "今日は良い天気ですね。",
            "language": "ja"
          },
          {
            "id": "seg_def456",
            "content": "日本語の勉強は楽しいです。",
            "language": "ja"
          }
        ],
        "segmentsCustomized": false
      },
      "sourceIds": [],
      "position": {"x": 100, "y": 100},
      "_timestamp": 1623456789000
    },
    "textSequence_456def": {
      "id": "textSequence_456def",
      "type": "textSequence",
      "customName": "日语序列",
      "params": {
        "sequence": [
          {
            "id": "seq_abc123",
            "sourceId": "seg_abc123",
            "content": "今日は良い天気ですね。",
            "language": "ja",
            "type": "text"
          },
          {
            "id": "seq_def456",
            "sourceId": "seg_def456",
            "content": "日本語の勉強は楽しいです。",
            "language": "ja",
            "type": "text"
          }
        ],
        "sections": [
          {
            "id": "section_read_123",
            "title": "通读环节: 基于序列",
            "type": "readthrough",
            "description": "默认通读环节",
            "processingMode": "sequence",
            "userEditable": true,
            "enabled": true,
            "config": {
              "speed": 1.0,
              "interval": 1000
            }
          },
          {
            "id": "section_repeat_456",
            "title": "重复练习: 基于序列",
            "type": "repeat",
            "description": "重复练习环节",
            "processingMode": "sequence",
            "userEditable": true,
            "enabled": true,
            "config": {
              "speed": 1.0,
              "interval": 1000,
              "repeatCount": 3,
              "insertTranslation": true,
              "translationLanguage": "zh-CN"
            }
          }
        ]
      },
      "sourceIds": ["textContent_123abc"],
      "position": {"x": 300, "y": 100},
      "_timestamp": 1623456790000
    },
    "resource_789ghi": {
      "id": "resource_789ghi",
      "type": "resource",
      "customName": "资源管理",
      "params": {
        "annotation": {
          "enabled": true,
          "method": "furigana"
        },
        "translation": {
          "enabled": true,
          "targets": ["zh-CN", "en-US"]
        },
        "audio": {
          "enabled": true,
          "service": "auto",
          "voice": "auto"
        }
      },
      "sourceIds": ["textSequence_456def"],
      "position": {"x": 500, "y": 100},
      "_timestamp": 1623456791000
    },
    "videoConfig_012jkl": {
      "id": "videoConfig_012jkl",
      "type": "videoConfig",
      "customName": "视频配置",
      "params": {
        "background": {
          "type": "color",
          "color": "#000000",
          "opacity": 1.0
        },
        "size": {
          "width": 1280,
          "height": 720
        },
        "metadata": {
          "title": "日语学习视频",
          "description": "基础日语对话练习",
          "tags": ["日语", "学习", "对话"]
        },
        "font": {
          "family": "sans-serif",
          "size": 32,
          "color": "#FFFFFF",
          "weight": "normal",
          "shadow": true,
          "shadowColor": "#000000"
        },
        "layout": {
          "position": "center",
          "maxWidth": 800,
          "padding": 40
        }
      },
      "sourceIds": ["resource_789ghi"],
      "position": {"x": 700, "y": 100},
      "_timestamp": 1623456792000
    }
  },
  "nodeTypeCounters": {
    "textContent": 1,
    "textSequence": 1,
    "resource": 1,
    "videoConfig": 1
  },
  "resources": {
    "annotations": {
      "seg_abc123": "今日（きょう）は良（よ）い天気（てんき）ですね。",
      "seg_def456": "日本語（にほんご）の勉強（べんきょう）は楽（たの）しいです。"
    },
    "translations": {
      "zh-CN": {
        "seg_abc123": "今天天气真好。",
        "seg_def456": "学习日语很有趣。"
      },
      "en-US": {
        "seg_abc123": "The weather is nice today.",
        "seg_def456": "Learning Japanese is fun."
      }
    },
    "audioItems": [
      {
        "id": "audio_abc123",
        "text": "今日は良い天気ですね。",
        "language": "ja",
        "audioUrl": "https://example.com/audio/abc123.mp3",
        "duration": 2.5
      },
      {
        "id": "audio_def456",
        "text": "日本語の勉強は楽しいです。",
        "language": "ja",
        "audioUrl": "https://example.com/audio/def456.mp3",
        "duration": 3.0
      }
    ]
  }
}
```
