/**
 * 认证中间件
 * 用于保护需要登录的API路由
 */
const authService = require("../services/authService");
const db = require("../models");

/**
 * 验证用户是否已登录
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 * @param {Function} next 下一个中间件
 */
async function authenticate(req, res, next) {
  try {
    // 首先尝试从请求头中获取令牌
    let token = null;
    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith("Bearer ")) {
      token = authHeader.split(" ")[1];
      console.log("[AuthMiddleware] authenticate: 从请求头提取到令牌");
    }
    // 如果请求头中没有令牌，尝试从查询参数中获取
    else if (req.query.token) {
      token = req.query.token;
      console.log("[AuthMiddleware] authenticate: 从查询参数提取到令牌");
    }

    // 如果没有找到令牌，返回未授权错误
    if (!token) {
      console.log("[AuthMiddleware] authenticate: 未提供令牌");
      return res.status(401).json({
        success: false,
        error: "未授权，请先登录",
      });
    }

    // 获取设备信息和IP地址
    const userAgent = req.headers["user-agent"] || "Unknown";
    const ipAddress = req.ip || req.connection.remoteAddress || "Unknown";

    // 验证令牌和会话
    console.log("[AuthMiddleware] authenticate: 开始验证令牌");
    const result = await authService.verifyToken(token);

    if (!result.success) {
      console.log(
        `[AuthMiddleware] authenticate: 令牌验证失败: ${result.error}`
      );

      // 添加更详细的错误信息
      let errorMessage = result.error;
      if (
        result.error.includes("会话已过期") ||
        result.error.includes("会话无效")
      ) {
        errorMessage = "您的会话已过期或在其他设备登录，请重新登录";
      }

      return res.status(401).json({
        success: false,
        error: errorMessage,
        code: result.error.includes("会话")
          ? "SESSION_EXPIRED"
          : "TOKEN_INVALID",
      });
    }

    console.log(
      `[AuthMiddleware] authenticate: 令牌验证成功，用户ID: ${result.decoded.id}`
    );

    // 将用户信息添加到请求对象
    req.user = result.decoded;
    next();
  } catch (error) {
    console.error("[AuthMiddleware] 认证中间件错误:", error);
    return res.status(500).json({
      success: false,
      error: "服务器内部错误",
    });
  }
}

/**
 * 验证用户是否为管理员
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 * @param {Function} next 下一个中间件
 */
async function isAdmin(req, res, next) {
  try {
    // 先验证用户是否已登录
    await new Promise((resolve, reject) => {
      authenticate(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    // 检查用户角色
    if (req.user.role !== "admin") {
      return res.status(403).json({
        success: false,
        error: "权限不足，需要管理员权限",
      });
    }
    next();
  } catch (error) {
    // 认证中间件已经处理了错误响应，这里不需要额外处理
  }
}

/**
 * 验证用户是否为内容所有者
 * @param {string} contentIdParam 内容ID参数名
 * @returns {Function} 中间件函数
 */
function isContentOwner(contentIdParam = "id") {
  return async (req, res, next) => {
    try {
      // 先验证用户是否已登录
      await new Promise((resolve, reject) => {
        authenticate(req, res, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });

      // 获取内容ID
      const contentId = req.params[contentIdParam];
      if (!contentId) {
        return res.status(400).json({
          success: false,
          error: "缺少内容ID",
        });
      }

      // 查找内容
      const content = await db.Content.findByPk(contentId);
      if (!content) {
        return res.status(404).json({
          success: false,
          error: "内容不存在",
        });
      }

      // 检查用户是否为内容所有者或管理员
      if (content.userId !== req.user.id && req.user.role !== "admin") {
        return res.status(403).json({
          success: false,
          error: "权限不足，您不是内容所有者",
        });
      }

      next();
    } catch (error) {
      console.error("验证内容所有者失败:", error);
      // 如果是认证错误，认证中间件已经处理了响应
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          error: `验证内容所有者失败: ${error.message}`,
        });
      }
    }
  };
}

/**
 * 可选认证中间件
 * 如果用户已登录，则添加用户信息到请求对象
 * 如果用户未登录，则继续处理请求
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 * @param {Function} next 下一个中间件
 */
async function optionalAuthenticate(req, res, next) {
  try {
    // 从请求头中获取令牌
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      // 用户未提供令牌，继续处理请求
      console.log(
        "[AuthMiddleware] optionalAuthenticate: 未提供令牌，继续处理请求"
      );
      return next();
    }

    // 提取令牌
    const token = authHeader.split(" ")[1];
    console.log("[AuthMiddleware] optionalAuthenticate: 提取到令牌");

    // 验证令牌
    const result = await authService.verifyToken(token);

    if (!result.success) {
      // 令牌无效，但这是可选的，所以继续处理请求
      console.log(
        `[AuthMiddleware] optionalAuthenticate: 令牌验证失败: ${result.error}，继续处理请求`
      );
      return next();
    }

    // 令牌有效，将用户信息添加到请求对象
    console.log(
      `[AuthMiddleware] optionalAuthenticate: 令牌验证成功，用户ID: ${result.decoded.id}`
    );
    req.user = result.decoded;
    next();
  } catch (error) {
    // 出错了，但这是可选的，所以继续处理请求
    console.error("[AuthMiddleware] 可选认证中间件错误:", error);
    next();
  }
}

module.exports = {
  authenticate,
  isAdmin,
  isContentOwner,
  optionalAuthenticate,
};
