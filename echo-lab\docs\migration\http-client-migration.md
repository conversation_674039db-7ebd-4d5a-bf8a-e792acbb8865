# HTTP客户端统一迁移指南

本文档说明如何将现有的网络请求代码迁移到统一的HTTP客户端。

## 🎯 迁移目标

- 统一所有网络请求的入口
- 消除重复的拦截器设置
- 提供一致的错误处理
- 简化API调用方式

## 📊 迁移前后对比

### 迁移前（多种方式）

```javascript
// 方式1：直接使用axios
import axios from 'axios';
const response = await axios.get('/api/contents');

// 方式2：使用api实例
import api from '@/services/api';
const response = await api.get('/api/contents');

// 方式3：使用apiRequest工具
import { apiRequest } from '@/utils/apiRequest';
const response = await apiRequest('/api/contents');
```

### 迁移后（统一方式）

```javascript
// 统一使用httpClient
import httpClient from '@/utils/httpClient';
const response = await httpClient.get('/api/contents');

// 或者使用API端点常量
import { API_ENDPOINTS } from '@/config/api';
const response = await httpClient.get(API_ENDPOINTS.CONTENTS.BASE);
```

## 🔧 具体迁移步骤

### 1. 更新导入语句

**旧代码：**
```javascript
import axios from 'axios';
import api from '@/services/api';
import { apiRequest } from '@/utils/apiRequest';
```

**新代码：**
```javascript
import httpClient from '@/utils/httpClient';
import { API_ENDPOINTS } from '@/config/api';
```

### 2. 更新API调用

**旧代码：**
```javascript
// GET请求
const response = await axios.get('/api/contents');
const data = response.data;

// POST请求
const response = await api.post('/api/contents', { name: 'test' });
const result = response.data;

// 文件上传
const response = await uploadFile('/api/images/upload', formData);
```

**新代码：**
```javascript
// GET请求
const data = await httpClient.get(API_ENDPOINTS.CONTENTS.BASE);

// POST请求
const result = await httpClient.post(API_ENDPOINTS.CONTENTS.BASE, { name: 'test' });

// 文件上传
const result = await httpClient.upload(API_ENDPOINTS.IMAGES.UPLOAD, formData);
```

### 3. 错误处理

**旧代码：**
```javascript
try {
  const response = await axios.get('/api/contents');
  return response.data;
} catch (error) {
  console.error('请求失败:', error);
  ElMessage.error('获取内容失败');
  throw error;
}
```

**新代码：**
```javascript
try {
  // 错误处理已在httpClient中统一处理
  const data = await httpClient.get(API_ENDPOINTS.CONTENTS.BASE);
  return data;
} catch (error) {
  // 只需要处理业务逻辑错误
  throw error;
}
```

## 📝 服务文件迁移示例

### 内容服务迁移

**旧代码：**
```javascript
import axios from 'axios';

export default {
  async getContents() {
    const response = await axios.get('/api/contents');
    return response.data;
  },

  async createContent(data) {
    const response = await axios.post('/api/contents', data);
    return response.data;
  }
};
```

**新代码：**
```javascript
import httpClient from '@/utils/httpClient';
import { API_ENDPOINTS } from '@/config/api';

export default {
  async getContents() {
    return await httpClient.get(API_ENDPOINTS.CONTENTS.BASE);
  },

  async createContent(data) {
    return await httpClient.post(API_ENDPOINTS.CONTENTS.BASE, data);
  }
};
```

## 🚫 需要废弃的文件和方法

### 1. 废弃的文件
- `src/utils/apiRequest.js` - 使用 `httpClient` 代替
- 各服务文件中的独立axios实例

### 2. 废弃的方法
- `setupAuthInterceptors()` - 拦截器已在httpClient中统一设置
- 各种自定义的错误处理函数

## ✅ 迁移检查清单

- [ ] 更新所有服务文件的导入语句
- [ ] 替换所有axios调用为httpClient调用
- [ ] 使用API_ENDPOINTS常量替换硬编码URL
- [ ] 移除重复的错误处理代码
- [ ] 移除重复的拦截器设置
- [ ] 测试所有API调用功能
- [ ] 更新相关文档

## 🔄 兼容性说明

为了确保平滑迁移，我们保留了以下兼容性：

1. **API端点常量**：保留旧的常量名称，但建议使用新的 `API_ENDPOINTS` 结构
2. **响应格式**：httpClient返回的数据格式与原来保持一致
3. **错误处理**：保持原有的错误抛出机制

## ✅ 迁移完成状态

### 已完成迁移的服务文件：
- ✅ `src/services/api.js` - 核心API服务
- ✅ `src/services/authService.js` - 认证服务
- ✅ `src/services/contentService.js` - 内容服务
- ✅ `src/services/publicContentService.js` - 公开内容服务
- ✅ `src/services/feedbackService.js` - 反馈服务
- ✅ `src/services/templateService.js` - 模板服务
- ✅ `src/services/specialWordService.js` - 特殊词汇服务
- ✅ `src/services/imageService.js` - 图片服务
- ✅ `src/services/featurePermissionService.js` - 功能权限服务
- ❌ `src/services/videoService.js` - 视频服务（已删除，改用前端WebCodecs）
- ✅ `src/services/sessionService.js` - 会话管理服务
- ✅ `src/services/userLevelService.js` - 用户等级服务
- ✅ `src/services/adminService.js` - 管理员服务

### 已删除的废弃文件：
- ❌ `src/utils/apiRequest.js` - 已完全删除，所有引用已迁移到httpClient

### 已更新的配置文件：
- ✅ `src/config/api.js` - 新增API_ENDPOINTS结构
- ✅ `src/main.js` - 移除旧的拦截器设置

## 🎯 迁移效果

### 统一前（问题）：
- 🔴 **多种请求方式**：axios、api实例、apiRequest工具
- 🔴 **重复拦截器**：认证拦截器在多个地方设置
- 🔴 **错误处理分散**：每个服务都有自己的错误处理
- 🔴 **维护困难**：修改请求逻辑需要改多个地方

### 统一后（优势）：
- ✅ **单一入口**：所有请求都通过httpClient
- ✅ **统一拦截器**：认证和错误处理集中管理
- ✅ **一致的API**：所有服务使用相同的请求方法
- ✅ **易于维护**：修改请求逻辑只需改一个地方

## 📚 相关文档

- [HTTP客户端API文档](../api/http-client.md)
- [API端点配置文档](../api/endpoints.md)
- [错误处理指南](../guides/error-handling.md)
