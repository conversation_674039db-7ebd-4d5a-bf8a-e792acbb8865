# 前端视频导出架构

本文档说明了Echo Lab项目移除后端视频合成逻辑后的纯前端视频导出架构。

## 🎯 架构概述

### **纯前端导出流程**
```
用户点击导出 → 检查WebCodecs支持 → 前端视频合成 → 直接下载
```

### **技术栈**
- **WebCodecs API**: 视频/音频编码
- **MP4Box.js**: MP4容器封装
- **Canvas API**: 视频帧渲染
- **Vue 3**: 组件渲染和状态管理

## 🔧 核心组件

### **1. VideoExportDialog.vue**
主要的视频导出对话框组件，负责：
- 用户界面和选项配置
- 导出流程控制
- 进度显示和错误处理
- 浏览器兼容性检查

### **2. frontendVideoExporter.js**
前端视频导出核心逻辑：
- 导出流程协调
- 浏览器支持检测
- 文件下载处理

### **3. webCodecsVideoExporter.js**
WebCodecs视频合成引擎：
- 视频/音频编码
- MP4容器生成
- 音频采样率处理

## 📊 导出流程

### **阶段划分**
```javascript
const phases = {
  'preparing': '准备导出',
  'generating-frames': '生成视频帧',
  'initializing-encoder': '初始化编码器',
  'encoding-video': '编码视频',
  'encoding-audio': '编码音频',
  'finalizing': '完成处理',
  'complete': '完成',
  'cancelled': '已取消',
  'error': '出错'
};
```

### **详细流程**
1. **准备阶段** (0-5%)
   - 验证输入数据
   - 检查浏览器支持
   - 初始化AbortController

2. **生成帧阶段** (5-40%)
   - 渲染时间线内容为Canvas
   - 生成视频帧序列
   - 处理封面图片和文本

3. **初始化编码器** (40-45%)
   - 创建VideoEncoder和AudioEncoder
   - 配置编码参数
   - 初始化MP4Muxer

4. **编码视频** (45-75%)
   - 将Canvas帧编码为H.264
   - 处理帧率和时间戳
   - 实时进度更新

5. **编码音频** (75-90%)
   - 处理音频采样率转换
   - 编码为AAC格式
   - 同步音视频时间轴

6. **完成处理** (90-100%)
   - 生成MP4文件
   - 创建字幕文件
   - 准备下载

## 🎵 音频处理

### **采样率策略**
```javascript
// 编码器配置 - 固定使用48kHz
const audioConfig = {
  codec: 'mp4a.40.2',
  sampleRate: 48000,  // WebCodecs支持的标准采样率
  numberOfChannels: 1,
  bitrate: 128000
};

// 时间戳计算 - 使用原始采样率
timestamp += (frameSize / originalSampleRate) * 1000000;
```

### **处理逻辑**
- **编码器采样率**: 固定48kHz (WebCodecs要求)
- **时间戳计算**: 使用原始24kHz采样率
- **无需重采样**: 保持原始音频数据不变

## 🖼️ 视频渲染

### **内容渲染**
```javascript
const renderContent = async (item) => {
  // 创建容器
  const container = document.createElement('div');
  container.style.width = `${width}px`;
  container.style.height = `${height}px`;
  
  // 使用Vue渲染ContentDisplay组件
  const app = createApp({
    render() {
      return Vue.h(ContentDisplay, {
        item: normalizedItem,
        textStyle: videoConfig.textStyle
      });
    }
  });
  
  app.mount(container);
  return container;
};
```

### **质量配置**
```javascript
const qualityPresets = {
  low: { width: 854, height: 480, bitrate: 1000000 },
  medium: { width: 1280, height: 720, bitrate: 2500000 },
  high: { width: 1920, height: 1080, bitrate: 5000000 }
};
```

## 🚫 移除的后端逻辑

### **已删除的功能**
- ❌ 后端FFmpeg视频合成
- ❌ 文件上传到服务器
- ❌ 服务器端视频处理
- ❌ ZIP文件打包上传
- ❌ 任务队列和进度轮询
- ❌ 服务器端字幕生成

### **已删除的组件**
- ❌ `videoService.js` 相关调用
- ❌ `audioBufferToWav` 转换
- ❌ JSZip文件打包
- ❌ 后端任务状态管理
- ❌ 服务器取消API调用

### **已删除的状态**
```javascript
// 移除的状态变量
- currentJobId
- isUploadedToServer
- phaseAllocation
- 后端相关的phase常量
```

## ✅ 浏览器兼容性

### **支持检测**
```javascript
export function shouldUseFrontendExport() {
  return !!(
    window.VideoEncoder &&
    window.AudioEncoder &&
    window.MP4Box
  );
}
```

### **兼容性要求**
- **Chrome 94+**: 完整WebCodecs支持
- **Edge 94+**: 完整WebCodecs支持
- **Firefox**: 不支持 (需要实验性标志)
- **Safari**: 不支持

### **降级策略**
- 不支持的浏览器显示警告信息
- 引导用户使用Chrome 94+
- 不提供后端降级选项

## 🎯 用户体验

### **优势**
- ✅ **无需上传**: 节省带宽和时间
- ✅ **本地处理**: 数据隐私保护
- ✅ **即时下载**: 处理完成立即可用
- ✅ **无服务器负载**: 降低运营成本

### **限制**
- ⚠️ **浏览器要求**: 仅支持Chrome 94+
- ⚠️ **性能依赖**: 依赖客户端硬件性能
- ⚠️ **内存占用**: 长视频可能消耗大量内存

## 🔧 配置选项

### **视频选项**
```javascript
const exportOptions = {
  videoQuality: 'medium',    // low/medium/high
  videoWidth: 1280,          // 自动根据质量设置
  videoHeight: 720,          // 自动根据质量设置
  fileName: 'echo_lab_video' // 用户可编辑
};
```

### **字幕选项**
```javascript
const subtitleOptions = {
  generateSubtitles: true,           // 是否生成字幕
  subtitleLanguages: ['ja', 'zh']    // 选择的语言列表
};
```

## 📈 性能优化

### **内存管理**
- 及时释放Canvas对象
- 清理Vue组件实例
- 避免大量帧缓存

### **进度反馈**
- 实时更新编码进度
- 分阶段显示处理状态
- 支持用户取消操作

### **错误处理**
- WebCodecs API错误捕获
- 浏览器兼容性检查
- 用户友好的错误提示

## 🚀 未来扩展

### **可能的改进**
- 支持更多视频格式
- 添加视频预览功能
- 优化大文件处理
- 支持批量导出

### **技术演进**
- 等待Firefox/Safari支持WebCodecs
- 考虑WebAssembly FFmpeg作为降级
- 探索Web Workers并行处理

## 📚 相关文档

- [WebCodecs视频导出器](./webcodecs-video-exporter.md)
- [音频采样率处理](./audio-sample-rate-handling.md)
- [前端视频导出器](./frontend-video-exporter.md)

## 🎉 总结

通过移除后端视频合成逻辑，Echo Lab实现了：

1. **简化架构**: 纯前端处理，无需服务器资源
2. **提升性能**: 本地处理，无网络传输延迟
3. **降低成本**: 减少服务器负载和带宽消耗
4. **保护隐私**: 数据完全在本地处理

这种架构特别适合现代浏览器环境，为用户提供了快速、安全、高效的视频导出体验。
