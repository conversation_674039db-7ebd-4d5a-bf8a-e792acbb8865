/**
 * TTS服务
 * 提供文本转语音功能，支持多种语言和缓存机制
 * 集成阿里云OSS存储和MySQL数据库
 */
const axios = require("axios");
const fs = require("fs");
const path = require("path");
const dotenv = require("dotenv");
const AipSpeech = require("baidu-aip-sdk").speech;
const crypto = require("crypto-js");
// music-metadata 是 ES 模块，需要动态导入
const pLimit = require("p-limit");
const db = require("../models");
const ossService = require("./ossService");

/**
 * 估算音频时长
 * @param {string} text - 文本内容
 * @param {string} language - 语言代码
 * @returns {number} 估算的音频时长（秒）
 */
function estimateAudioDuration(text, language = "auto") {
  if (!text) return 1; // 默认1秒

  // 根据语言选择不同的估算方法
  switch (language) {
    case "ja":
    case "jp":
      return estimateJapaneseDuration(text);
    case "zh":
    case "zh-CN":
    case "zh-TW":
      return estimateChineseDuration(text);
    case "en":
      return estimateEnglishDuration(text);
    default:
      return estimateDefaultDuration(text);
  }
}

/**
 * 估算日语音频时长
 */
function estimateJapaneseDuration(text) {
  const charactersPerSecond = 6;
  const baseDuration = 0.5;
  const cleanText = text.replace(/[\s\.,!?，。！？]/g, "");
  const characterCount = cleanText.length;
  const estimatedDuration = characterCount / charactersPerSecond + baseDuration;
  return Math.max(1, estimatedDuration);
}

/**
 * 估算中文音频时长
 */
function estimateChineseDuration(text) {
  const charactersPerSecond = 4.5;
  const baseDuration = 0.5;
  const cleanText = text.replace(/[\s\.,!?，。！？]/g, "");
  const characterCount = cleanText.length;
  const estimatedDuration = characterCount / charactersPerSecond + baseDuration;
  return Math.max(1, estimatedDuration);
}

/**
 * 估算英语音频时长
 */
function estimateEnglishDuration(text) {
  const wordsPerSecond = 2.75;
  const baseDuration = 0.5;
  const words = text.split(/\s+/).filter((word) => word.length > 0);
  const wordCount = words.length;
  const estimatedDuration = wordCount / wordsPerSecond + baseDuration;
  return Math.max(1, estimatedDuration);
}

/**
 * 默认音频时长估算
 */
function estimateDefaultDuration(text) {
  const charactersPerSecond = 5;
  const baseDuration = 0.5;
  const cleanText = text.replace(/\s/g, "");
  const characterCount = cleanText.length;
  const estimatedDuration = characterCount / charactersPerSecond + baseDuration;
  return Math.max(1, estimatedDuration);
}

/**
 * 从音频Buffer获取时长
 * @param {Buffer} audioBuffer - 音频数据
 * @returns {Promise<number>} 音频时长（秒）
 */
async function getAudioDurationFromBuffer(audioBuffer) {
  try {
    // 动态导入 music-metadata ES 模块
    const { parseBuffer } = await import("music-metadata");

    // 使用 music-metadata 解析音频元数据
    const metadata = await parseBuffer(audioBuffer);

    // 获取音频时长（秒）
    const duration = metadata.format.duration;

    if (!duration || duration <= 0) {
      throw new Error("无法获取有效的音频时长");
    }

    console.log(`[TTS] 音频时长: ${duration}秒`);
    return duration;
  } catch (err) {
    console.error(`[TTS] 获取音频时长失败: ${err.message}`);
    // 音频时长获取失败，认为音频生成失败
    throw new Error(`[TTS] 音频时长获取失败，音频可能损坏: ${err.message}`);
  }
}

// 加载环境变量
dotenv.config();

// 从环境变量获取API密钥
const GOOGLE_API_KEY =
  process.env.GOOGLE_API_KEY || "AIzaSyBOti4mM-6x9WDnZIjIeyEU21OpBXqWBgw";
const BAIDU_APP_ID = process.env.BAIDU_APP_ID || "118547411";
const BAIDU_API_KEY = process.env.BAIDU_API_KEY || "REi2kJMug7PysLSXD98MV3W6";
const BAIDU_SECRET_KEY =
  process.env.BAIDU_SECRET_KEY || "b6vdIzTNm1ZnIso1OtNE9dt4wScbtoA4";

// 创建百度语音客户端
const baiduClient = new AipSpeech(
  BAIDU_APP_ID,
  BAIDU_API_KEY,
  BAIDU_SECRET_KEY
);

// 限制并发请求数量 - 降低并发数以减轻服务器负载
const limit = pLimit(2);

/**
 * 计算缓存键
 * @param {string} text 文本内容
 * @param {string} language 语言代码
 * @param {number} speed 语速
 * @param {number} voiceDbId 声音数据库ID
 * @returns {string} MD5哈希值
 */
function calculateCacheKey(text, language, speed, voiceDbId) {
  const data = `${text}|${language}|${speed}|${voiceDbId}`;
  return crypto.MD5(data).toString();
}

/**
 * 获取或生成音频
 * 先检查缓存，如果缓存中没有，则生成新的音频
 * @param {string} text 文本内容
 * @param {string} language 语言代码
 * @param {number} speed 语速
 * @param {string} speaker 说话者
 * @param {string} cacheKey 可选的缓存键，如果不提供则自动计算
 * @param {number} voiceDbId 声音数据库ID，用于TTS服务和缓存键计算
 * @returns {Promise<Object>} 音频信息
 */
async function getOrGenerateAudio(
  text,
  language,
  speed,
  speaker,
  cacheKey = null,
  voiceDbId = null
) {
  try {
    // 检查必要参数
    if (!voiceDbId) {
      throw new Error(
        `[TTS] 声音数据库ID不能为空，说话人: ${speaker}, 语言: ${language}`
      );
    }

    // 计算缓存键（如果没有提供）
    const actualCacheKey =
      cacheKey || calculateCacheKey(text, language, speed, voiceDbId);

    // 查询数据库缓存
    const cachedAudio = await db.Audio.findOne({
      where: { md5Hash: actualCacheKey },
    });

    if (cachedAudio) {
      console.log("从缓存获取音频:", actualCacheKey);
      return {
        audioUrl: cachedAudio.ossUrl,
        duration: cachedAudio.duration,
        audioId: cachedAudio.id,
        fromCache: true, // 标记音频是从缓存获取的
      };
    }

    // 缓存未命中，生成新音频
    console.log("缓存未命中，生成新音频:", actualCacheKey);
    return generateAudio(
      text,
      language,
      speed,
      speaker,
      actualCacheKey,
      voiceDbId
    );
  } catch (error) {
    console.error("获取或生成音频失败:", error);
    throw error;
  }
}

/**
 * 获取声音信息（统一使用数据库ID）
 * @param {number} voiceDbId 声音数据库ID
 * @returns {Promise<Object|null>} 声音信息
 */
async function getVoiceInfo(voiceDbId) {
  try {
    // 统一使用数据库ID查询
    const voice = await db.Voice.findByPk(voiceDbId);

    if (!voice) {
      console.error(`[TTS] 未找到声音数据库ID为 ${voiceDbId} 的声音信息`);
      return null;
    }

    // 检查声音是否禁用
    if (voice.disabled) {
      console.error(`[TTS] 声音已禁用: ${voiceDbId}`);
      return null;
    }

    return voice;
  } catch (error) {
    console.error(`[TTS] 获取声音信息失败: ${error.message}`);
    return null;
  }
}

/**
 * 生成音频（忽略缓存模式，使用UPSERT操作）
 * @param {string} text 文本内容
 * @param {string} language 语言代码
 * @param {number} speed 语速
 * @param {string} speaker 说话者
 * @param {string} cacheKey 缓存键
 * @param {number} voiceDbId 声音数据库ID，用于TTS服务和缓存键计算
 * @returns {Promise<Object>} 音频信息
 */
async function generateAudioWithUpsert(
  text,
  language,
  speed,
  speaker,
  cacheKey,
  voiceDbId
) {
  try {
    console.log(
      `[TTS] 生成音频(UPSERT模式): 语言=${language}, 说话人=${speaker}, 声音数据库ID=${voiceDbId}`
    );

    // 检查必要参数
    if (!text) {
      throw new Error(`[TTS] 文本内容不能为空`);
    }

    if (!language) {
      throw new Error(`[TTS] 语言不能为空`);
    }

    if (!voiceDbId) {
      throw new Error(
        `[TTS] 声音数据库ID不能为空，说话人: ${speaker}, 语言: ${language}`
      );
    }

    // 如果没有提供缓存键，使用声音数据库ID计算
    if (!cacheKey) {
      cacheKey = calculateCacheKey(text, language, speed, voiceDbId);
    }

    // 获取声音信息（统一使用 getVoiceInfo 函数）
    const voiceInfo = await getVoiceInfo(voiceDbId);
    if (!voiceInfo) {
      throw new Error(`[TTS] 未找到声音数据库ID为 ${voiceDbId} 的声音信息`);
    }

    // 前端已处理特殊词汇，直接使用传入的文本
    let processedText = text;

    // 根据声音所属的服务选择TTS服务
    let audioBuffer;
    if (voiceInfo.service_id === "google") {
      // 使用Google TTS服务
      audioBuffer = await generateGoogleTTS(
        processedText,
        speed,
        speaker,
        3,
        2000,
        voiceInfo.speaker_id,
        voiceInfo.api_params
      );
    } else if (voiceInfo.service_id === "baidu") {
      // 使用百度TTS服务
      audioBuffer = await generateBaiduTTS(
        processedText,
        language,
        speed * 5,
        speaker,
        voiceInfo.speaker_id,
        voiceInfo.api_params
      );
    } else {
      throw new Error(`[TTS] 不支持的TTS服务: ${voiceInfo.service_id}`);
    }

    // 上传到OSS
    const ossResult = await ossService.uploadAudio(audioBuffer, language);

    // 获取音频时长
    const duration = await getAudioDurationFromBuffer(audioBuffer);

    // 如果时长为0或无效，也认为是失败
    if (!duration || duration <= 0) {
      throw new Error(`[TTS] 音频时长无效: ${duration}秒`);
    }

    // 使用UPSERT操作保存到数据库（如果存在则更新，不存在则创建）
    const [audio, created] = await db.Audio.upsert(
      {
        text,
        language,
        speed,
        voice_db_id: voiceDbId, // 使用声音数据库ID而不是speaker标识
        ossUrl: ossResult.url,
        ossKey: ossResult.key,
        duration,
        md5Hash: cacheKey,
      },
      {
        returning: true,
      }
    );

    console.log(`[TTS] 音频${created ? "创建" : "更新"}成功: ${cacheKey}`);

    return {
      audioUrl: ossResult.url,
      duration,
      audioId: audio.id,
      fromCache: false, // 标记为新生成的音频
    };
  } catch (error) {
    console.error("生成音频失败(UPSERT模式):", error);
    throw error;
  }
}

/**
 * 生成音频
 * @param {string} text 文本内容
 * @param {string} language 语言代码
 * @param {number} speed 语速
 * @param {string} speaker 说话者
 * @param {string} cacheKey 缓存键
 * @param {number} voiceDbId 声音数据库ID，用于TTS服务和缓存键计算
 * @returns {Promise<Object>} 音频信息
 */
async function generateAudio(
  text,
  language,
  speed,
  speaker,
  cacheKey,
  voiceDbId
) {
  try {
    console.log(
      `[TTS] 生成音频: 语言=${language}, 说话人=${speaker}, 声音数据库ID=${voiceDbId}`
    );

    // 检查必要参数
    if (!text) {
      throw new Error(`[TTS] 文本内容不能为空`);
    }

    if (!language) {
      throw new Error(`[TTS] 语言不能为空`);
    }

    if (!voiceDbId) {
      throw new Error(
        `[TTS] 声音数据库ID不能为空，说话人: ${speaker}, 语言: ${language}`
      );
    }

    // 如果没有提供缓存键，使用声音数据库ID计算
    if (!cacheKey) {
      cacheKey = calculateCacheKey(text, language, speed, voiceDbId);
    }

    // 获取声音信息（统一使用 getVoiceInfo 函数）
    const voiceInfo = await getVoiceInfo(voiceDbId);
    if (!voiceInfo) {
      throw new Error(`[TTS] 未找到声音数据库ID为 ${voiceDbId} 的声音信息`);
    }

    // 前端已处理特殊词汇，直接使用传入的文本
    let processedText = text;

    // 根据声音所属的服务选择TTS服务
    let audioBuffer;
    if (voiceInfo.service_id === "google") {
      // 使用Google TTS服务
      audioBuffer = await generateGoogleTTS(
        processedText, // 使用处理后的文本
        speed,
        speaker,
        3,
        2000,
        voiceInfo.speaker_id,
        voiceInfo.api_params
      );
    } else if (voiceInfo.service_id === "baidu") {
      // 使用百度TTS服务
      audioBuffer = await generateBaiduTTS(
        processedText, // 使用处理后的文本
        language,
        speed * 5,
        speaker,
        voiceInfo.speaker_id,
        voiceInfo.api_params
      ); // 百度TTS的速度范围是0-15，需要转换
    } else {
      throw new Error(`[TTS] 不支持的TTS服务: ${voiceInfo.service_id}`);
    }

    // 上传到OSS
    const ossResult = await ossService.uploadAudio(audioBuffer, language);

    // 获取音频时长
    const duration = await getAudioDurationFromBuffer(audioBuffer);

    // 如果时长为0或无效，也认为是失败
    if (!duration || duration <= 0) {
      throw new Error(`[TTS] 音频时长无效: ${duration}秒`);
    }

    // 保存到数据库
    const audio = await db.Audio.create({
      text,
      language,
      speed,
      voice_db_id: voiceDbId, // 使用声音数据库ID而不是speaker标识
      ossUrl: ossResult.url,
      ossKey: ossResult.key,
      duration,
      md5Hash: cacheKey,
    });

    return {
      audioUrl: ossResult.url,
      duration,
      audioId: audio.id,
    };
  } catch (error) {
    console.error("生成音频失败:", error);
    throw error;
  }
}

/**
 * 批量生成音频
 * @param {Array<Object>} items 音频项数组
 * @param {boolean} ignoreCache 是否忽略缓存
 * @returns {Promise<Array<Object>>} 生成结果
 */
async function batchGenerateAudio(items, ignoreCache = false) {
  try {
    // 按语言分组
    const groupedItems = {};
    items.forEach((item) => {
      const lang = item.language || "auto";
      if (!groupedItems[lang]) {
        groupedItems[lang] = [];
      }
      groupedItems[lang].push(item);
    });

    // 不同语言并行处理
    const results = await Promise.all(
      Object.entries(groupedItems).map(async ([langKey, langItems]) => {
        // langKey是语言代码，用于分组处理
        // 同一语言内串行处理，避免API限流
        const langResults = [];
        for (const item of langItems) {
          try {
            // 使用每个项目自己的语速设置，如果没有则使用默认值
            const speed = typeof item.speed === "number" ? item.speed : 1.0;

            // 获取说话人和声音数据库ID
            const speaker = item.speaker || "default";
            const voiceDbId = item.voice_db_id || null;

            console.log(
              `[TTS] 处理项目: ID=${item.id}, 语言=${
                item.language
              }, 说话人=${speaker}, 声音数据库ID=${
                voiceDbId || "默认"
              }, 忽略缓存: ${ignoreCache ? "是" : "否"}`
            );

            // 检查必要参数
            if (!voiceDbId) {
              throw new Error(
                `[TTS] 声音数据库ID不能为空，说话人: ${speaker}, 语言: ${
                  item.language || "auto"
                }`
              );
            }

            // 如果忽略缓存，直接生成新音频
            let result;
            if (ignoreCache) {
              // 计算缓存键 - 使用声音数据库ID而不是说话人名称
              const cacheKey = calculateCacheKey(
                item.text,
                item.language || "auto",
                speed,
                voiceDbId
              );

              // 直接生成新音频，跳过缓存检查，使用UPSERT模式
              result = await limit(() =>
                generateAudioWithUpsert(
                  item.text,
                  item.language || "auto",
                  speed,
                  speaker,
                  cacheKey,
                  voiceDbId
                )
              );
            } else {
              // 正常流程，先检查缓存
              result = await limit(() =>
                getOrGenerateAudio(
                  item.text,
                  item.language || "auto",
                  speed,
                  speaker, // 说话人名称
                  null, // 缓存键，会在函数内部计算
                  voiceDbId // 声音数据库ID，用于TTS服务和缓存键计算
                )
              );
            }

            langResults.push({
              ...item,
              audioUrl: result.audioUrl,
              duration: result.duration,
              audioId: result.audioId,
              success: true,
            });
          } catch (error) {
            console.error(`生成音频失败: ${item.text}`, error);
            langResults.push({
              ...item,
              error: error.message,
              success: false,
            });
          }
        }
        return langResults;
      })
    );

    // 合并结果
    return results.flat();
  } catch (error) {
    console.error("批量生成音频失败:", error);
    throw error;
  }
}

/**
 * 生成Google TTS音频
 * @param {string} text 文本内容
 * @param {number} speed 语速，范围0.5-2.0
 * @param {string} speaker 说话人，用于选择不同的声音
 * @param {number} retryCount 重试次数，默认为3
 * @param {number} retryDelay 重试延迟(毫秒)，默认为2000
 * @param {string} speakerId 说话人ID，用于TTS服务
 * @param {Object} apiParams API参数，包含languageCode等
 * @returns {Promise<Buffer>} 音频数据
 */
async function generateGoogleTTS(
  text,
  speed = 1.0,
  speaker = "default",
  retryCount = 3,
  retryDelay = 2000,
  speakerId,
  apiParams = {}
) {
  let lastError = null;

  for (let attempt = 1; attempt <= retryCount + 1; attempt++) {
    try {
      console.log(
        `[GoogleTTS] 生成音频 (尝试 ${attempt}/${
          retryCount + 1
        }): ${text.substring(
          0,
          20
        )}..., 语速: ${speed}, 说话人: ${speaker}, 说话人ID: ${speakerId}`
      );

      const url = `https://texttospeech.googleapis.com/v1/text:synthesize?key=${GOOGLE_API_KEY}`;

      // 检查必要参数
      if (!speakerId) {
        throw new Error(`[GoogleTTS] 未提供有效的说话人ID，无法生成音频`);
      }

      // 获取语言代码
      const languageCode = apiParams.languageCode || "ja-JP";

      const response = await axios.post(url, {
        input: { text },
        voice: { languageCode: languageCode, name: speakerId },
        audioConfig: {
          audioEncoding: "MP3",
          speakingRate: parseFloat(speed) || 1.0,
        },
      });

      // Google TTS返回的是Base64编码的音频数据
      if (response.data && response.data.audioContent) {
        if (attempt > 1) {
          console.log(`[GoogleTTS] 重试成功，第${attempt}次尝试`);
        }
        return Buffer.from(response.data.audioContent, "base64");
      } else {
        throw new Error("Google TTS返回的数据格式不正确");
      }
    } catch (error) {
      lastError = error;

      // 检查是否是请求限制错误
      const isRateLimitError = error.response && error.response.status === 429;

      // 如果是最后一次尝试或者不是请求限制错误，则抛出错误
      if (attempt === retryCount + 1 || !isRateLimitError) {
        console.error(
          `[GoogleTTS] 生成失败 (尝试 ${attempt}/${retryCount + 1}): ${
            error.message
          }`
        );
        throw error;
      }

      // 否则等待一段时间后重试
      console.log(
        `[GoogleTTS] 请求限制错误，${retryDelay}ms后重试 (尝试 ${attempt}/${
          retryCount + 1
        })`
      );
      await new Promise((resolve) => setTimeout(resolve, retryDelay));

      // 每次重试增加延迟时间，避免连续触发限制
      retryDelay = retryDelay * 1.5;
    }
  }

  // 这行代码理论上不会执行，因为循环中会抛出错误或返回结果
  throw lastError;
}

/**
 * 生成百度TTS音频
 * @param {string} text 文本内容
 * @param {string} language 语言代码，zh或en
 * @param {number} speed 语速，范围0-15
 * @param {string} speaker 说话人，用于选择不同的声音
 * @param {string} speakerId 说话人ID，用于TTS服务
 * @param {Object} apiParams API参数，包含per等
 * @returns {Promise<Buffer>} 音频数据
 */
async function generateBaiduTTS(
  text,
  language = "zh",
  speed = 5,
  speaker = "default",
  speakerId,
  apiParams = {}
) {
  try {
    console.log(
      `[BaiduTTS] 生成音频: ${text.substring(
        0,
        20
      )}..., 语言: ${language}, 语速: ${speed}, 说话人: ${speaker}, 说话人ID: ${speakerId}`
    );

    // 检查必要参数
    if (!speakerId) {
      throw new Error(`[BaiduTTS] 未提供有效的说话人ID，无法生成音频`);
    }

    // 获取发音人ID
    const per = apiParams.per;
    if (!per) {
      throw new Error(`[BaiduTTS] 未提供有效的发音人ID，无法生成音频`);
    }

    // 确保速度在有效范围内
    const normalizedSpeed = Math.min(Math.max(parseInt(speed) || 5, 0), 15);

    // 设置TTS参数
    const options = {
      spd: normalizedSpeed, // 语速，取值0-15
      pit: 5, // 音调，取值0-15
      vol: 15, // 音量，取值0-15
      per: per, // 发音人ID
      aue: 3, // 音频格式，3为mp3格式
    };

    // 调用百度SDK生成语音
    return new Promise((resolve, reject) => {
      baiduClient
        .text2audio(text, options)
        .then((result) => {
          if (result.data) {
            // 返回音频数据
            resolve(result.data);
          } else {
            // 返回错误信息
            console.error(`[BaiduTTS] 生成失败: ${JSON.stringify(result)}`);
            reject(new Error(`百度TTS错误: ${result.err_msg || "未知错误"}`));
          }
        })
        .catch((err) => {
          console.error(`[BaiduTTS] 生成失败: ${err.message}`);
          reject(err);
        });
    });
  } catch (error) {
    console.error(`[BaiduTTS] 生成失败: ${error.message}`);
    throw error;
  }
}

module.exports = {
  getOrGenerateAudio,
  generateAudioWithUpsert,
  batchGenerateAudio,
  calculateCacheKey,
  generateGoogleTTS,
  generateBaiduTTS,
  getVoiceInfo,
};
