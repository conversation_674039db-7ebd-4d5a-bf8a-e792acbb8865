/**
 * 图片服务
 * 处理图片上传和获取
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

/**
 * 上传图片到服务器
 * @param {File} file - 图片文件
 * @param {string} type - 图片类型，如 'cover', 'background'
 * @param {Object} metadata - 可选的元数据
 * @returns {Promise<Object>} 上传结果
 */
export async function uploadImage(file, type = "general", metadata = {}) {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("type", type);

  if (Object.keys(metadata).length > 0) {
    formData.append("metadata", JSON.stringify(metadata));
  }

  try {
    const response = await httpClient.upload(
      API_ENDPOINTS.IMAGES.UPLOAD,
      formData
    );
    return response;
  } catch (error) {
    console.error("图片上传失败:", error);
    throw error;
  }
}

export default {
  uploadImage,
};
