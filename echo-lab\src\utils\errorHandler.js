/**
 * 全局错误处理器
 * 自动捕获和上报各种类型的错误到自建统计系统
 */

import { errorReportService } from "@/services/errorStatisticsService";
import { userBehaviorTracker } from "@/utils/userBehaviorTracker";

/**
 * 错误处理器配置
 */
const ERROR_HANDLER_CONFIG = {
  // 是否启用错误上报
  enabled: true,
  // 是否在控制台输出错误
  logToConsole: true,
  // 错误采样率（0-1，1表示上报所有错误）
  sampleRate: 1.0,
  // 忽略的错误类型
  ignoredErrors: [
    "ResizeObserver loop limit exceeded",
    "ResizeObserver loop completed with undelivered notifications",
    "Non-Error promise rejection captured",
    "Script error.",
    "Network request failed",
  ],
  // 忽略的URL模式
  ignoredUrls: [/chrome-extension:/, /moz-extension:/, /safari-extension:/],
};

/**
 * 检查是否应该忽略错误
 * @param {Error|string} error - 错误对象或消息
 * @param {string} [url] - 错误发生的URL
 * @returns {boolean} - 是否应该忽略
 */
function shouldIgnoreError(error, url) {
  const message = typeof error === "string" ? error : error.message;

  // 检查忽略的错误消息
  if (
    ERROR_HANDLER_CONFIG.ignoredErrors.some((ignored) =>
      message.includes(ignored)
    )
  ) {
    return true;
  }

  // 检查忽略的URL模式
  if (
    url &&
    ERROR_HANDLER_CONFIG.ignoredUrls.some((pattern) => pattern.test(url))
  ) {
    return true;
  }

  // 采样率检查
  if (Math.random() > ERROR_HANDLER_CONFIG.sampleRate) {
    return true;
  }

  return false;
}

/**
 * 处理JavaScript错误
 * @param {Error} error - 错误对象
 * @param {Object} [context={}] - 额外上下文
 */
export function handleJavaScriptError(error, context = {}) {
  if (!ERROR_HANDLER_CONFIG.enabled || shouldIgnoreError(error)) {
    return;
  }

  if (ERROR_HANDLER_CONFIG.logToConsole) {
    console.error("JavaScript Error:", error);
  }

  // 上报到自建错误统计系统
  const errorData = errorReportService.formatErrorData(error, {
    type: "javascript",
    ...context,
  });
  errorReportService.addToBatch(errorData);
}

/**
 * 处理Promise拒绝错误
 * @param {any} reason - 拒绝原因
 * @param {Object} [context={}] - 额外上下文
 */
export function handlePromiseRejection(reason, context = {}) {
  if (!ERROR_HANDLER_CONFIG.enabled) {
    return;
  }

  const error = reason instanceof Error ? reason : new Error(String(reason));

  if (shouldIgnoreError(error)) {
    return;
  }

  if (ERROR_HANDLER_CONFIG.logToConsole) {
    console.error("Unhandled Promise Rejection:", reason);
  }

  // 上报到自建错误统计系统
  const errorData = errorReportService.formatErrorData(error, {
    type: "promise",
    ...context,
  });
  errorReportService.addToBatch(errorData);
}

/**
 * 处理Vue组件错误
 * @param {Error} error - 错误对象
 * @param {Object} instance - Vue组件实例
 * @param {string} info - 错误信息
 */
export function handleVueError(error, instance, info) {
  if (!ERROR_HANDLER_CONFIG.enabled || shouldIgnoreError(error)) {
    return;
  }

  if (ERROR_HANDLER_CONFIG.logToConsole) {
    console.error("Vue Error:", error, "Info:", info);
  }

  const componentName =
    instance?.$options?.name || instance?.$options?.__name || "Unknown";

  // 上报到自建错误统计系统
  const errorData = errorReportService.formatErrorData(error, {
    type: "vue",
    component: componentName,
    info,
    lifecycle: info,
  });
  errorReportService.addToBatch(errorData);
}

/**
 * 处理网络错误
 * @param {string} url - 请求URL
 * @param {number} [status] - HTTP状态码
 * @param {string} [message] - 错误消息
 * @param {Object} [context={}] - 额外上下文
 */
export function handleNetworkError(url, status, message, context = {}) {
  if (!ERROR_HANDLER_CONFIG.enabled) {
    return;
  }

  const error = new Error(message || `Network request failed: ${status}`);

  if (shouldIgnoreError(error, url)) {
    return;
  }

  if (ERROR_HANDLER_CONFIG.logToConsole) {
    console.error("Network Error:", { url, status, message });
  }

  // 上报到自建错误统计系统
  const errorData = errorReportService.formatErrorData(error, {
    type: "network",
    url,
    status,
    ...context,
  });
  errorReportService.addToBatch(errorData);
}

/**
 * 处理资源加载错误
 * @param {Event} event - 错误事件
 */
export function handleResourceError(event) {
  if (!ERROR_HANDLER_CONFIG.enabled) {
    return;
  }

  const target = event.target;
  const resourceType = target.tagName?.toLowerCase() || "unknown";
  const resourceUrl = target.src || target.href || "unknown";

  if (shouldIgnoreError("Resource load failed", resourceUrl)) {
    return;
  }

  if (ERROR_HANDLER_CONFIG.logToConsole) {
    console.error("Resource Load Error:", { resourceType, resourceUrl });
  }

  // 上报到自建错误统计系统
  const error = new Error("Resource load failed");
  const errorData = errorReportService.formatErrorData(error, {
    type: "resource",
    resourceType,
    resourceUrl,
  });
  errorReportService.addToBatch(errorData);
}

/**
 * 初始化全局错误处理器
 */
export function initializeErrorHandler() {
  if (!ERROR_HANDLER_CONFIG.enabled) {
    return;
  }

  // 初始化用户行为追踪器
  userBehaviorTracker.initialize();

  // 捕获JavaScript错误
  window.addEventListener("error", (event) => {
    handleJavaScriptError(event.error || new Error(event.message), {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
    });
  });

  // 捕获Promise拒绝
  window.addEventListener("unhandledrejection", (event) => {
    handlePromiseRejection(event.reason);
  });

  // 捕获资源加载错误
  window.addEventListener(
    "error",
    (event) => {
      if (event.target !== window) {
        handleResourceError(event);
      }
    },
    true
  );

  // 页面卸载时发送剩余的错误
  window.addEventListener("beforeunload", () => {
    errorReportService.flushBatch();
  });

  // 页面隐藏时发送剩余的错误（移动端兼容）
  document.addEventListener("visibilitychange", () => {
    if (document.visibilityState === "hidden") {
      errorReportService.flushBatch();
    }
  });

  console.log("全局错误处理器已初始化");
}

/**
 * 配置错误处理器
 * @param {Object} config - 配置选项
 */
export function configureErrorHandler(config) {
  Object.assign(ERROR_HANDLER_CONFIG, config);
}

/**
 * 手动上报错误
 * @param {Error|string} error - 错误对象或消息
 * @param {Object} [context={}] - 额外上下文
 */
export function reportError(error, context = {}) {
  handleJavaScriptError(
    error instanceof Error ? error : new Error(error),
    context
  );
}

export default {
  initializeErrorHandler,
  configureErrorHandler,
  reportError,
  handleJavaScriptError,
  handlePromiseRejection,
  handleVueError,
  handleNetworkError,
  handleResourceError,
};
