/**
 * 特殊词汇处理服务
 * 提供特殊词汇的查询和处理功能
 */
const db = require('../models');
const { Op } = require('sequelize');

/**
 * 获取特殊词汇列表
 * @param {string} serviceId TTS服务ID
 * @param {string} languageCode 语言代码
 * @returns {Promise<Array>} 特殊词汇列表
 */
async function getSpecialWords(serviceId, languageCode) {
  try {
    // 构建查询条件
    const where = {};
    
    if (serviceId) {
      where.serviceId = serviceId;
    }
    
    if (languageCode) {
      where.languageCode = languageCode;
    }
    
    // 查询特殊词汇
    const specialWords = await db.SpecialWord.findAll({
      where,
      order: [
        ['word', 'ASC']
      ]
    });
    
    return specialWords;
  } catch (error) {
    console.error('获取特殊词汇列表失败:', error);
    return [];
  }
}

/**
 * 处理文本中的特殊词汇
 * 将特殊词汇替换为标注中的假名
 * @param {string} text 原始文本
 * @param {string} annotation 标注文本
 * @param {string} serviceId TTS服务ID
 * @param {string} languageCode 语言代码
 * @returns {Promise<Object>} 处理结果，包含处理后的文本和是否进行了替换
 */
async function processSpecialWords(text, annotation, serviceId, languageCode) {
  try {
    // 如果没有标注，直接返回原文
    if (!annotation) {
      return {
        processedText: text,
        isProcessed: false
      };
    }
    
    // 获取特殊词汇列表
    const specialWords = await getSpecialWords(serviceId, languageCode);
    
    // 如果没有特殊词汇，直接返回原文
    if (!specialWords || specialWords.length === 0) {
      return {
        processedText: text,
        isProcessed: false
      };
    }
    
    // 处理特殊词汇
    let processedText = text;
    let isProcessed = false;
    
    // 按词汇长度降序排序，优先处理长词汇
    const sortedWords = specialWords.sort((a, b) => b.word.length - a.word.length);
    
    for (const specialWord of sortedWords) {
      const word = specialWord.word;
      
      // 检查文本中是否包含特殊词汇
      if (processedText.includes(word)) {
        // 在原文中查找特殊词汇的位置
        const wordIndex = processedText.indexOf(word);
        
        // 在标注中查找对应的假名
        // 这里需要实现一个函数来从标注中提取对应的假名
        const reading = extractReadingFromAnnotation(text, annotation, word, wordIndex);
        
        if (reading) {
          // 替换特殊词汇为假名
          processedText = processedText.replace(word, reading);
          isProcessed = true;
          
          console.log(`[SpecialWord] 替换特殊词汇: "${word}" -> "${reading}"`);
        }
      }
    }
    
    return {
      processedText,
      isProcessed
    };
  } catch (error) {
    console.error('处理特殊词汇失败:', error);
    return {
      processedText: text,
      isProcessed: false
    };
  }
}

/**
 * 从标注中提取特殊词汇的读音
 * @param {string} originalText 原始文本
 * @param {string} annotation 标注文本
 * @param {string} word 特殊词汇
 * @param {number} wordIndex 特殊词汇在原文中的位置
 * @returns {string|null} 提取的读音，如果无法提取则返回null
 */
function extractReadingFromAnnotation(originalText, annotation, word, wordIndex) {
  try {
    // 计算特殊词汇前面的字符数
    const textBefore = originalText.substring(0, wordIndex);
    
    // 计算特殊词汇在标注中的大致位置
    // 这是一个简化的算法，假设原文和标注的字符数大致对应
    // 实际情况可能需要更复杂的算法
    
    // 计算特殊词汇前面的非假名字符数
    let nonKanaCount = 0;
    for (let i = 0; i < textBefore.length; i++) {
      const char = textBefore[i];
      // 检查是否为假名（平假名或片假名）
      if (!isKana(char)) {
        nonKanaCount++;
      }
    }
    
    // 在标注中查找对应位置
    let annotationIndex = 0;
    let currentNonKanaCount = 0;
    
    // 跳过标注中的前面部分，直到找到对应位置
    while (currentNonKanaCount < nonKanaCount && annotationIndex < annotation.length) {
      if (!isKana(originalText[currentNonKanaCount])) {
        // 跳过对应的假名部分
        let kanaCount = 0;
        while (annotationIndex < annotation.length && isKana(annotation[annotationIndex])) {
          annotationIndex++;
          kanaCount++;
        }
        currentNonKanaCount++;
      } else {
        // 原文中的假名，直接跳过
        annotationIndex++;
        currentNonKanaCount++;
      }
    }
    
    // 提取特殊词汇对应的假名部分
    let reading = '';
    let currentIndex = annotationIndex;
    
    // 提取连续的假名
    while (currentIndex < annotation.length && isKana(annotation[currentIndex])) {
      reading += annotation[currentIndex];
      currentIndex++;
    }
    
    // 如果没有提取到读音，返回null
    if (!reading) {
      return null;
    }
    
    return reading;
  } catch (error) {
    console.error('提取读音失败:', error);
    return null;
  }
}

/**
 * 检查字符是否为假名（平假名或片假名）
 * @param {string} char 要检查的字符
 * @returns {boolean} 是否为假名
 */
function isKana(char) {
  if (!char) return false;
  
  // 平假名Unicode范围：\u3040-\u309F
  // 片假名Unicode范围：\u30A0-\u30FF
  return /[\u3040-\u309F\u30A0-\u30FF]/.test(char);
}

module.exports = {
  getSpecialWords,
  processSpecialWords
};
