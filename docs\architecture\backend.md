# Echo Lab 后端架构

本文档详细说明了 Echo Lab 后端的架构设计、API 结构和工作流程。

## 技术栈

- **运行环境**：Node.js 18+
- **Web 框架**：Express
- **数据库**：MySQL
- **ORM**：Sequelize
- **身份验证**：JWT (JSON Web Token)
- **文件存储**：阿里云 OSS
- **TTS 服务**：
  - Google TTS (日语、英语)
  - 百度 TTS (中文)
- **邮件服务**：阿里云邮件服务
- **并发控制**：p-limit

## 目录结构

```
backend/
├── config/         # 配置文件
│   ├── config.js   # 主配置文件
│   └── database.js # 数据库配置
├── controllers/    # 控制器
│   ├── authController.js    # 认证控制器
│   ├── contentController.js # 内容控制器
│   ├── resourceController.js # 资源控制器
│   └── feedbackController.js # 反馈控制器
├── database/       # 数据库配置
├── middleware/     # 中间件
│   ├── auth.js     # 认证中间件
│   └── error.js    # 错误处理中间件
├── migrations/     # 数据库迁移
├── models/         # 数据模型
│   ├── User.js     # 用户模型
│   ├── Content.js  # 内容模型
│   ├── Resource.js # 资源模型
│   └── Feedback.js # 反馈模型
├── routes/         # 路由定义
│   ├── auth.js     # 认证路由
│   ├── content.js  # 内容路由
│   ├── resource.js # 资源路由
│   └── feedback.js # 反馈路由
├── scripts/        # 脚本文件
├── services/       # 服务层
│   ├── authService.js    # 认证服务
│   ├── contentService.js # 内容服务
│   ├── resourceService.js # 资源服务
│   ├── ttsService.js     # TTS服务
│   ├── storageService.js # 存储服务
│   └── emailService.js   # 邮件服务
└── temp/           # 临时文件
```

## 核心模块

### 1. 认证模块

认证模块负责用户注册、登录和权限控制。

#### 主要功能

- 用户注册
- 用户登录
- JWT 生成和验证
- 权限控制

#### 实现方式

```javascript
// 用户登录
async function login(req, res) {
  const { username, password } = req.body;
  
  try {
    // 查找用户
    const user = await User.findOne({ where: { username } });
    
    if (!user) {
      return res.status(401).json({ message: '用户名或密码错误' });
    }
    
    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    
    if (!isPasswordValid) {
      return res.status(401).json({ message: '用户名或密码错误' });
    }
    
    // 生成 JWT
    const token = jwt.sign(
      { id: user.id, username: user.username },
      process.env.JWT_SECRET,
      { expiresIn: '365d' } // 有效期一年
    );
    
    // 返回用户信息和 token
    return res.json({
      id: user.id,
      username: user.username,
      email: user.email,
      token
    });
  } catch (error) {
    console.error('登录错误:', error);
    return res.status(500).json({ message: '服务器错误' });
  }
}
```

### 2. 内容模块

内容模块负责内容的创建、读取、更新和删除。

#### 主要功能

- 创建内容
- 获取内容列表
- 获取内容详情
- 更新内容
- 删除内容

#### 实现方式

```javascript
// 创建内容
async function createContent(req, res) {
  const { title, description, data } = req.body;
  const userId = req.user.id;
  
  try {
    // 创建内容
    const content = await Content.create({
      title,
      description,
      data: JSON.stringify(data),
      userId
    });
    
    return res.status(201).json(content);
  } catch (error) {
    console.error('创建内容错误:', error);
    return res.status(500).json({ message: '服务器错误' });
  }
}

// 获取内容列表
async function getContentList(req, res) {
  const userId = req.user.id;
  
  try {
    // 查询内容列表
    const contentList = await Content.findAll({
      where: { userId },
      attributes: ['id', 'title', 'description', 'createdAt', 'updatedAt'],
      order: [['updatedAt', 'DESC']]
    });
    
    return res.json(contentList);
  } catch (error) {
    console.error('获取内容列表错误:', error);
    return res.status(500).json({ message: '服务器错误' });
  }
}
```

### 3. 资源模块

资源模块负责资源的生成和管理。

#### 主要功能

- 生成音频
- 生成翻译
- 生成标注
- 上传和下载资源

#### 实现方式

```javascript
// 生成音频
async function generateAudio(req, res) {
  const { text, language } = req.body;
  
  try {
    // 计算文本的MD5作为缓存键
    const textMd5 = crypto.createHash('md5').update(text).digest('hex');
    
    // 检查缓存
    const existingAudio = await Resource.findOne({
      where: {
        type: 'audio',
        textMd5,
        language
      }
    });
    
    if (existingAudio && !req.body.ignoreCache) {
      return res.json({
        url: existingAudio.url,
        duration: existingAudio.duration
      });
    }
    
    // 选择TTS服务
    let ttsService;
    if (language === 'zh-CN') {
      ttsService = baiduTtsService;
    } else {
      ttsService = googleTtsService;
    }
    
    // 生成音频
    const { audioBuffer, duration } = await ttsService.generateAudio(text, language);
    
    // 上传到存储
    const fileName = `audio/${textMd5}.mp3`;
    const url = await storageService.uploadBuffer(audioBuffer, fileName, 'audio/mpeg');
    
    // 保存资源记录
    const resource = await Resource.create({
      type: 'audio',
      textMd5,
      text,
      language,
      url,
      duration
    });
    
    return res.json({
      url,
      duration
    });
  } catch (error) {
    console.error('生成音频错误:', error);
    return res.status(500).json({ message: '服务器错误' });
  }
}
```

### 4. TTS 服务

TTS 服务负责文本转语音。

#### 主要功能

- Google TTS 集成
- 百度 TTS 集成
- 音频处理

#### 实现方式

```javascript
// Google TTS 服务
const googleTtsService = {
  async generateAudio(text, language) {
    try {
      // 创建 TTS 客户端
      const client = new textToSpeech.TextToSpeechClient();
      
      // 构建请求
      const request = {
        input: { text },
        voice: {
          languageCode: language,
          ssmlGender: 'NEUTRAL'
        },
        audioConfig: {
          audioEncoding: 'MP3'
        }
      };
      
      // 调用 API
      const [response] = await client.synthesizeSpeech(request);
      
      // 获取音频数据
      const audioBuffer = response.audioContent;
      
      // 计算音频时长
      const duration = await getAudioDuration(audioBuffer);
      
      return { audioBuffer, duration };
    } catch (error) {
      console.error('Google TTS 错误:', error);
      throw error;
    }
  }
};

// 百度 TTS 服务
const baiduTtsService = {
  async generateAudio(text, language) {
    try {
      // 创建百度 AI 客户端
      const client = new AipSpeech(
        process.env.BAIDU_APP_ID,
        process.env.BAIDU_API_KEY,
        process.env.BAIDU_SECRET_KEY
      );
      
      // 调用 API
      const result = await client.text2audio(text, {
        spd: 5, // 语速
        pit: 5, // 音调
        vol: 5, // 音量
        per: 0  // 发音人
      });
      
      // 检查结果
      if (result.data) {
        // 获取音频数据
        const audioBuffer = result.data;
        
        // 计算音频时长
        const duration = await getAudioDuration(audioBuffer);
        
        return { audioBuffer, duration };
      } else {
        throw new Error(result.err_msg);
      }
    } catch (error) {
      console.error('百度 TTS 错误:', error);
      throw error;
    }
  }
};
```

### 5. 存储服务

存储服务负责文件的上传和下载。

#### 主要功能

- 上传文件
- 下载文件
- 生成临时URL

#### 实现方式

```javascript
// 阿里云 OSS 存储服务
const storageService = {
  async uploadBuffer(buffer, fileName, contentType) {
    try {
      // 创建 OSS 客户端
      const client = new OSS({
        region: process.env.OSS_REGION,
        accessKeyId: process.env.OSS_ACCESS_KEY_ID,
        accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
        bucket: process.env.OSS_BUCKET
      });
      
      // 上传文件
      const result = await client.put(fileName, buffer, {
        mime: contentType
      });
      
      // 返回文件 URL
      return result.url;
    } catch (error) {
      console.error('上传文件错误:', error);
      throw error;
    }
  },
  
  async downloadFile(fileName) {
    try {
      // 创建 OSS 客户端
      const client = new OSS({
        region: process.env.OSS_REGION,
        accessKeyId: process.env.OSS_ACCESS_KEY_ID,
        accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
        bucket: process.env.OSS_BUCKET
      });
      
      // 下载文件
      const result = await client.get(fileName);
      
      // 返回文件内容
      return result.content;
    } catch (error) {
      console.error('下载文件错误:', error);
      throw error;
    }
  }
};
```

## API 路由

Echo Lab 后端提供以下 API 路由：

### 认证路由

```javascript
// 认证路由
router.post('/register', authController.register);
router.post('/login', authController.login);
router.get('/profile', authMiddleware, authController.getProfile);
```

### 内容路由

```javascript
// 内容路由
router.post('/', authMiddleware, contentController.createContent);
router.get('/', authMiddleware, contentController.getContentList);
router.get('/:id', contentController.getContentById);
router.put('/:id', authMiddleware, contentController.updateContent);
router.delete('/:id', authMiddleware, contentController.deleteContent);
```

### 资源路由

```javascript
// 资源路由
router.post('/audio', resourceController.generateAudio);
router.post('/translation', resourceController.generateTranslation);
router.post('/annotation', resourceController.generateAnnotation);
router.post('/upload', authMiddleware, resourceController.uploadFile);
```

### 反馈路由

```javascript
// 反馈路由
router.post('/', feedbackController.createFeedback);
router.get('/', authMiddleware, feedbackController.getFeedbackList);
router.get('/admin', authMiddleware, adminMiddleware, feedbackController.getAllFeedback);
router.put('/:id', authMiddleware, adminMiddleware, feedbackController.updateFeedbackStatus);
```

## 数据库模型

Echo Lab 后端使用 Sequelize ORM 定义以下数据模型：

### 用户模型

```javascript
// 用户模型
const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false
  },
  role: {
    type: DataTypes.ENUM('user', 'admin'),
    defaultValue: 'user'
  }
});
```

### 内容模型

```javascript
// 内容模型
const Content = sequelize.define('Content', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  data: {
    type: DataTypes.TEXT('long'),
    allowNull: false
  },
  coverUrl: {
    type: DataTypes.STRING,
    allowNull: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  }
});
```

### 资源模型

```javascript
// 资源模型
const Resource = sequelize.define('Resource', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  type: {
    type: DataTypes.ENUM('audio', 'translation', 'annotation'),
    allowNull: false
  },
  textMd5: {
    type: DataTypes.STRING,
    allowNull: false
  },
  text: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  language: {
    type: DataTypes.STRING,
    allowNull: false
  },
  url: {
    type: DataTypes.STRING,
    allowNull: false
  },
  duration: {
    type: DataTypes.FLOAT,
    allowNull: true
  }
});
```

### 反馈模型

```javascript
// 反馈模型
const Feedback = sequelize.define('Feedback', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  email: {
    type: DataTypes.STRING,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'completed', 'rejected'),
    defaultValue: 'pending'
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id'
    }
  }
});
```
