/**
 * 语言状态管理
 * 管理用户当前学习的语言
 */

import { defineStore } from "pinia";
import { SUPPORTED_LANGUAGES, getLanguageLabel } from "@/config/languages";
import { getLearningLanguage, setLearningLanguage, getUserLevel, setUserLevel } from "@/utils/userSettings";
import { getLanguageLevels, getLevelInfo } from "@/config/languageLevels";

export const useLanguageStore = defineStore("language", {
  state: () => ({
    // 当前学习的语言
    currentLearningLanguage: getLearningLanguage(), // 从统一设置中获取

    // 当前用户等级
    currentUserLevel: getUserLevel(),

    // 支持的语言列表
    supportedLanguages: SUPPORTED_LANGUAGES,
  }),

  getters: {
    /**
     * 获取当前学习语言的标签
     */
    currentLanguageLabel(state) {
      return getLanguageLabel(state.currentLearningLanguage);
    },

    /**
     * 获取当前学习语言的配置对象
     */
    currentLanguageConfig(state) {
      return state.supportedLanguages.find(
        (lang) => lang.value === state.currentLearningLanguage
      );
    },

    /**
     * 检查是否已设置学习语言
     */
    hasLanguageSet(state) {
      return !!state.currentLearningLanguage;
    },

    /**
     * 检查是否已设置用户等级
     */
    hasLevelSet(state) {
      return !!state.currentUserLevel;
    },

    /**
     * 获取当前语言的等级选项
     */
    currentLanguageLevels(state) {
      if (!state.currentLearningLanguage) return [];
      return getLanguageLevels(state.currentLearningLanguage);
    },

    /**
     * 获取当前用户等级信息
     */
    currentLevelInfo(state) {
      if (!state.currentLearningLanguage || !state.currentUserLevel) return null;
      return getLevelInfo(state.currentLearningLanguage, state.currentUserLevel);
    },

    /**
     * 检查是否完成了基本设置（语言+等级）
     */
    hasCompletedBasicSetup(state) {
      return !!state.currentLearningLanguage && !!state.currentUserLevel;
    },
  },

  actions: {
    /**
     * 设置当前学习语言
     * @param {string} languageCode 语言代码
     */
    setLearningLanguage(languageCode) {
      if (!languageCode) return false;

      // 验证语言代码是否支持
      const isSupported = this.supportedLanguages.some(
        (lang) => lang.value === languageCode
      );

      if (!isSupported) {
        console.warn(`不支持的语言代码: ${languageCode}`);
        return false;
      }

      // 如果语言发生变化，清除之前的等级设置
      if (this.currentLearningLanguage !== languageCode) {
        this.currentUserLevel = null;
        setUserLevel(null);
      }

      this.currentLearningLanguage = languageCode;

      // 使用统一的设置管理
      const success = setLearningLanguage(languageCode);

      if (success) {
        console.log(`学习语言已切换为: ${this.currentLanguageLabel}`);
      }

      return success;
    },

    /**
     * 设置用户等级
     * @param {string} levelKey 等级键
     */
    setUserLevel(levelKey) {
      if (!this.currentLearningLanguage) {
        console.error("请先设置学习语言");
        return false;
      }

      // 验证等级是否适用于当前语言
      const availableLevels = this.currentLanguageLevels;
      const isValidLevel = availableLevels.some(level => level.key === levelKey);

      if (!isValidLevel) {
        console.error("无效的等级:", levelKey, "当前语言:", this.currentLearningLanguage);
        return false;
      }

      // 更新状态
      this.currentUserLevel = levelKey;

      // 保存到本地存储
      const success = setUserLevel(levelKey);

      if (success) {
        console.log("用户等级已设置为:", levelKey);
      } else {
        console.error("保存用户等级失败");
      }

      return success;
    },

    /**
     * 清除用户等级
     */
    clearUserLevel() {
      this.currentUserLevel = null;
      setUserLevel(null);
      console.log("用户等级已清除");
    },

    /**
     * 从本地存储加载学习语言和等级
     */
    loadLearningLanguage() {
      try {
        // 加载语言设置
        const savedLanguage = getLearningLanguage();
        if (savedLanguage) {
          // 验证保存的语言是否仍然支持
          const isSupported = this.supportedLanguages.some(
            (lang) => lang.value === savedLanguage
          );

          if (isSupported) {
            this.currentLearningLanguage = savedLanguage;
            console.log(`已加载学习语言: ${this.currentLanguageLabel}`);
          } else {
            console.warn(`保存的语言代码不再支持: ${savedLanguage}，使用默认语言`);
            this.setLearningLanguage("ja"); // 使用默认日语
          }
        }

        // 加载等级设置
        const savedLevel = getUserLevel();
        if (savedLevel) {
          this.currentUserLevel = savedLevel;
          console.log("已加载用户等级:", savedLevel);
        }
      } catch (error) {
        console.error("加载学习语言失败:", error);
        this.setLearningLanguage("ja"); // 出错时使用默认日语
      }
    },

    /**
     * 重新加载用户等级（用于从其他地方更新等级后同步状态）
     */
    reloadUserLevel() {
      const savedLevel = getUserLevel();
      this.currentUserLevel = savedLevel;
      console.log("用户等级已重新加载:", savedLevel);
    },

    /**
     * 重置为默认语言
     */
    resetToDefault() {
      this.setLearningLanguage("ja");
    },

    /**
     * 切换学习语言（带确认）
     * @param {string} languageCode 新的语言代码
     * @returns {Promise<boolean>} 是否成功切换
     */
    async switchLanguage(languageCode) {
      if (languageCode === this.currentLearningLanguage) {
        return true; // 相同语言，无需切换
      }

      try {
        this.setLearningLanguage(languageCode);

        // 可以在这里添加切换后的处理逻辑
        // 比如清除相关缓存、重新加载内容等

        return true;
      } catch (error) {
        console.error("切换学习语言失败:", error);
        return false;
      }
    },

    /**
     * 获取语言选项列表（用于UI显示）
     */
    getLanguageOptions() {
      return this.supportedLanguages.map((lang) => ({
        value: lang.value,
        label: lang.label,
        isActive: lang.value === this.currentLearningLanguage,
      }));
    },
  },
});

export default useLanguageStore;
