<!--
  功能权限管理页面
  管理员可以在此页面管理功能标志和用户权限
-->
<template>
  <div class="feature-permissions-page">
    <div class="page-header">
      <h1>功能权限管理</h1>
      <div class="header-actions">
        <el-button type="primary" @click="refreshData">
          <el-icon>
            <i-ep-refresh />
          </el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <el-tabs v-model="activeTab" class="feature-tabs">
      <!-- 功能标志管理 -->
      <el-tab-pane label="功能标志" name="flags">
        <div class="tab-content">
          <div class="flags-header">
            <el-button type="primary" @click="showAddFeatureDialog">
              <el-icon>
                <i-ep-plus />
              </el-icon>
              添加功能权限
            </el-button>
          </div>

          <el-table v-loading="flagsLoading" :data="featureFlags" style="width: 100%">
            <el-table-column prop="featureKey" label="功能标识" min-width="180" />
            <el-table-column prop="displayName" label="显示名称" min-width="150" />
            <el-table-column prop="description" label="描述" min-width="250" />
            <el-table-column label="全局启用" width="120">
              <template #default="{ row }">
                <el-switch v-model="row.isEnabled" @change="handleFlagChange(row)" :loading="row.updating" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button size="small" type="primary" @click="editFlag(row)">
                  <el-icon>
                    <i-ep-edit />
                  </el-icon>
                  编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 用户权限管理 -->
      <el-tab-pane label="用户权限" name="permissions">
        <div class="tab-content">
          <div class="permissions-header">
            <el-button type="primary" @click="showAddPermissionDialog">
              <el-icon>
                <i-ep-plus />
              </el-icon>
              添加用户权限
            </el-button>
          </div>

          <el-table v-loading="permissionsLoading" :data="userPermissions" style="width: 100%">
            <el-table-column label="用户" min-width="200">
              <template #default="{ row }">
                <div v-if="row.user">
                  <div>{{ row.user.username || '未命名用户' }}</div>
                  <div class="user-email">{{ row.user.email }}</div>
                </div>
                <div v-else>未知用户</div>
              </template>
            </el-table-column>
            <el-table-column prop="featureKey" label="功能标识" min-width="180" />
            <el-table-column label="创建时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.createdAt) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button size="small" type="danger" @click="confirmDeletePermission(row)">
                  <el-icon>
                    <i-ep-delete />
                  </el-icon>
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 编辑功能标志对话框 -->
    <el-dialog v-model="flagDialogVisible" title="编辑功能标志" width="500px">
      <el-form v-if="currentFlag" :model="currentFlag" label-width="100px">
        <el-form-item label="功能标识">
          <el-input v-model="currentFlag.featureKey" disabled />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="currentFlag.description" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="全局启用">
          <el-switch v-model="currentFlag.isEnabled" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="flagDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveFlag" :loading="savingFlag">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加用户权限对话框 -->
    <el-dialog v-model="permissionDialogVisible" title="添加用户权限" width="500px">
      <el-form :model="newPermission" label-width="100px">
        <el-form-item label="用户ID" required>
          <el-input v-model="newPermission.userId" placeholder="输入用户ID" />
        </el-form-item>
        <el-form-item label="功能标识" required>
          <el-select v-model="newPermission.featureKey" placeholder="选择功能标识" style="width: 100%">
            <el-option v-for="flag in featureFlags" :key="flag.featureKey" :label="flag.featureKey"
              :value="flag.featureKey" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="permissionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addPermission" :loading="addingPermission">添加</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加功能权限对话框 -->
    <el-dialog v-model="featureDialogVisible" title="添加功能权限" width="500px">
      <el-form :model="newFeature" label-width="100px">
        <el-form-item label="功能标识" required>
          <el-input v-model="newFeature.featureKey" placeholder="输入功能标识，如 video_export" />
        </el-form-item>
        <el-form-item label="显示名称">
          <el-input v-model="newFeature.displayName" placeholder="输入显示名称，如 视频导出" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="newFeature.description" type="textarea" :rows="3" placeholder="输入功能描述" />
        </el-form-item>
        <el-form-item label="全局启用">
          <el-switch v-model="newFeature.isEnabled" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="featureDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addFeature" :loading="addingFeature">添加</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import featurePermissionService from '@/services/featurePermissionService';

// 标签页状态
const activeTab = ref('flags');

// 功能标志状态
const featureFlags = ref([]);
const flagsLoading = ref(false);
const flagDialogVisible = ref(false);
const currentFlag = ref(null);
const savingFlag = ref(false);

// 用户权限状态
const userPermissions = ref([]);
const permissionsLoading = ref(false);
const permissionDialogVisible = ref(false);
const newPermission = ref({
  userId: '',
  featureKey: ''
});
const addingPermission = ref(false);

// 功能权限状态
const featureDialogVisible = ref(false);
const newFeature = ref({
  featureKey: '',
  displayName: '',
  description: '',
  isEnabled: false
});
const addingFeature = ref(false);

// 加载功能标志
async function loadFeatureFlags() {
  flagsLoading.value = true;
  try {
    const flags = await featurePermissionService.getAllFeatureFlags();
    featureFlags.value = flags.map(flag => ({
      ...flag,
      updating: false
    }));
  } catch (error) {
    ElMessage.error('加载功能标志失败');
    console.error('加载功能标志失败:', error);
  } finally {
    flagsLoading.value = false;
  }
}

// 加载用户权限
async function loadUserPermissions() {
  permissionsLoading.value = true;
  try {
    const permissions = await featurePermissionService.getAllUserPermissions();
    userPermissions.value = permissions;
  } catch (error) {
    ElMessage.error('加载用户权限失败');
    console.error('加载用户权限失败:', error);
  } finally {
    permissionsLoading.value = false;
  }
}

// 刷新所有数据
function refreshData() {
  loadFeatureFlags();
  loadUserPermissions();
}

// 处理功能标志开关变化
async function handleFlagChange(flag) {
  flag.updating = true;
  try {
    await featurePermissionService.updateFeatureFlag(flag.id, {
      isEnabled: flag.isEnabled
    });
    ElMessage.success(`功能 ${flag.featureKey} 已${flag.isEnabled ? '启用' : '禁用'}`);
  } catch (error) {
    // 恢复原始状态
    flag.isEnabled = !flag.isEnabled;
    ElMessage.error('更新功能标志失败');
    console.error('更新功能标志失败:', error);
  } finally {
    flag.updating = false;
  }
}

// 编辑功能标志
function editFlag(flag) {
  currentFlag.value = { ...flag };
  flagDialogVisible.value = true;
}

// 保存功能标志
async function saveFlag() {
  if (!currentFlag.value) return;

  savingFlag.value = true;
  try {
    await featurePermissionService.updateFeatureFlag(currentFlag.value.id, {
      isEnabled: currentFlag.value.isEnabled,
      description: currentFlag.value.description
    });

    // 更新本地数据
    const index = featureFlags.value.findIndex(f => f.id === currentFlag.value.id);
    if (index !== -1) {
      featureFlags.value[index] = {
        ...featureFlags.value[index],
        isEnabled: currentFlag.value.isEnabled,
        description: currentFlag.value.description
      };
    }

    ElMessage.success('功能标志已更新');
    flagDialogVisible.value = false;
  } catch (error) {
    ElMessage.error('保存功能标志失败');
    console.error('保存功能标志失败:', error);
  } finally {
    savingFlag.value = false;
  }
}

// 显示添加权限对话框
function showAddPermissionDialog() {
  newPermission.value = {
    userId: '',
    featureKey: ''
  };
  permissionDialogVisible.value = true;
}

// 添加用户权限
async function addPermission() {
  if (!newPermission.value.userId || !newPermission.value.featureKey) {
    ElMessage.warning('请填写完整信息');
    return;
  }

  addingPermission.value = true;
  try {
    await featurePermissionService.addUserPermission(
      newPermission.value.userId,
      newPermission.value.featureKey
    );

    ElMessage.success('用户权限已添加');
    permissionDialogVisible.value = false;

    // 重新加载权限列表
    loadUserPermissions();
  } catch (error) {
    ElMessage.error('添加用户权限失败: ' + (error.message || '未知错误'));
    console.error('添加用户权限失败:', error);
  } finally {
    addingPermission.value = false;
  }
}

// 确认删除权限
function confirmDeletePermission(permission) {
  ElMessageBox.confirm(
    `确定要删除用户 ${permission.user?.email || permission.userId} 的 ${permission.featureKey} 权限吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    deletePermission(permission);
  }).catch(() => {
    // 用户取消删除
  });
}

// 删除用户权限
async function deletePermission(permission) {
  try {
    await featurePermissionService.deleteUserPermission(permission.id);
    ElMessage.success('用户权限已删除');

    // 从列表中移除
    userPermissions.value = userPermissions.value.filter(p => p.id !== permission.id);
  } catch (error) {
    ElMessage.error('删除用户权限失败');
    console.error('删除用户权限失败:', error);
  }
}

// 格式化日期
function formatDate(dateStr) {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString();
}

// 显示添加功能权限对话框
function showAddFeatureDialog() {
  newFeature.value = {
    featureKey: '',
    displayName: '',
    description: '',
    isEnabled: false
  };
  featureDialogVisible.value = true;
}

// 添加功能权限
async function addFeature() {
  if (!newFeature.value.featureKey) {
    ElMessage.warning('请填写功能标识');
    return;
  }

  // 检查功能标识是否已存在
  if (featureFlags.value.some(f => f.featureKey === newFeature.value.featureKey)) {
    ElMessage.warning('功能标识已存在');
    return;
  }

  addingFeature.value = true;
  try {
    await featurePermissionService.createFeature(
      newFeature.value.featureKey,
      newFeature.value.displayName,
      newFeature.value.description,
      newFeature.value.isEnabled
    );

    ElMessage.success('功能权限已添加');
    featureDialogVisible.value = false;

    // 重新加载功能标志列表
    loadFeatureFlags();
  } catch (error) {
    ElMessage.error('添加功能权限失败: ' + (error.message || '未知错误'));
    console.error('添加功能权限失败:', error);
  } finally {
    addingFeature.value = false;
  }
}

// 组件挂载时加载数据
onMounted(() => {
  refreshData();
});
</script>

<style scoped>
.feature-permissions-page {
  padding: 1.5rem;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.page-header h1 {
  margin: 0;
  font-size: 1.5rem;
  color: #303133;
}

.feature-tabs {
  background-color: #fff;
  border-radius: 0.25rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.05);
  padding: 1rem;
}

.tab-content {
  padding: 1rem 0;
}

.permissions-header,
.flags-header {
  margin-bottom: 1rem;
}

.user-email {
  font-size: 0.875rem;
  color: #909399;
  margin-top: 0.25rem;
}
</style>
