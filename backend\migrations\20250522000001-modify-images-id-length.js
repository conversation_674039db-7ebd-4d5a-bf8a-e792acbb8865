"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // 修改images表的id列长度，从21增加到25，不重新定义主键
      await queryInterface.sequelize.query(
        "ALTER TABLE `images` MODIFY `id` VARCHAR(25) NOT NULL;"
      );

      console.log("已成功修改images表的id列长度从21增加到25");
    } catch (error) {
      console.error("修改images表id列长度失败:", error.message);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      // 回滚操作 - 将id列长度改回21
      // 注意：如果已经存在超过21个字符的ID，此操作可能会失败
      await queryInterface.sequelize.query(
        "ALTER TABLE `images` MODIFY `id` VARCHAR(21) NOT NULL;"
      );

      console.log("已成功将images表的id列长度从25改回21");
    } catch (error) {
      console.error("回滚images表id列长度失败:", error.message);
      throw error;
    }
  },
};
