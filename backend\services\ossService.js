/**
 * OSS服务
 * 提供文件上传和管理功能
 */
const fs = require("fs");
const path = require("path");
const { v4: uuidv4 } = require("uuid");
const ossClient = require("../config/oss");

// 获取环境变量中的域名配置，如果没有则使用默认值
const DOMAIN = process.env.SERVER_DOMAIN || "https://echolab.club";
const OSS_PROXY_PATH = process.env.OSS_PROXY_PATH || "/oss-resources";

/**
 * 获取资源的代理URL
 * @param {string} ossKey OSS存储键
 * @returns {string} 代理URL（完整URL）
 */
function getProxyUrl(ossKey) {
  return `${DOMAIN}${OSS_PROXY_PATH}/${ossKey}`;
}

/**
 * 上传音频文件到OSS
 * @param {Buffer} audioBuffer 音频数据Buffer
 * @param {string} language 语言代码
 * @returns {Promise<Object>} 上传结果，包含URL和Key
 */
async function uploadAudio(audioBuffer, language) {
  try {
    // 构建OSS存储路径
    const ossKey = `audio/${language}/${Date.now()}_${uuidv4()}.mp3`;

    // 直接上传Buffer到OSS，不再使用临时文件
    await ossClient.put(ossKey, audioBuffer, {
      mime: "audio/mpeg",
    });

    // 返回代理URL
    return {
      url: getProxyUrl(ossKey),
      key: ossKey,
    };
  } catch (error) {
    console.error("上传音频到OSS失败:", error);
    throw new Error(`上传音频到OSS失败: ${error.message}`);
  }
}

/**
 * 从OSS删除音频文件
 * @param {string} ossKey OSS存储键
 * @returns {Promise<boolean>} 删除结果
 */
async function deleteAudio(ossKey) {
  try {
    await ossClient.delete(ossKey);
    return true;
  } catch (error) {
    console.error("从OSS删除音频失败:", error);
    return false;
  }
}

/**
 * 获取音频文件的签名URL（带过期时间）
 * @param {string} ossKey OSS存储键
 * @param {number} expires 过期时间（秒），默认1小时
 * @returns {Promise<string>} 签名URL
 */
async function getSignedUrl(ossKey, expires = 3600) {
  try {
    // 由于Nginx代理已经配置，我们直接返回代理URL
    // 这样可以避免签名URL的复杂性，并且利用Nginx的缓存机制
    return getProxyUrl(ossKey);
  } catch (error) {
    console.error("获取签名URL失败:", error);
    throw new Error(`获取签名URL失败: ${error.message}`);
  }
}

/**
 * 直接上传Buffer到OSS
 * @param {string} ossKey OSS存储键
 * @param {Buffer} buffer 文件数据Buffer
 * @param {Object} options 选项，如mime类型
 * @returns {Promise<Object>} 上传结果，包含URL和Key
 */
async function put(ossKey, buffer, options = {}) {
  try {
    // 上传到OSS
    await ossClient.put(ossKey, buffer, options);

    // 返回代理URL
    return {
      url: getProxyUrl(ossKey),
      key: ossKey,
    };
  } catch (error) {
    console.error("上传文件到OSS失败:", error);
    throw new Error(`上传文件到OSS失败: ${error.message}`);
  }
}

module.exports = {
  uploadAudio,
  deleteAudio,
  getSignedUrl,
  put,
};
