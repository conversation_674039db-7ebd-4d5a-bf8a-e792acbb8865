<template>
  <el-dialog :model-value="modelValue" @update:model-value="$emit('update:modelValue', $event)" :title="title"
    :width="width" :top="top" :append-to-body="true" :destroy-on-close="true" :modal="true"
    :close-on-click-modal="closeOnClickModal" :z-index="zIndex" :before-close="beforeClose" class="standard-dialog">
    <div class="standard-dialog-content">
      <slot></slot>
    </div>
    <template #footer>
      <slot name="footer">
        <div class="standard-dialog-footer">
          <!-- 额外按钮 -->
          <template v-if="extraButtons && extraButtons.length > 0">
            <el-button v-for="(btn, index) in extraButtons" :key="index" :type="btn.type || 'default'"
              @click="handleExtraButtonClick(btn, index)">
              {{ btn.text }}
            </el-button>
          </template>

          <!-- 取消按钮 -->
          <el-button @click="handleClose">{{ cancelText }}</el-button>

          <!-- 确认按钮 -->
          <el-button v-if="showConfirm" type="primary" @click="handleConfirm" :disabled="confirmDisabled">{{ confirmText
            }}</el-button>
        </div>
      </slot>
    </template>
  </el-dialog>
</template>

<script setup>


const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: '对话框'
  },
  width: {
    type: String,
    default: '60%'
  },
  top: {
    type: String,
    default: '10vh'
  },

  closeOnClickModal: {
    type: Boolean,
    default: false
  },
  zIndex: {
    type: Number,
    default: 2000
  },
  showConfirm: {
    type: Boolean,
    default: false
  },
  confirmText: {
    type: String,
    default: '确认'
  },
  cancelText: {
    type: String,
    default: '关闭'
  },
  beforeClose: {
    type: Function,
    default: null
  },
  // 额外按钮配置
  extraButtons: {
    type: Array,
    default: () => []
    // 每个按钮对象格式: { text: '按钮文本', type: '按钮类型', closeDialog: true/false, event: '事件名' }
  },
  // 确认按钮是否关闭对话框
  confirmClosesDialog: {
    type: Boolean,
    default: true
  },
  // 取消按钮是否关闭对话框
  cancelClosesDialog: {
    type: Boolean,
    default: true
  },
  // 确认按钮是否禁用
  confirmDisabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel', 'extra-button-click']);

function handleClose() {
  emit('cancel');
  if (props.cancelClosesDialog) {
    emit('update:modelValue', false);
  }
}

function handleConfirm() {
  emit('confirm');
  if (props.confirmClosesDialog) {
    emit('update:modelValue', false);
  }
}

function handleExtraButtonClick(button, index) {
  // 发出额外按钮点击事件
  emit('extra-button-click', { button, index });

  // 如果按钮配置了特定事件，也发出该事件
  if (button.event) {
    emit(button.event, button);
  }

  // 如果按钮配置了关闭对话框（默认为true），则关闭对话框
  if (button.closeDialog !== false) {
    emit('update:modelValue', false);
  }
}
</script>

<style scoped>
.standard-dialog-content {
  /* 恢复padding到content层 */
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  max-height: 65vh;
  /* 直接在CSS中设置固定值 */
  overflow: auto;
  /* 只在content层滚动 */
  box-sizing: border-box;
}

.standard-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.625rem;
}

:deep(.el-overlay-dialog) {
  overflow: hidden !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.el-dialog) {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  max-height: 90vh !important;
  max-width: 90vw !important;
  overflow: hidden !important;
  height: auto !important;
  position: fixed !important;
  /* 确保对话框固定定位 */
}

:deep(.el-dialog__header) {
  padding: 0.9375rem 1.25rem;
  margin: 0;
  border-bottom: 0.0625rem solid #ebeef5;
  flex-shrink: 0;
  /* 头部不收缩 */
}

:deep(.el-dialog__footer) {
  padding: 0.9375rem 1.25rem;
  border-top: 0.0625rem solid #ebeef5;
  flex-shrink: 0;
  /* 底部不收缩 */
}

:deep(.el-dialog__body) {
  padding: 0;
  /* 移除padding，让content自己处理 */
  box-sizing: border-box;
  overflow: hidden;
  /* 防止body层面的滚动 */
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}
</style>
