<!--
  文本序列节点组件
  管理文本序列和顺序
-->
<template>
  <BaseNode :nodeId="nodeId">
    <div class="text-sequence-node-content">
      <div class="source-section">
        <div class="section-title">源节点：</div>
        <div v-if="hasSourceNode" class="source-info">
          <el-tag v-for="sourceNode in sourceNodes" :key="sourceNode.id" size="small" class="source-tag"
            :closable="true" @close="removeSourceNode(sourceNode.id)">
            {{ getSourceNodeLabel(sourceNode) }}
          </el-tag>
        </div>
        <div v-else class="source-empty">
          <el-alert type="warning" :closable="false" show-icon>
            请连接一个文本内容节点作为输入源
          </el-alert>
        </div>
      </div>
      <div class="preview-section">
        <div class="preview-header">
          <div>序列预览：</div>
          <div class="button-group">
            <el-button size="small" type="primary" @click="showSequenceDialog">
              <el-icon class="el-icon--left">
                <i-ep-document />
              </el-icon>
              编辑序列
            </el-button>
          </div>
        </div>
        <div v-if="processedResult && processedResult.sequence && processedResult.sequence.length > 0"
          class="preview-info success-bg">
          已生成 {{ processedResult.sequence.length }} 个序列项
        </div>
        <div v-else class="preview-empty">
          序列将在连接源节点后自动生成，或点击"编辑序列"按钮手动编辑序列
        </div>
      </div>
      <div class="preview-section">
        <div class="preview-header">
          <div>播放环节管理：</div>
          <el-button size="small" type="primary" @click="showSectionsDialog">
            <el-icon class="el-icon--left">
              <i-ep-set-up />
            </el-icon>
            管理环节
          </el-button>
        </div>
        <div v-if="params.sections && params.sections.length > 0" class="preview-info success-bg">
          <div class="sections-header">
            <span class="sections-count">已生成 {{ params.sections.length }} 个环节</span>
          </div>
          <!-- 环节预览 - 优化UI -->
          <div class="sections-card-container">
            <div v-for="section in params.sections.slice(0, 2)" :key="section.id" class="section-card">
              <div class="section-card-header">
                <div class="section-card-title">
                  <span class="section-title-text">{{ section.title.replace(/^(通读|重复练习|序列环节): /, '') }}</span>
                </div>
                <div class="section-card-badges">
                  <el-tag v-if="section.repeatCount > 1" size="small" type="success">重复{{ section.repeatCount
                    }}次</el-tag>
                  <el-tag v-if="section.enableTranslation && section.translationLanguage" size="small" type="warning">
                    {{ getLanguageLabel(section.translationLanguage) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
          <div v-if="params.sections.length > 2" class="sections-more">
            <el-button size="small" link @click="showSectionsDialog">
              查看全部 {{ params.sections.length }} 个环节
            </el-button>
          </div>
        </div>
        <div v-else class="preview-empty">
          点击"管理环节"按钮添加环节
        </div>
      </div>
    </div>
    <!-- 序列编辑对话框 -->
    <standard-dialog v-model="sequenceDialogVisible" title="编辑序列" width="70%" :show-confirm="true" confirm-text="保存修改"
      @confirm="saveSequence" @cancel="sequenceDialogVisible = false">
      <div class="dialog-content">
        <div class="dialog-toolbar">
          <el-input v-model="searchText" placeholder="搜索内容" clearable prefix-icon="Search" style="width: 15rem;" />
          <div class="toolbar-actions">
            <el-button type="primary" @click="applySequenceChanges">应用更改</el-button>
          </div>
        </div>
        <div class="sequence-table-container">
          <el-table :data="filteredSequence" border style="width: 100%" row-key="id">
            <el-table-column label="序号" width="80" align="center">
              <template #default="{ $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column>
            <el-table-column label="内容" min-width="200">
              <template #default="{ row }">
                <div>{{ row.content }}</div>
              </template>
            </el-table-column>
            <el-table-column label="语言" width="100">
              <template #default="{ row }">
                <el-tag size="small" :type="getLanguageTagType(row.language)">
                  {{ getLanguageLabel(row.language) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="类型" width="100">
              <template #default="{ row }">
                <el-tag size="small" :type="getTypeTagType(row.type)">
                  {{ getTypeLabel(row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" align="center">
              <template #default="{ $index }">
                <el-button-group>
                  <el-button v-if="$index > 0" size="small" type="primary" circle title="上移"
                    @click="moveSequenceItem($index, 'up')">
                    <el-icon>
                      <i-ep-arrow-up />
                    </el-icon>
                  </el-button>
                  <el-button v-if="$index < tempSequence.length - 1" size="small" type="primary" circle title="下移"
                    @click="moveSequenceItem($index, 'down')">
                    <el-icon>
                      <i-ep-arrow-down />
                    </el-icon>
                  </el-button>
                  <el-button size="small" type="danger" circle title="删除" @click="removeSequenceItem($index)">
                    <el-icon>
                      <i-ep-delete />
                    </el-icon>
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </standard-dialog>
    <!-- 环节管理对话框 -->
    <standard-dialog v-model="sectionsDialogVisible" title="环节管理" width="80%" :show-confirm="true" confirm-text="保存修改"
      @confirm="saveSections" @cancel="sectionsDialogVisible = false">
      <div class="dialog-content sections-dialog-content" :key="activeSection?.id">
        <div class="sections-content">
          <!-- 左侧环节列表 -->
          <div class="left-panel">
            <div class="sections-panel">
              <div class="panel-header">
                <div class="panel-actions">
                  <el-button type="success" size="small" @click="autoCreateSections">
                    <el-icon class="el-icon--left">
                      <i-ep-set-up />
                    </el-icon>
                    自动创建
                  </el-button>
                  <el-button type="primary" size="small" @click="addSection">
                    <el-icon class="el-icon--left">
                      <i-ep-plus />
                    </el-icon>
                    添加环节
                  </el-button>
                </div>
              </div>
              <div v-if="tempSections.length === 0" class="empty-sections">
                <el-empty description="暂无环节，请点击自动创建或添加环节按钮" />
              </div>
              <div v-else class="sections-list">
                <div v-for="(section, index) in tempSections" :key="section.id" class="section-item"
                  :class="{ 'active': index === activeSectionIndex }" @click="selectSection(index)">
                  <div class="section-item-header">
                    <div class="section-item-title">
                      <span class="section-title-text">{{ section.title.replace(/^(通读|重复练习|序列环节): /, '') }}</span>
                    </div>
                    <div class="section-item-actions">
                      <el-button-group>
                        <el-button v-if="index > 0" size="small" type="primary" circle title="上移"
                          @click.stop="moveSection(index, 'up')">
                          <el-icon>
                            <i-ep-arrow-up />
                          </el-icon>
                        </el-button>
                        <el-button v-if="index < tempSections.length - 1" size="small" type="primary" circle title="下移"
                          @click.stop="moveSection(index, 'down')">
                          <el-icon>
                            <i-ep-arrow-down />
                          </el-icon>
                        </el-button>
                        <el-button size="small" type="success" circle title="复制" @click.stop="duplicateSection(index)">
                          <el-icon>
                            <i-ep-copy-document />
                          </el-icon>
                        </el-button>
                        <el-tooltip content="至少需要保留一个环节" :disabled="tempSections.length > 1">
                          <el-button size="small" type="danger" circle title="删除" @click.stop="removeSection(index)"
                            :disabled="tempSections.length <= 1">
                            <el-icon>
                              <i-ep-delete />
                            </el-icon>
                          </el-button>
                        </el-tooltip>
                      </el-button-group>
                    </div>
                  </div>
                  <div class="section-item-info">
                    <div class="section-item-mode">
                      <span v-if="section.processingMode === 'sequence'">
                        <el-tag size="small" type="warning">序列</el-tag>
                      </span>
                      <span
                        v-else-if="section.processingMode === 'source' && section.sourceNodeIds && section.sourceNodeIds.length > 0">
                        <el-tag size="small" type="info">源节点</el-tag>
                        <span v-if="section.sourceNodeIds.length === 1">
                          {{ getSourceNodeLabel(nodeStore.getNode(section.sourceNodeIds[0])) }}
                        </span>
                        <span v-else>
                          {{ section.sourceNodeIds.length }}个源节点
                        </span>
                      </span>
                      <span v-else class="warning-text">
                        <el-tag size="small" type="danger">未选择源节点</el-tag>
                      </span>
                    </div>
                    <div class="section-item-repeat" v-if="section.repeatCount > 1">
                      <el-tag size="small" type="success">重复{{ section.repeatCount }}次</el-tag>
                    </div>
                  </div>
                  <div v-if="section.description" class="section-item-desc">
                    {{ section.description }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 右侧环节详情 -->
          <div class="right-panel">
            <div class="section-detail-panel">
              <div v-if="!activeSection" class="empty-detail">
                <el-empty description="请选择或创建环节" />
              </div>
              <template v-else>
                <div class="panel-header">
                  <h3>详细配置</h3>
                </div>
                <el-form :model="activeSection" label-width="6.25rem">
                  <el-form-item label="环节标题">
                    <el-input v-model="activeSection.title" @change="updateSectionParams" />
                  </el-form-item>
                  <el-form-item label="处理方式">
                    <el-radio-group v-model="activeSection.processingMode" @change="handleProcessingModeChange">
                      <el-radio value="source">基于源节点</el-radio>
                      <el-radio value="sequence">基于序列</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="源节点" v-if="activeSection.processingMode === 'source'">
                    <el-select v-model="activeSection.sourceNodeIds" @change="handleSourceNodeChange" multiple>
                      <el-option v-for="node in sourceNodes" :key="node.id" :label="getSourceNodeLabel(node)"
                        :value="node.id" />
                    </el-select>
                    <div class="form-help">选择此环节处理的源节点内容（可多选）</div>
                  </el-form-item>
                  <el-form-item label="序列处理" v-else>
                    <div class="form-help">此环节将处理整个序列的内容，按照序列中的顺序</div>
                  </el-form-item>
                  <el-form-item label="环节描述">
                    <el-input v-model="activeSection.description" type="textarea" :rows="2"
                      @change="updateSectionParams" />
                  </el-form-item>
                  <el-form-item label="用户可编辑">
                    <el-switch v-model="activeSection.userEditable" @change="updateSectionParams" />
                  </el-form-item>
                  <!-- 通用设置 - 适用于所有环节类型 -->
                  <el-divider>播放设置</el-divider>
                  <el-form-item label="停顿时长">
                    <NumberEditor v-model="activeSection.pauseDuration" :min="0" :max="5000" :step="100" suffix="ms"
                      @change="updateSectionParams" />
                    <div class="form-help">句子之间的停顿时长（毫秒）</div>
                  </el-form-item>
                  <!-- 所有环节都显示重复设置 -->
                  <el-divider content-position="center">重复设置</el-divider>
                  <el-form-item label="重复次数">
                    <NumberEditor v-model="activeSection.repeatCount" :min="1" :max="10"
                      @change="handleRepeatCountChange" class="full-width" />
                  </el-form-item>
                  <!-- 自定义重复设置 -->
                  <el-divider content-position="center">高级设置</el-divider>
                  <!-- 自定义重复设置表格 -->
                  <div class="custom-repeat-settings">
                    <div class="custom-repeat-header">
                      <div class="repeat-index-header">重复次数</div>
                      <div class="repeat-speed-header">播放速度</div>
                      <div class="repeat-pause-header">停顿时长(ms)</div>
                    </div>
                    <div class="custom-repeat-body">
                      <template v-if="activeSection.repeatSpeeds && activeSection.repeatPauses">
                        <div v-for="(_, index) in Array(activeSection.repeatCount || 1)"
                          :key="activeSection.id + '-' + index" class="custom-repeat-row">
                          <div class="repeat-index">第 {{ index + 1 }} 次</div>
                          <div class="repeat-speed">
                            <NumberEditor v-model="activeSection.repeatSpeeds[index]" :min="0.9" :max="1.1" :step="0.1"
                              :precision="1" suffix="x" class="full-width" @change="updateSectionParams" />
                          </div>
                          <div class="repeat-pause">
                            <NumberEditor v-model="activeSection.repeatPauses[index]" :min="0" :max="10000" :step="100"
                              suffix="ms" class="full-width" @change="updateSectionParams" />
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                  <el-divider content-position="center">翻译设置</el-divider>
                  <template v-if="availableTranslationLanguages.length > 0">
                    <div class="setting-header">
                      <div class="setting-title">
                        <span>插入翻译</span>
                        <el-tooltip content="在重复播放中插入翻译音频" placement="top">
                          <el-icon class="question-icon">
                            <i-ep-question-filled />
                          </el-icon>
                        </el-tooltip>
                      </div>
                      <el-switch v-model="activeSection.enableTranslation" @change="updateSectionParams" />
                    </div>
                    <template v-if="activeSection.enableTranslation">
                      <el-row :gutter="12">
                        <el-col :span="12">
                          <el-form-item label="翻译语言" label-width="6.25rem">
                            <el-select v-model="activeSection.translationLanguage" placeholder="选择翻译语言"
                              @change="updateSectionParams" class="full-width">
                              <el-option v-for="lang in availableTranslationLanguages" :key="lang.value"
                                :label="lang.label" :value="lang.value" />
                              <el-empty v-if="availableTranslationLanguages.length === 0" description="没有可用的翻译语言"
                                :image-size="60">
                              </el-empty>
                            </el-select>
                            <div v-if="!activeSection.translationLanguage" class="form-help error-text">
                              请选择翻译语言
                            </div>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="插入位置" label-width="6.25rem">
                            <NumberEditor v-model="activeSection.translationPosition" :min="1"
                              :max="activeSection.repeatCount" @change="updateSectionParams" class="full-width" />
                            <div class="form-help">在第几次重复后插入翻译（1-{{ activeSection.repeatCount }}）</div>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </template>
                  </template>
                  <template v-else>
                    <el-alert type="info" :closable="false" show-icon>
                      <template #title>
                        没有可用的翻译语言
                      </template>
                      <template #default>
                        请确保画布中存在资源管理节点并配置了翻译语言
                      </template>
                    </el-alert>
                  </template>
                  <el-divider content-position="center">关键词设置</el-divider>
                  <div class="setting-header">
                    <div class="setting-title">
                      <span>插入关键词</span>
                      <el-tooltip content="在重复播放中插入关键词音频" placement="top">
                        <el-icon class="question-icon">
                          <i-ep-question-filled />
                        </el-icon>
                      </el-tooltip>
                    </div>
                    <el-switch v-model="activeSection.enableKeywords" @change="updateSectionParams" />
                  </div>
                  <template v-if="activeSection.enableKeywords">
                    <el-row :gutter="12">
                      <el-col :span="12">
                        <el-form-item label="重复次数" label-width="6.25rem">
                          <NumberEditor v-model="activeSection.keywordRepeatCount" :min="1" :max="5"
                            @change="updateSectionParams" class="full-width" />
                          <div class="form-help">关键词重复播放的次数</div>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="插入位置" label-width="6.25rem">
                          <NumberEditor v-model="activeSection.keywordPosition" :min="1"
                            :max="activeSection.repeatCount" @change="updateSectionParams" class="full-width" />
                          <div class="form-help">在第几次重复后插入关键词（1-{{ activeSection.repeatCount }}）</div>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </template>
                </el-form>
              </template>
            </div>
          </div>
        </div>
      </div>
    </standard-dialog>
  </BaseNode>
</template>
<script setup>
import { computed, ref, watch, reactive } from 'vue';
import { useNodeStore } from '@/core/stores/nodeStore';
import BaseNode from './BaseNode.vue';
import StandardDialog from '../common/StandardDialog.vue';
import NumberEditor from '../common/NumberEditor.vue';
import nodeFactory from '@/core/factories/NodeFactory';
import { ElMessage } from 'element-plus';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { getLanguageLabel, getLanguageTagType } from '@/config/languages';
const props = defineProps({
  nodeId: {
    type: String,
    required: true
  }
});
const emit = defineEmits(['node-updated']);
const nodeStore = useNodeStore();
// 节点数据
const node = computed(() => nodeStore.getNode(props.nodeId));
// 工厂函数，统一生成响应式section对象
function createSection(options = {}) {
  const repeatCount = options.repeatCount || 1;
  return reactive({
    id: options.id || `section_${Date.now()}`,
    title: options.title || '标题',
    description: options.description || '',
    processingMode: options.processingMode || 'sequence',
    userEditable: true,
    pauseDuration: options.pauseDuration || 3000,
    repeatCount,
    enableTranslation: options.enableTranslation || false,
    translationLanguage: options.translationLanguage || '',
    translationPosition: options.translationPosition || 2,
    enableKeywords: options.enableKeywords || false,
    keywordRepeatCount: options.keywordRepeatCount || 2,
    keywordPosition: options.keywordPosition || 2,
    repeatSpeeds: reactive(Array.isArray(options.repeatSpeeds) ? [...options.repeatSpeeds] : Array(repeatCount).fill(1.0)),
    repeatPauses: reactive(Array.isArray(options.repeatPauses) ? [...options.repeatPauses] : Array(repeatCount).fill(options.pauseDuration || 3000)),
    ...options
  });
}
// 节点参数
const params = computed({
  get: () => {
    // 如果节点存在但没有环节，创建默认环节并写入 store
    if (node.value && (!node.value.params.sections || node.value.params.sections.length === 0)) {
      const defaultSection = createSection();
      const updatedParams = {
        ...node.value.params,
        sections: [defaultSection]
      };
      nodeStore.updateNodeParams(props.nodeId, updatedParams);
      return updatedParams;
    }
    return node.value?.params || { sequence: [], sections: [createSection()] };
  },
  set: (value) => nodeStore.updateNodeParams(props.nodeId, value)
});
// 源节点
const sourceNodes = computed(() => {
  if (!node.value || !node.value.sourceIds || node.value.sourceIds.length === 0) {
    return [];
  }
  return node.value.sourceIds.map(id => nodeStore.getNode(id)).filter(Boolean);
});
// 是否有源节点
const hasSourceNode = computed(() => sourceNodes.value.length > 0);
// 处理结果
const processedResult = ref(null);
// 类型标签
const typeLabels = {
  'text': '文本',
  'annotated': '标注',
  'translation': '翻译'
};
// 对话框状态
const sequenceDialogVisible = ref(false);
const sectionsDialogVisible = ref(false);
const searchText = ref('');
// 临时序列数据（用于对话框编辑）
const tempSequence = ref([]);
const hasSequenceChanged = ref(false);
// 临时环节数据（用于对话框编辑）
const tempSections = ref([]);
const hasSectionsChanged = ref(false);
const activeSectionIndex = ref(0);
// 过滤序列
const filteredSequence = computed(() => {
  if (!searchText.value) return tempSequence.value;
  return tempSequence.value.filter(item =>
    item.content.toLowerCase().includes(searchText.value.toLowerCase())
  );
});
// 使用计算属性直接管理当前选中的环节，确保响应式连接
const activeSection = computed(() => tempSections.value[activeSectionIndex.value] || null);
// 获取画布中的所有资源管理节点
const resourceNodes = computed(() => {
  return Object.values(nodeStore.nodes).filter(node => node.type === 'resource');
});
// 可用的翻译语言
const availableTranslationLanguages = computed(() => {
  // 从资源管理节点中获取可用的翻译语言
  const languages = new Set();
  // 从画布中的所有资源管理节点获取翻译语言
  resourceNodes.value.forEach(resourceNode => {
    if (resourceNode.processedResult && resourceNode.processedResult.translations) {
      Object.keys(resourceNode.processedResult.translations).forEach(lang => {
        languages.add(lang);
      });
    } else if (resourceNode.params && resourceNode.params.translations) {
      Object.keys(resourceNode.params.translations).forEach(lang => {
        languages.add(lang);
      });
    } else if (resourceNode.params && resourceNode.params.targets) {
      resourceNode.params.targets.forEach(lang => {
        languages.add(lang);
      });
    }
  });
  return Array.from(languages).map(lang => {
    return {
      value: lang,
      label: getLanguageLabel(lang)
    };
  });
});
// 获取源节点标签
function getSourceNodeLabel(sourceNode) {
  if (!sourceNode) return '未知节点';
  // 如果有自定义名称，优先显示
  if (sourceNode.customName) {
    return sourceNode.customName;
  }
  // 获取节点类型标签
  const typeLabel = nodeFactory.getNodeTypeConfig(sourceNode.type)?.label || sourceNode.type;
  // 使用节点编号
  const nodeNumber = sourceNode.number || 1;
  return `${typeLabel} #${nodeNumber}`;
}
// 获取类型标签
function getTypeLabel(type) {
  return typeLabels[type] || type;
}
// 获取类型标签类型
function getTypeTagType(type) {
  switch (type) {
    case 'text':
      return '';
    case 'annotated':
      return 'warning';
    case 'translation':
      return 'success';
    default:
      return 'info';
  }
}
// 移除源节点
function removeSourceNode(sourceId) {
  nodeStore.disconnectNodes(sourceId, props.nodeId);
}
// 显示序列对话框
function showSequenceDialog() {
  if (!processedResult.value || !processedResult.value.sequence) {
    ElMessage.warning('没有可编辑的序列');
    return;
  }
  // 创建临时数据副本
  tempSequence.value = JSON.parse(JSON.stringify(processedResult.value.sequence));
  hasSequenceChanged.value = false;
  // 显示对话框
  sequenceDialogVisible.value = true;
}
// 移动序列项
function moveSequenceItem(index, direction) {
  if (direction === 'up' && index > 0) {
    // 上移
    const temp = tempSequence.value[index];
    tempSequence.value[index] = tempSequence.value[index - 1];
    tempSequence.value[index - 1] = temp;
    hasSequenceChanged.value = true;
  } else if (direction === 'down' && index < tempSequence.value.length - 1) {
    // 下移
    const temp = tempSequence.value[index];
    tempSequence.value[index] = tempSequence.value[index + 1];
    tempSequence.value[index + 1] = temp;
    hasSequenceChanged.value = true;
  }
}
// 移除序列项
function removeSequenceItem(index) {
  tempSequence.value.splice(index, 1);
  hasSequenceChanged.value = true;
}
// 应用序列更改
function applySequenceChanges() {
  if (!hasSequenceChanged.value) {
    ElMessage.info('没有更改需要应用');
    return;
  }
  // 更新节点参数，确保保留sections
  const updatedParams = {
    ...params.value,
    sequence: tempSequence.value,
    sections: params.value.sections || []
  };
  nodeStore.updateNodeParams(props.nodeId, updatedParams);
  // 重新处理节点
  processNode();
  hasSequenceChanged.value = false;
  ElMessage.success('序列更改已应用');
}
// 保存序列
function saveSequence() {
  if (hasSequenceChanged.value) {
    // 应用更改
    applySequenceChanges();
  }
  // 关闭对话框
  sequenceDialogVisible.value = false;
}
// 显示环节管理对话框
function showSectionsDialog() {
  tempSections.value = (params.value.sections && params.value.sections.length > 0)
    ? params.value.sections.map(section => createSection(section))
    : [];
  hasSectionsChanged.value = false;
  activeSectionIndex.value = tempSections.value.length > 0 ? 0 : -1;
  sectionsDialogVisible.value = true;
}
// 添加环节
function addSection() {
  const newSection = createSection();
  tempSections.value.push(newSection);
  activeSectionIndex.value = tempSections.value.length - 1;
  hasSectionsChanged.value = true;
}
// 选择环节
function selectSection(index) {
  activeSectionIndex.value = index;
  if (activeSection.value) {
    initCustomRepeatSettings();
  }
}
// 移动环节
function moveSection(index, direction) {
  if (direction === 'up' && index > 0) {
    // 上移
    const temp = tempSections.value[index];
    tempSections.value[index] = tempSections.value[index - 1];
    tempSections.value[index - 1] = temp;
    // 更新选中的环节索引
    activeSectionIndex.value = index - 1;
    // 标记为已更改
    hasSectionsChanged.value = true;
  } else if (direction === 'down' && index < tempSections.value.length - 1) {
    // 下移
    const temp = tempSections.value[index];
    tempSections.value[index] = tempSections.value[index + 1];
    tempSections.value[index + 1] = temp;
    // 更新选中的环节索引
    activeSectionIndex.value = index + 1;
    // 标记为已更改
    hasSectionsChanged.value = true;
  }
}
// 移除环节
function removeSection(index) {
  // 如果只有一个环节，不允许删除
  if (tempSections.value.length <= 1) {
    ElMessage.warning('至少需要保留一个环节');
    return;
  }
  tempSections.value.splice(index, 1);
  // 如果删除的是当前选中的环节，更新选中的环节索引
  if (index === activeSectionIndex.value) {
    activeSectionIndex.value = Math.min(index, tempSections.value.length - 1);
    if (activeSectionIndex.value < 0) {
      activeSectionIndex.value = 0;
    }
  } else if (index < activeSectionIndex.value) {
    // 如果删除的环节在当前选中的环节之前，更新选中的环节索引
    activeSectionIndex.value--;
  }
  // 标记为已更改
  hasSectionsChanged.value = true;
}
// 复制环节
function duplicateSection(index) {
  const sourceSection = tempSections.value[index];
  const copy = createSection({
    ...JSON.parse(JSON.stringify(sourceSection)),
    id: `section_${Date.now()}`,
    title: `${sourceSection.title} (复制)`
  });
  tempSections.value.splice(index + 1, 0, copy);
  activeSectionIndex.value = index + 1;
  hasSectionsChanged.value = true;
  ElMessage.success('环节已复制');
}
// 处理处理方式变更
function handleProcessingModeChange() {
  if (!activeSection.value) return;
  if (activeSection.value.processingMode === 'sequence') {
    delete activeSection.value.sourceNodeIds;
    if (activeSection.value.sourceNodeId) {
      delete activeSection.value.sourceNodeId;
    }
    // 更新环节标题
    activeSection.value.title = `标题`;
    if (activeSection.value.repeatCount > 1) {
      activeSection.value.title += ` (重复${activeSection.value.repeatCount}次)`;
    }
  } else {
    activeSection.value.sourceNodeIds = [];
    if (sourceNodes.value.length > 0) {
      activeSection.value.sourceNodeIds = [sourceNodes.value[0].id];
    }
    // 更新环节标题
    updateSectionTitle();
  }
  // 标记为已更改
  hasSectionsChanged.value = true;
}
// 处理源节点变更
function handleSourceNodeChange() {
  if (!activeSection.value) return;
  if (!activeSection.value.sourceNodeIds) {
    activeSection.value.sourceNodeIds = [];
  }
  // 更新环节标题
  updateSectionTitle();
  // 标记为已更改
  hasSectionsChanged.value = true;
}
// 更新环节标题
function updateSectionTitle() {
  if (!activeSection.value) return;
  // 更新环节标题
  if (activeSection.value.processingMode === 'sequence' ||
    (!activeSection.value.sourceNodeIds || activeSection.value.sourceNodeIds.length === 0)) {
    // 保留用户设置的标题，如果没有则使用默认标题
    if (!activeSection.value.title || activeSection.value.title.startsWith('基于序列')) {
      activeSection.value.title = `标题`;
      // 如果有重复次数大于1，添加重复次数信息
      if (activeSection.value.repeatCount > 1) {
        activeSection.value.title += ` (重复${activeSection.value.repeatCount}次)`;
      }
    }
  } else {
    // 获取源节点标签
    let sourceNodesLabel = '';
    if (activeSection.value.sourceNodeIds && activeSection.value.sourceNodeIds.length > 0) {
      const selectedNodes = activeSection.value.sourceNodeIds
        .map(id => nodeStore.getNode(id))
        .filter(Boolean);
      if (selectedNodes.length === 1) {
        sourceNodesLabel = getSourceNodeLabel(selectedNodes[0]);
        // 如果有重复次数大于1，添加重复次数信息
        if (activeSection.value.repeatCount > 1) {
          sourceNodesLabel += ` (重复${activeSection.value.repeatCount}次)`;
        }
      } else if (selectedNodes.length > 1) {
        sourceNodesLabel = `${selectedNodes.length}个源节点`;
        // 如果有重复次数大于1，添加重复次数信息
        if (activeSection.value.repeatCount > 1) {
          sourceNodesLabel += ` (重复${activeSection.value.repeatCount}次)`;
        }
      }
    }
    if (!sourceNodesLabel) {
      sourceNodesLabel = '未选择源节点';
    }
    // 更新环节标题
    activeSection.value.title = sourceNodesLabel;
  }
}
// 更新环节参数
function updateSectionParams() {
  if (!activeSection.value) return;
  // 标记为已更改
  hasSectionsChanged.value = true;
}
// 处理重复次数变化
function handleRepeatCountChange() {
  if (!activeSection.value) return;
  // 获取重复次数
  const repeatCount = activeSection.value.repeatCount || 1;
  // 确保翻译插入位置不超过重复次数
  if (activeSection.value.enableTranslation) {
    // 如果未设置翻译插入位置，设置为2
    if (!activeSection.value.translationPosition) {
      activeSection.value.translationPosition = 2;
    }
    // 确保不超过重复次数
    if (activeSection.value.translationPosition > repeatCount) {
      activeSection.value.translationPosition = repeatCount;
    }
  }
  // 确保关键词插入位置不超过重复次数
  // 如果未设置关键词插入位置，设置为2
  if (!activeSection.value.keywordPosition) {
    activeSection.value.keywordPosition = 2;
  }
  // 确保不超过重复次数
  if (activeSection.value.keywordPosition > repeatCount) {
    activeSection.value.keywordPosition = repeatCount;
  }
  // 确保有关键词重复次数设置
  if (!activeSection.value.keywordRepeatCount) {
    activeSection.value.keywordRepeatCount = 2;
  }
  // 更新重复速度和停顿时长数组
  initCustomRepeatSettings();
  // 更新环节标题
  updateSectionTitle();
  // 更新节点参数
  updateSectionParams();
}
// 初始化自定义重复设置
function initCustomRepeatSettings() {
  if (!activeSection.value) return;
  // 获取重复次数
  const repeatCount = activeSection.value.repeatCount || 1;
  const baseSpeed = 1.0;
  const basePause = activeSection.value.pauseDuration || 3000;
  // 确保关键词相关字段被初始化
  if (!activeSection.value.keywordRepeatCount) {
    activeSection.value.keywordRepeatCount = 2;
  }
  if (!activeSection.value.keywordPosition) {
    activeSection.value.keywordPosition = Math.min(2, repeatCount);
  }
  if (activeSection.value.enableKeywords === undefined) {
    activeSection.value.enableKeywords = false;
  }
  // 创建或调整重复速度数组
  if (!activeSection.value.repeatSpeeds || !Array.isArray(activeSection.value.repeatSpeeds)) {
    activeSection.value.repeatSpeeds = Array(repeatCount).fill(baseSpeed);
  } else {
    // 调整数组长度为重复次数
    while (activeSection.value.repeatSpeeds.length < repeatCount) {
      activeSection.value.repeatSpeeds.push(baseSpeed);
    }
    // 如果数组过长，截断
    if (activeSection.value.repeatSpeeds.length > repeatCount) {
      activeSection.value.repeatSpeeds = activeSection.value.repeatSpeeds.slice(0, repeatCount);
    }
  }
  // 创建或调整重复停顿时长数组
  if (!activeSection.value.repeatPauses || !Array.isArray(activeSection.value.repeatPauses)) {
    activeSection.value.repeatPauses = Array(repeatCount).fill(basePause);
  } else {
    // 调整数组长度为重复次数
    while (activeSection.value.repeatPauses.length < repeatCount) {
      activeSection.value.repeatPauses.push(basePause);
    }
    // 如果数组过长，截断
    if (activeSection.value.repeatPauses.length > repeatCount) {
      activeSection.value.repeatPauses = activeSection.value.repeatPauses.slice(0, repeatCount);
    }
  }
}
// 保存环节
function saveSections() {
  if (hasSectionsChanged.value) {
    // 检查是否有源节点（仅对基于源节点的环节需要）
    const hasSourceBasedSections = tempSections.value.some(section =>
      section.processingMode === 'source'
    );
    if (hasSourceBasedSections && sourceNodes.value.length === 0) {
      ElMessage.warning('没有可用的源节点，无法保存基于源节点的环节');
      return;
    }
    // 默认源节点（用于没有指定源节点的环节）
    const defaultSourceNode = sourceNodes.value.length > 0 ? sourceNodes.value[0] : null;
    // 确保每个环节都有正确的配置和处理方式
    tempSections.value.forEach(section => {
      if (section.processingMode === 'source') {
        // 确保有sourceNodeIds数组
        if (!section.sourceNodeIds) {
          section.sourceNodeIds = [];
        }
        if (section.sourceNodeId && !section.sourceNodeIds.includes(section.sourceNodeId)) {
          section.sourceNodeIds = [section.sourceNodeId];
          delete section.sourceNodeId;
        }
        if (section.sourceNodeIds.length === 0 && defaultSourceNode) {
          section.sourceNodeIds = [defaultSourceNode.id];
        }
        // 更新环节标题
        let sourceNodesLabel = '';
        if (section.sourceNodeIds.length === 1) {
          const sourceNode = nodeStore.getNode(section.sourceNodeIds[0]);
          if (sourceNode) {
            sourceNodesLabel = getSourceNodeLabel(sourceNode);
            // 如果有重复次数大于1，添加重复次数信息
            if (section.repeatCount > 1) {
              sourceNodesLabel += ` (重复${section.repeatCount}次)`;
            }
          }
        } else if (section.sourceNodeIds.length > 1) {
          sourceNodesLabel = `${section.sourceNodeIds.length}个源节点`;
          // 如果有重复次数大于1，添加重复次数信息
          if (section.repeatCount > 1) {
            sourceNodesLabel += ` (重复${section.repeatCount}次)`;
          }
        } else {
          sourceNodesLabel = '未选择源节点';
        }
        section.title = sourceNodesLabel;
      } else {
        // 移除源节点IDs
        delete section.sourceNodeIds;
        // 保留用户设置的标题，如果没有则使用默认标题
        if (!section.title || section.title.startsWith('基于序列')) {
          section.title = `标题`;
          // 如果有重复次数大于1，添加重复次数信息
          if (section.repeatCount > 1) {
            section.title += ` (重复${section.repeatCount}次)`;
          }
        }
      }
      if (section.type) {
        delete section.type;
      }
      // 确保有重复次数设置
      if (!section.repeatCount) {
        section.repeatCount = 1; // 默认重复1次
      }
      // 确保有翻译插入位置设置
      if (section.enableTranslation && !section.translationPosition) {
        section.translationPosition = 2; // 默认在第2次重复后插入翻译
      }
      // 确保翻译插入位置不超过重复次数
      if (section.enableTranslation && section.translationPosition > section.repeatCount) {
        section.translationPosition = section.repeatCount;
      }
      // 如果启用了翻译，但没有选择翻译语言，禁用翻译功能
      if (section.enableTranslation && !section.translationLanguage) {
        section.enableTranslation = false;
      }
      // 确保有关键词设置
      if (section.enableKeywords === undefined) {
        section.enableKeywords = false;
      }
      // 确保有关键词重复次数设置
      if (!section.keywordRepeatCount) {
        section.keywordRepeatCount = 2; // 默认关键词重复2次
      }
      // 确保有关键词插入位置设置
      if (!section.keywordPosition) {
        section.keywordPosition = 2; // 默认在第2次重复后插入关键词
      }
      // 确保关键词插入位置不超过重复次数
      if (section.enableKeywords && section.keywordPosition > section.repeatCount) {
        section.keywordPosition = section.repeatCount;
      }
      // 确保重复速度和停顿时长数组存在且长度正确
      const repeatCount = section.repeatCount || 1;
      const baseSpeed = 1.0;
      const basePause = section.pauseDuration || 3000;
      // 确保重复速度数组存在且长度正确
      if (!section.repeatSpeeds || !Array.isArray(section.repeatSpeeds)) {
        section.repeatSpeeds = Array(repeatCount).fill(baseSpeed);
      } else {
        // 调整数组长度
        while (section.repeatSpeeds.length < repeatCount) {
          section.repeatSpeeds.push(baseSpeed);
        }
        if (section.repeatSpeeds.length > repeatCount) {
          section.repeatSpeeds = section.repeatSpeeds.slice(0, repeatCount);
        }
      }
      // 确保重复停顿时长数组存在且长度正确
      if (!section.repeatPauses || !Array.isArray(section.repeatPauses)) {
        section.repeatPauses = Array(repeatCount).fill(basePause);
      } else {
        // 调整数组长度
        while (section.repeatPauses.length < repeatCount) {
          section.repeatPauses.push(basePause);
        }
        if (section.repeatPauses.length > repeatCount) {
          section.repeatPauses = section.repeatPauses.slice(0, repeatCount);
        }
      }
    });
    // 更新节点参数
    const updatedParams = { ...params.value, sections: tempSections.value };
    nodeStore.updateNodeParams(props.nodeId, updatedParams);
    // 重新处理节点
    processNode();
    hasSectionsChanged.value = false;
    ElMessage.success('环节更改已保存');
  }
  // 关闭对话框
  sectionsDialogVisible.value = false;
}
// 自动创建环节
function autoCreateSections() {
  // 检查源节点
  if (sourceNodes.value.length === 0) {
    ElMessage.warning('没有可用的源节点，无法创建环节');
    return;
  }
  const newSections = [];
  // 为每个源节点创建环节
  sourceNodes.value.forEach(sourceNode => {
    const sourceNodeLabel = getSourceNodeLabel(sourceNode);
    // 为每个源节点创建单次播放环节（重复次数为1的环节）
    const sectionId1 = `section_read_${Date.now()}_${sourceNode.id}`;
    newSections.push({
      id: sectionId1,
      title: sourceNodeLabel,
      description: `单次播放环节，源自 ${sourceNodeLabel}`,
      sourceNodeIds: [sourceNode.id], // 使用数组存储源节点ID
      processingMode: 'source', // 基于源节点
      userEditable: true,
      pauseDuration: 3000,
      repeatCount: 1, // 重复1次
      enableTranslation: false,
      translationLanguage: '',
      translationPosition: 1,
      enableKeywords: false, // 默认不启用关键词
      keywordRepeatCount: 2, // 默认关键词重复2次
      keywordPosition: 1, // 默认在第1次重复后插入关键词
      repeatSpeeds: [1.0], // 初始化重复速度数组
      repeatPauses: [3000] // 初始化重复停顿时长数组
    });
    // 为每个源节点创建重复环节（重复次数大于1的环节）
    const sectionId2 = `section_repeat_${Date.now()}_${sourceNode.id}`;
    newSections.push({
      id: sectionId2,
      title: `${sourceNodeLabel} (重复4次)`,
      description: `重复播放环节，源自 ${sourceNodeLabel}`,
      sourceNodeIds: [sourceNode.id], // 使用数组存储源节点ID
      processingMode: 'source', // 基于源节点
      userEditable: true,
      pauseDuration: 3000,
      repeatCount: 4, // 默认重复4次
      enableTranslation: false, // 默认不启用翻译，需要用户手动选择翻译语言
      translationLanguage: '', // 不自动选择翻译语言
      translationPosition: 2, // 默认在第2次重复后插入翻译
      enableKeywords: false, // 默认不启用关键词
      keywordRepeatCount: 2, // 默认关键词重复2次
      keywordPosition: 2, // 默认在第2次重复后插入关键词
      repeatSpeeds: [1.0, 1.0, 1.0, 1.0], // 初始化重复速度数组
      repeatPauses: [3000, 3000, 3000, 3000] // 初始化重复停顿时长数组
    });
  });
  // 更新临时环节数据
  tempSections.value = newSections;
  // 选中第一个环节
  activeSectionIndex.value = newSections.length > 0 ? 0 : -1;
  // 标记为已更改
  hasSectionsChanged.value = true;
  ElMessage.success(`已为 ${sourceNodes.value.length} 个源节点创建环节`);
}
// 处理节点
function processNode() {
  try {
    // 获取处理结果
    const result = nodeStore.processNode(props.nodeId);
    // 确保处理结果中包含环节信息
    if (result && !result.sections && params.value.sections) {
      result.sections = params.value.sections;
    }
    // 更新处理结果
    processedResult.value = result;
    // 通知父组件节点已更新
    emit('node-updated', props.nodeId);
  } catch (error) {
    console.error('处理节点失败:', error);
    processedResult.value = null;
  }
}
// 监听源节点变化
watch(sourceNodes, (newSourceNodes, oldSourceNodes) => {
  // 检查节点是否仍然存在
  if (nodeStore.getNode(props.nodeId)) {
    // 检查是否有新的源节点被添加
    if (newSourceNodes && (!oldSourceNodes || newSourceNodes.length > oldSourceNodes.length)) {
      // 延迟一点时间，确保连接已经完成
      setTimeout(() => {
        processNode();
      }, 100);
    }
    // 检查是否有源节点被删除
    if (oldSourceNodes && oldSourceNodes.length > newSourceNodes.length) {
      // 如果所有源节点都被删除，清空环节和序列数据
      if (newSourceNodes.length === 0) {
        // 清空环节和序列数据
        const updatedParams = {
          ...params.value,
          sections: [],
          sequence: []
        };
        // 更新节点参数
        nodeStore.updateNodeParams(props.nodeId, updatedParams);
        // 重新处理节点
        processNode();
        ElMessage.info('已清空环节和序列数据');
        return;
      }
      // 获取当前存在的源节点ID列表
      const currentSourceNodeIds = newSourceNodes.map(node => node.id);
      // 检查环节中是否有引用已删除源节点的情况
      if (params.value.sections && params.value.sections.length > 0) {
        let sectionsUpdated = false;
        const updatedSections = params.value.sections.map(section => {
          // 如果环节基于源节点
          if (section.processingMode === 'source') {
            // 创建新的环节对象（避免直接修改原对象）
            const updatedSection = { ...section };
            if (updatedSection.sourceNodeId) {
              // 如果sourceNodeId不在当前可用的源节点中
              if (!currentSourceNodeIds.includes(updatedSection.sourceNodeId)) {
                // 如果有可用的源节点，更新为第一个可用的源节点
                if (currentSourceNodeIds.length > 0) {
                  // 初始化sourceNodeIds数组
                  updatedSection.sourceNodeIds = [currentSourceNodeIds[0]];
                } else {
                  // 没有可用的源节点，初始化为空数组
                  updatedSection.sourceNodeIds = [];
                }
                // 删除旧的sourceNodeId
                delete updatedSection.sourceNodeId;
                sectionsUpdated = true;
              } else {
                // sourceNodeId仍然有效，转换为sourceNodeIds数组
                updatedSection.sourceNodeIds = [updatedSection.sourceNodeId];
                delete updatedSection.sourceNodeId;
                sectionsUpdated = true;
              }
            }
            // 处理新版本的sourceNodeIds数组
            else if (updatedSection.sourceNodeIds && updatedSection.sourceNodeIds.length > 0) {
              // 过滤掉已删除的源节点ID
              const validSourceNodeIds = updatedSection.sourceNodeIds.filter(id =>
                currentSourceNodeIds.includes(id)
              );
              // 如果有源节点被删除
              if (validSourceNodeIds.length < updatedSection.sourceNodeIds.length) {
                updatedSection.sourceNodeIds = validSourceNodeIds;
                sectionsUpdated = true;
              }
              // 如果没有有效的源节点ID，但有可用的源节点，添加第一个可用的源节点
              if (validSourceNodeIds.length === 0 && currentSourceNodeIds.length > 0) {
                updatedSection.sourceNodeIds = [currentSourceNodeIds[0]];
                sectionsUpdated = true;
              }
            }
            // 更新环节标题
            if (sectionsUpdated) {
              if (!updatedSection.sourceNodeIds || updatedSection.sourceNodeIds.length === 0) {
                updatedSection.title = `未选择源节点`;
              } else if (updatedSection.sourceNodeIds.length === 1) {
                const sourceNode = newSourceNodes.find(node => node.id === updatedSection.sourceNodeIds[0]);
                if (sourceNode) {
                  const sourceNodeLabel = getSourceNodeLabel(sourceNode);
                  updatedSection.title = sourceNodeLabel;
                }
              } else {
                updatedSection.title = `${updatedSection.sourceNodeIds.length}个源节点`;
              }
            }
            return updatedSection;
          }
          return section;
        });
        // 如果有环节被更新，则更新节点参数
        if (sectionsUpdated) {
          const updatedParams = { ...params.value, sections: updatedSections };
          nodeStore.updateNodeParams(props.nodeId, updatedParams);
          ElMessage.info('已更新环节引用的源节点');
        }
      }
    }
    // 处理节点
    processNode();
  }
}, { deep: true });
// 组件挂载时初始化
// 检查是否有源节点，如果没有则清空环节和序列数据
if (sourceNodes.value.length === 0 && params.value.sections && params.value.sections.length > 0) {
  // 清空环节和序列数据
  const updatedParams = {
    ...params.value,
    sections: [],
    sequence: []
  };
  // 更新节点参数
  nodeStore.updateNodeParams(props.nodeId, updatedParams);
  // 重新处理节点
  processNode();
} else {
  // 正常处理节点
  processNode();
}
</script>
<style scoped>
.text-sequence-node-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.section-title {
  font-size: 0.875rem;
  color: #606266;
  margin-bottom: 0.25rem;
}

.source-info {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.source-tag {
  margin-right: 0;
}

.preview-section {
  margin-top: 0.75rem;
  padding: 0.625rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-weight: bold;
  color: #606266;
}

.button-group {
  display: flex;
  gap: 0.5rem;
}

.preview-info {
  text-align: center;
  font-size: 0.875rem;
  padding: 0.625rem;
  border-radius: 0.25rem;
  margin-top: 0.75rem;
}

.success-bg {
  color: #67c23a;
  background-color: #f0f9eb;
}

.preview-empty {
  text-align: center;
  color: #909399;
  font-size: 0.875rem;
  padding: 0.625rem;
  background-color: #f5f7fa;
  border-radius: 0.25rem;
  margin-top: 0.75rem;
}

.sequence-summary {
  margin-top: 0.5rem;
}

.sequence-summary-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.sequence-summary-desc {
  font-size: 0.75rem;
  color: #909399;
  margin-top: 0.25rem;
}

.dialog-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.sequence-table-container {
  max-height: 31.25rem;
  overflow-y: auto;
}

.sequence-items {
  margin-top: 0.75rem;
  border: 1px solid #ebeef5;
  border-radius: 0.25rem;
  overflow: hidden;
}

.sequence-item {
  display: flex;
  padding: 0.5rem;
  border-bottom: 1px solid #ebeef5;
}

.sequence-item:last-child {
  border-bottom: none;
}

.item-index {
  width: 2rem;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #909399;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-text {
  margin-bottom: 0.25rem;
  word-break: break-word;
}

.item-info {
  display: flex;
  gap: 0.5rem;
}

.more-items {
  padding: 0.5rem;
  text-align: center;
  color: #909399;
  background-color: #f5f7fa;
}

/* 环节管理样式 */
.sections-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.3125rem;
  margin-top: 0.625rem;
}

.sections-panel {
  border: 0.0625rem solid #ebeef5;
  border-radius: 0.25rem;
  padding: 0.625rem;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  /* 确保flex子元素可以正确滚动 */
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.625rem;
}

.panel-header h3 {
  margin: 0;
  font-size: 1rem;
}

.panel-actions {
  display: flex;
  gap: 0.5rem;
}

.sections-list {
  overflow-y: auto;
  flex: 1;
  min-height: 0;
  /* 确保flex子元素可以正确滚动 */
}

.section-item {
  border: 0.0625rem solid #ebeef5;
  border-radius: 0.25rem;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.3s;
}

.section-item:hover {
  background-color: #f5f7fa;
}

.section-item.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.section-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.section-item-title {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  max-width: 70%;
}

.section-type-tag {
  flex-shrink: 0;
}

.section-item-info {
  margin-top: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.section-item-mode {
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.section-item-repeat {
  font-size: 0.75rem;
}

.section-item-desc {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #606266;
}

.warning-text {
  color: #E6A23C;
}

.section-item-actions {
  flex-shrink: 0;
}

.section-detail-panel {
  border: 0.0625rem solid #ebeef5;
  border-radius: 0.25rem;
  padding: 0.625rem;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  /* 确保flex子元素可以正确滚动 */
  overflow-y: auto;
}

.empty-detail {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  min-height: 12.5rem;
}

.section-info {
  padding: 0.625rem;
}

.section-type {
  margin-bottom: 0.625rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.source-info {
  font-size: 0.75rem;
  color: #409EFF;
}

.section-description {
  margin-bottom: 0.625rem;
  font-size: 0.875rem;
}

.section-config {
  display: flex;
  flex-wrap: wrap;
  gap: 0.625rem;
}

.config-item {
  display: flex;
  align-items: center;
}

.config-label {
  margin-right: 0.3125rem;
  font-weight: bold;
  font-size: 0.75rem;
}

.config-value {
  font-size: 0.75rem;
}

/* 优化环节卡片样式 */
.sections-card-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.section-card {
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 0.25rem;
  overflow: hidden;
  box-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.section-card:hover {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
}

.section-card-header {
  padding: 0.375rem 0.5rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 0.0625rem solid rgba(0, 0, 0, 0.05);
}

.section-card-title {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  max-width: 70%;
}

.section-card-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  justify-content: flex-end;
}

/* 重复语速设置样式 */
.repeat-speeds-container {
  margin-top: 0.625rem;
  padding: 0.625rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
  border: 0.0625rem solid #ebeef5;
}

.repeat-speed-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.repeat-speed-item:last-child {
  margin-bottom: 0;
}

.repeat-label {
  width: 9.375rem;
  margin-right: 0.625rem;
  font-size: 0.875rem;
}

.section-source {
  font-weight: 500;
  font-size: 0.8125rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #303133;
}

.section-card-content {
  padding: 0.375rem 0.5rem;
}

.section-detail {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #606266;
  margin-bottom: 0.25rem;
}

.section-detail:last-child {
  margin-bottom: 0;
}

.section-detail.translation {
  color: #409eff;
}

.sections-more {
  text-align: center;
  margin-top: 0.375rem;
}

.section-title-text {
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.section-repeat-info {
  color: #909399;
  font-size: 0.75rem;
}

.sections-more-info {
  text-align: center;
  color: #909399;
  font-size: 0.75rem;
  padding: 0.5rem;
  background-color: #f5f7fa;
  margin-top: 0.5rem;
  border-radius: 0.25rem;
}

.form-help {
  font-size: 0.75rem;
  color: #666;
  margin-top: 0.3125rem;
}

/* 自定义标签样式 */
.custom-label {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
}

.custom-label-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.question-icon {
  color: #909399;
  cursor: pointer;
}

/* 自定义重复设置样式 */
.custom-repeat-settings {
  margin: 0.5rem 0 1rem;
  border: 0.0625rem solid #ebeef5;
  border-radius: 0.25rem;
  overflow: hidden;
  box-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.05);
}

.custom-repeat-header {
  display: flex;
  background-color: #f5f7fa;
  padding: 0.5rem;
  font-weight: bold;
  border-bottom: 0.0625rem solid #ebeef5;
}

.custom-repeat-body {
  max-height: 15rem;
  overflow-y: auto;
}

.custom-repeat-row {
  display: flex;
  padding: 0.5rem;
  border-bottom: 0.0625rem solid #ebeef5;
  align-items: center;
}

.custom-repeat-row:last-child {
  border-bottom: none;
}

.repeat-index-header,
.repeat-index {
  flex: 1;
  padding: 0 0.25rem;
}

.repeat-speed-header,
.repeat-speed {
  flex: 1.5;
  padding: 0 0.25rem;
}

.repeat-pause-header,
.repeat-pause {
  flex: 1.5;
  padding: 0 0.25rem;
}

.full-width {
  width: 100%;
}

.error-text {
  color: #F56C6C;
}

.empty-sections {
  padding: 1.25rem 0;
}

/* 环节管理对话框样式 */
.sections-dialog-content {
  display: flex;
  flex-direction: column;
  height: 70vh;
  overflow: hidden;
}

.sections-content {
  display: flex;
  flex: 1;
  min-height: 0;
  /* 确保flex子元素可以正确滚动 */
}

.left-panel {
  width: 33.33%;
  padding-right: 0.625rem;
  display: flex;
  flex-direction: column;
  min-height: 0;
  /* 确保flex子元素可以正确滚动 */
}

.right-panel {
  width: 66.67%;
  padding-left: 0.625rem;
  display: flex;
  flex-direction: column;
  min-height: 0;
  /* 确保flex子元素可以正确滚动 */
}

/* 确保表单项标签和内容在一行显示 */
:deep(.el-form-item__label) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.el-form-item__content) {
  display: flex;
  align-items: center;
}

/* 设置标题和开关的样式 */
.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0 0.5rem;
}

.setting-title {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #606266;
}

.setting-title span {
  margin-right: 0.5rem;
}

.question-icon {
  color: #909399;
  font-size: 1rem;
  cursor: help;
}
</style>
