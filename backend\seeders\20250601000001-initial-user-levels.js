"use strict";

/**
 * 初始化用户等级数据
 */
module.exports = {
  async up(queryInterface, Sequelize) {
    const now = new Date();

    try {
      // 先清空表
      await queryInterface.bulkDelete("user_levels", null, {});

      // 添加初始用户等级
      await queryInterface.bulkInsert("user_levels", [
        {
          level: 0,
          name: "免费用户",
          description: "基本功能，有使用限制",
          is_default: true,
          created_at: now,
          updated_at: now,
        },
        {
          level: 1,
          name: "基础会员",
          description: "解锁更多功能，提高使用限制",
          is_default: false,
          created_at: now,
          updated_at: now,
        },
        {
          level: 2,
          name: "高级会员",
          description: "解锁高级功能，更高使用限制",
          is_default: false,
          created_at: now,
          updated_at: now,
        },
        {
          level: 3,
          name: "专业会员",
          description: "解锁所有功能，无使用限制",
          is_default: false,
          created_at: now,
          updated_at: now,
        },
      ]);

      // 清空权限表
      await queryInterface.bulkDelete("level_permissions", null, {});

      // 添加初始等级权限
      // 免费用户(0)权限
      await queryInterface.bulkInsert("level_permissions", [
        {
          level: 0,
          feature_key: "player_access",
          created_at: now,
          updated_at: now,
        },
      ]);

      // 基础会员(1)权限
      await queryInterface.bulkInsert("level_permissions", [
        {
          level: 1,
          feature_key: "player_access",
          created_at: now,
          updated_at: now,
        },
        {
          level: 1,
          feature_key: "content_creation",
          created_at: now,
          updated_at: now,
        },
      ]);

      // 高级会员(2)权限
      await queryInterface.bulkInsert("level_permissions", [
        {
          level: 2,
          feature_key: "player_access",
          created_at: now,
          updated_at: now,
        },
        {
          level: 2,
          feature_key: "content_creation",
          created_at: now,
          updated_at: now,
        },
        {
          level: 2,
          feature_key: "video_export",
          created_at: now,
          updated_at: now,
        },
      ]);

      // 专业会员(3)权限 - 所有功能
      await queryInterface.bulkInsert("level_permissions", [
        {
          level: 3,
          feature_key: "player_access",
          created_at: now,
          updated_at: now,
        },
        {
          level: 3,
          feature_key: "content_creation",
          created_at: now,
          updated_at: now,
        },
        {
          level: 3,
          feature_key: "video_export",
          created_at: now,
          updated_at: now,
        },
      ]);

      // 清空功能使用限制表
      await queryInterface.bulkDelete("feature_usage_limits", null, {});

      // 添加功能使用限制
      await queryInterface.bulkInsert("feature_usage_limits", [
        // 免费用户限制
        {
          level: 0,
          feature_key: "content_creation",
          daily_limit: 3,
          monthly_limit: 30,
          created_at: now,
          updated_at: now,
        },
        {
          level: 0,
          feature_key: "video_export",
          daily_limit: 1,
          monthly_limit: 5,
          created_at: now,
          updated_at: now,
        },

        // 基础会员限制
        {
          level: 1,
          feature_key: "content_creation",
          daily_limit: 10,
          monthly_limit: 100,
          created_at: now,
          updated_at: now,
        },
        {
          level: 1,
          feature_key: "video_export",
          daily_limit: 3,
          monthly_limit: 30,
          created_at: now,
          updated_at: now,
        },

        // 高级会员限制
        {
          level: 2,
          feature_key: "content_creation",
          daily_limit: 30,
          monthly_limit: 300,
          created_at: now,
          updated_at: now,
        },
        {
          level: 2,
          feature_key: "video_export",
          daily_limit: 10,
          monthly_limit: 100,
          created_at: now,
          updated_at: now,
        },

        // 专业会员无限制
      ]);
    } catch (error) {
      console.error("初始化用户等级数据失败:", error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    // 删除所有添加的数据
    await queryInterface.bulkDelete("feature_usage_limits", null, {});
    await queryInterface.bulkDelete("level_permissions", null, {});
    await queryInterface.bulkDelete("user_levels", null, {});
  },
};
