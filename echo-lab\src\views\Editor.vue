<!--
  编辑器视图
  用于展示节点编辑器
-->
<template>
  <div class="editor-view">
    <div v-if="loading" class="loading-state">
      <el-skeleton :rows="10" animated />
    </div>
    <div v-else-if="error" class="error-state">
      <el-result icon="error" :title="error" :sub-title="isEditing ? '加载内容失败' : '初始化失败'">
        <template #extra>
          <el-button type="primary" @click="goHome">返回首页</el-button>
        </template>
      </el-result>
    </div>
    <NodeEditor v-else :projectId="projectId" :initialData="contentData" :isEditing="isEditing" />
  </div>
</template>

<script setup>
import NodeEditor from '@/components/editor';
import { ref, computed, onMounted, getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import contentService from '@/services/contentService';
import { getSpecialWords } from '@/services/specialWordService';
import { getTtsInfo } from '@/services/ttsInfoService';
import { preloadDefaultMappings } from '@/services/speakerMappingService';
import { trackEvent, EVENTS } from '@/utils/analytics';
import { useSEO, PAGE_SEO_CONFIG } from '@/composables/useSEO';
import { registerNodeComponents } from '@/components/nodes';

const route = useRoute();
const router = useRouter();

// 初始化SEO
useSEO(PAGE_SEO_CONFIG.editor);

const projectId = route.params.id;
const isEditing = computed(() => !!projectId);
const loading = ref(true);
const error = ref(null);
const contentData = ref(null);

// 加载内容数据
async function loadContent() {
  loading.value = true;
  error.value = null;

  try {
    if (isEditing.value) {
      const response = await contentService.getContent(projectId);
      if (response && response.success) {
        contentData.value = response.content;

        // 检查configJson是否为字符串，如果是则尝试解密
        if (contentData.value.configJson && typeof contentData.value.configJson === 'string') {
          try {
            // 导入解密工具
            const { decryptJSON } = await import('../utils/cryptoService');

            // 尝试解密configJson
            contentData.value.configJson = decryptJSON(contentData.value.configJson);
            console.log('解密configJson成功');
          } catch (decryptError) {
            console.error('解密configJson失败:', decryptError);
            // 解密失败时，保持原始数据，让NodeEditor组件处理
          }
        }
      } else {
        throw new Error(response?.error || '加载内容失败');
      }
    } else {
      // 新建内容，设置默认值
      contentData.value = {
        name: '',
        description: '',
        tags: [],
        configJson: null
      };
    }
  } catch (err) {
    console.error('加载内容失败:', err);
    error.value = err.message;
  } finally {
    loading.value = false;
  }
}


function goHome() {
  router.replace({ name: 'Home' });
}

// 预加载特殊词汇 - 一次性获取所有特殊词汇并缓存
async function preloadSpecialWords() {
  try {
    console.log('[Editor] 预加载特殊词汇');
    // 预加载所有特殊词汇（不指定服务和语言，一次性获取所有）
    await getSpecialWords();
    console.log('[Editor] 特殊词汇预加载完成');
  } catch (error) {
    console.error('[Editor] 预加载特殊词汇失败:', error);
  }
}

// 预加载TTS服务信息 - 只在编辑器页面加载
async function preloadTtsInfo() {
  try {
    console.log('[Editor] 预加载TTS服务信息');
    await getTtsInfo();
    console.log('[Editor] TTS服务信息预加载完成');
  } catch (error) {
    console.error('[Editor] 预加载TTS服务信息失败:', error);
    // TTS信息加载失败不影响编辑器基本功能，所以不设置全局错误
  }
}

// 预加载默认说话人映射 - 只在编辑器页面加载
async function preloadSpeakerMappings() {
  try {
    console.log('[Editor] 预加载默认说话人映射');
    await preloadDefaultMappings();
    console.log('[Editor] 默认说话人映射预加载完成');
  } catch (error) {
    console.error('[Editor] 预加载默认说话人映射失败:', error);
    // 说话人映射加载失败不影响编辑器基本功能，所以不设置全局错误
  }
}

onMounted(() => {
  // 在编辑器页面注册节点组件
  const instance = getCurrentInstance();
  if (instance) {
    registerNodeComponents(instance.appContext.app);
  }

  loadContent();
  // 预加载特殊词汇
  preloadSpecialWords();
  // 预加载TTS服务信息
  preloadTtsInfo();
  // 预加载默认说话人映射
  preloadSpeakerMappings();

  // 跟踪编辑器打开事件
  trackEvent(EVENTS.PROJECT_LOADED, {
    isEditing: isEditing.value,
    projectId: projectId || 'new'
  });
});
</script>

<style scoped>
.editor-view {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.loading-state,
.error-state {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.loading-state {
  width: 100%;
}
</style>
