import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

// 预定义的尺寸档位
const SIZE_BREAKPOINTS = [320, 480, 640, 800, 1024, 1280]

// 根据宽度选择档位
function getOptimalSize(width) {
  for (let size of SIZE_BREAKPOINTS) {
    if (width <= size) return size
  }
  return SIZE_BREAKPOINTS[SIZE_BREAKPOINTS.length - 1]
}

export function useResponsiveImage(baseUrl, containerRef) {
  const containerWidth = ref(480) // 默认480px
  
  const imageUrl = computed(() => {
    if (!baseUrl) return baseUrl
    const optimalWidth = getOptimalSize(containerWidth.value)
    return `${baseUrl}?x-oss-process=image/resize,w_${optimalWidth}`
  })
  
  const resizeObserver = ref(null)
  
  const updateSize = () => {
    if (!containerRef.value) return
    const newWidth = containerRef.value.getBoundingClientRect().width
    const newOptimalSize = getOptimalSize(newWidth)
    const currentOptimalSize = getOptimalSize(containerWidth.value)
    
    // 只有档位变化时才更新
    if (newOptimalSize !== currentOptimalSize) {
      containerWidth.value = newWidth
    }
  }
  
  onMounted(async () => {
    await nextTick()
    updateSize()
    
    if (window.ResizeObserver && containerRef.value) {
      resizeObserver.value = new ResizeObserver(updateSize)
      resizeObserver.value.observe(containerRef.value)
    }
  })
  
  onUnmounted(() => {
    resizeObserver.value?.disconnect()
  })
  
  return { imageUrl }
}