/**
 * 图片控制器
 * 处理图片上传和获取
 */
const crypto = require("crypto");
const ossClient = require("../services/ossService");
const path = require("path");
const db = require("../models");

// 自定义 nanoid 函数，生成 21 位随机 ID
function nanoid() {
  return crypto
    .randomBytes(16)
    .toString("base64")
    .replace(/[+/=]/g, "")
    .substring(0, 21);
}

/**
 * 上传图片
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.uploadImage = async (req, res) => {
  try {
    if (!req.file) {
      return res
        .status(400)
        .json({ success: false, message: "未提供图片文件" });
    }

    const { type = "general" } = req.body;
    let metadata = {};

    // 解析元数据（如果有）
    if (req.body.metadata) {
      try {
        metadata = JSON.parse(req.body.metadata);
      } catch (e) {
        console.warn("元数据解析失败:", e);
      }
    }

    // 生成唯一ID
    const imageId = `img_${nanoid()}`;

    // 从MIME类型获取文件扩展名
    const mimeToExt = {
      "image/jpeg": "jpg",
      "image/png": "png",
      "image/gif": "gif",
      "image/webp": "webp",
      "image/svg+xml": "svg",
    };

    const format = mimeToExt[req.file.mimetype] || "jpg";

    // 根据类型确定OSS路径
    const ossPath = `images/${type}/${imageId}.${format}`;

    // 上传到OSS
    const ossResult = await ossClient.put(ossPath, req.file.buffer, {
      mime: req.file.mimetype,
    });

    // 保存图片信息到数据库
    const image = await db.Image.create({
      id: imageId,
      type,
      originalName: req.file.originalname,
      ossUrl: ossResult.url,
      ossKey: ossPath,
      width: null, // 无法获取宽度
      height: null, // 无法获取高度
      format,
      size: req.file.size,
      metadata,
    });

    console.log(`图片上传成功，已保存到数据库: ${imageId}`);

    return res.json({
      success: true,
      data: {
        imageId: imageId,
        ossUrl: ossResult.url,
        ossKey: ossPath,
        width: null, // 无法获取宽度
        height: null, // 无法获取高度
        format: format,
        size: req.file.size,
      },
    });
  } catch (error) {
    console.error("图片上传失败:", error);
    return res
      .status(500)
      .json({ success: false, message: "图片上传失败", error: error.message });
  }
};
