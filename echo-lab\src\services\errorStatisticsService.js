/**
 * 错误统计服务
 * 提供错误上报和管理功能
 */
import { get, post, put } from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

/**
 * 错误上报服务
 */
export class ErrorReportService {
  constructor() {
    this.pendingErrors = [];
    this.batchSize = 10;
    this.batchTimeout = 5000; // 5秒
    this.batchTimer = null;
  }

  /**
   * 上报单个错误
   * @param {Object} errorData - 错误数据
   * @returns {Promise<Object>} 上报结果
   */
  async reportError(errorData) {
    try {
      const response = await post(API_ENDPOINTS.ERRORS.REPORT, errorData);
      return response;
    } catch (error) {
      console.error("错误上报失败:", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 批量上报错误
   * @param {Array} errors - 错误列表
   * @returns {Promise<Object>} 上报结果
   */
  async reportErrors(errors) {
    try {
      const response = await post(API_ENDPOINTS.ERRORS.REPORT_BATCH, {
        errors,
      });
      return response;
    } catch (error) {
      console.error("批量错误上报失败:", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 添加错误到批量队列
   * @param {Object} errorData - 错误数据
   */
  addToBatch(errorData) {
    this.pendingErrors.push(errorData);

    // 如果达到批量大小，立即发送
    if (this.pendingErrors.length >= this.batchSize) {
      this.flushBatch();
    } else {
      // 否则设置定时器
      this.scheduleBatchFlush();
    }
  }

  /**
   * 调度批量发送
   */
  scheduleBatchFlush() {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
    }

    this.batchTimer = setTimeout(() => {
      this.flushBatch();
    }, this.batchTimeout);
  }

  /**
   * 发送批量错误
   */
  async flushBatch() {
    if (this.pendingErrors.length === 0) return;

    const errors = [...this.pendingErrors];
    this.pendingErrors = [];

    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    await this.reportErrors(errors);
  }

  /**
   * 格式化错误数据
   * @param {Error|string} error - 错误对象或消息
   * @param {Object} context - 错误上下文
   * @returns {Object} 格式化后的错误数据
   */
  formatErrorData(error, context = {}) {
    const errorData = {
      error_type: context.type || "javascript",
      message: typeof error === "string" ? error : error.message,
      stack: error.stack || null,
      url: window.location.href,
      user_agent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      ...context,
    };

    // 添加错误位置信息
    if (error.filename) errorData.filename = error.filename;
    if (error.lineno) errorData.line_number = error.lineno;
    if (error.colno) errorData.column_number = error.colno;

    // 设置当前错误类型用于严重程度计算
    this.currentErrorType = errorData.error_type;

    // 添加增强的环境信息
    errorData.enhanced_context = this.collectEnhancedContext(error, context);

    return errorData;
  }

  /**
   * 收集增强的上下文信息
   * @param {Error|string} error - 错误对象
   * @param {Object} context - 错误上下文
   * @returns {Object} 增强的上下文数据
   */
  collectEnhancedContext(error, context = {}) {
    const enhancedContext = {};

    try {
      // 性能信息
      enhancedContext.performance = this.collectPerformanceInfo();

      // 环境状态
      enhancedContext.environment = this.collectEnvironmentInfo();

      // 用户行为
      enhancedContext.user_behavior = this.collectUserBehaviorInfo();

      // 应用状态
      enhancedContext.app_state = this.collectAppStateInfo();

      // 错误严重程度
      enhancedContext.severity = this.calculateErrorSeverity(error, context);
    } catch (err) {
      console.warn("收集增强上下文信息失败:", err);
      enhancedContext.collection_error = err.message;
    }

    return enhancedContext;
  }

  /**
   * 收集性能信息
   * @returns {Object} 性能相关信息
   */
  collectPerformanceInfo() {
    const performance = {};

    try {
      // 内存信息
      if (window.performance && window.performance.memory) {
        performance.memory = {
          used: window.performance.memory.usedJSHeapSize,
          total: window.performance.memory.totalJSHeapSize,
          limit: window.performance.memory.jsHeapSizeLimit,
        };
      }

      // 页面性能指标
      if (window.performance && window.performance.timing) {
        const timing = window.performance.timing;
        performance.page_load = {
          dom_ready: timing.domContentLoadedEventEnd - timing.navigationStart,
          load_complete: timing.loadEventEnd - timing.navigationStart,
          dns_lookup: timing.domainLookupEnd - timing.domainLookupStart,
          tcp_connect: timing.connectEnd - timing.connectStart,
        };
      }

      // 网络信息
      if (navigator.connection) {
        performance.network = {
          effective_type: navigator.connection.effectiveType,
          downlink: navigator.connection.downlink,
          rtt: navigator.connection.rtt,
          save_data: navigator.connection.saveData,
        };
      }
    } catch (err) {
      performance.collection_error = err.message;
    }

    return performance;
  }

  /**
   * 收集环境信息
   * @returns {Object} 环境状态信息
   */
  collectEnvironmentInfo() {
    const environment = {};

    try {
      // 屏幕信息
      environment.screen = {
        width: window.screen.width,
        height: window.screen.height,
        available_width: window.screen.availWidth,
        available_height: window.screen.availHeight,
        color_depth: window.screen.colorDepth,
        pixel_ratio: window.devicePixelRatio || 1,
      };

      // 视口信息
      environment.viewport = {
        width: window.innerWidth,
        height: window.innerHeight,
        scroll_x: window.scrollX || window.pageXOffset,
        scroll_y: window.scrollY || window.pageYOffset,
      };

      // 页面可见性
      environment.visibility = {
        hidden: document.hidden,
        visibility_state: document.visibilityState,
      };

      // 在线状态
      environment.online_status = {
        online: navigator.onLine,
        connection_type: navigator.connection?.type || "unknown",
      };

      // 时区信息
      environment.timezone = {
        offset: new Date().getTimezoneOffset(),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      };
    } catch (err) {
      environment.collection_error = err.message;
    }

    return environment;
  }

  /**
   * 收集用户行为信息
   * @returns {Object} 用户行为信息
   */
  collectUserBehaviorInfo() {
    const behavior = {};

    try {
      // 页面停留时间
      if (window.performance && window.performance.timing) {
        behavior.page_stay_time =
          Date.now() - window.performance.timing.navigationStart;
      }

      // 页面访问路径（从sessionStorage获取）
      const navigationHistory = sessionStorage.getItem("navigation_history");
      if (navigationHistory) {
        try {
          behavior.navigation_history = JSON.parse(navigationHistory).slice(-5); // 最近5个页面
        } catch (e) {
          behavior.navigation_history = [];
        }
      }

      // 用户交互统计
      behavior.interactions = {
        clicks: parseInt(sessionStorage.getItem("user_clicks") || "0"),
        scrolls: parseInt(sessionStorage.getItem("user_scrolls") || "0"),
        key_presses: parseInt(sessionStorage.getItem("user_keypresses") || "0"),
      };

      // 当前页面焦点状态
      behavior.page_focus = {
        has_focus: document.hasFocus(),
        last_focus_time: sessionStorage.getItem("last_focus_time"),
      };
    } catch (err) {
      behavior.collection_error = err.message;
    }

    return behavior;
  }

  /**
   * 收集应用状态信息
   * @returns {Object} 应用状态信息
   */
  collectAppStateInfo() {
    const appState = {};

    try {
      // Vue路由信息
      if (window.__VUE_ROUTER_INSTANCE__) {
        const router = window.__VUE_ROUTER_INSTANCE__;
        appState.route = {
          path: router.currentRoute.value.path,
          name: router.currentRoute.value.name,
          params: router.currentRoute.value.params,
          query: router.currentRoute.value.query,
        };
      }

      // 本地存储使用情况
      appState.storage = {
        localStorage_size: this.calculateStorageSize(localStorage),
        sessionStorage_size: this.calculateStorageSize(sessionStorage),
        localStorage_keys: localStorage.length,
        sessionStorage_keys: sessionStorage.length,
      };

      // Service Worker状态
      if ("serviceWorker" in navigator) {
        appState.service_worker = {
          controller: !!navigator.serviceWorker.controller,
          ready: "ready" in navigator.serviceWorker,
        };
      }

      // 页面资源加载状态
      if (window.performance && window.performance.getEntriesByType) {
        const resources = window.performance.getEntriesByType("resource");
        appState.resources = {
          total_count: resources.length,
          failed_count: resources.filter((r) => r.transferSize === 0).length,
          avg_load_time:
            resources.reduce((sum, r) => sum + r.duration, 0) /
            resources.length,
        };
      }
    } catch (err) {
      appState.collection_error = err.message;
    }

    return appState;
  }

  /**
   * 计算错误严重程度
   * @param {Error|string} error - 错误对象
   * @param {Object} context - 错误上下文
   * @returns {Object} 错误严重程度信息
   */
  calculateErrorSeverity(error, context = {}) {
    const severity = {
      level: "medium",
      score: 50,
      factors: [],
    };

    try {
      let score = 50; // 基础分数

      // 根据错误类型调整分数
      const errorType = context.type || this.currentErrorType || "javascript";
      switch (errorType) {
        case "network":
          score += 20; // 网络错误影响用户体验
          severity.factors.push("网络错误影响用户体验");
          break;
        case "vue":
          score += 30; // Vue错误可能导致页面崩溃
          severity.factors.push("Vue错误可能导致页面崩溃");
          break;
        case "resource":
          score += 10; // 资源加载错误影响相对较小
          severity.factors.push("资源加载错误");
          break;
        case "promise":
          score += 25; // Promise错误可能导致功能异常
          severity.factors.push("Promise错误可能导致功能异常");
          break;
      }

      // 根据错误消息内容调整
      const message = typeof error === "string" ? error : error?.message || "";
      if (
        message.includes("Cannot read property") ||
        message.includes("Cannot read properties")
      ) {
        score += 15;
        severity.factors.push("属性访问错误");
      }
      if (message.includes("is not a function")) {
        score += 15;
        severity.factors.push("函数调用错误");
      }

      // 根据内存使用情况调整
      if (window.performance && window.performance.memory) {
        const memoryUsage =
          window.performance.memory.usedJSHeapSize /
          window.performance.memory.jsHeapSizeLimit;
        if (memoryUsage > 0.8) {
          score += 20;
          severity.factors.push("内存使用率过高");
        } else if (memoryUsage > 0.6) {
          score += 10;
          severity.factors.push("内存使用率较高");
        }
      }

      // 根据页面可见性调整
      if (document.hidden) {
        score -= 10; // 后台错误影响较小
        severity.factors.push("页面在后台");
      }

      // 根据网络状态调整
      if (navigator.connection) {
        const connection = navigator.connection;
        if (
          connection.effectiveType === "slow-2g" ||
          connection.effectiveType === "2g"
        ) {
          score += 5;
          severity.factors.push("网络连接较慢");
        }
        if (connection.saveData) {
          score += 5;
          severity.factors.push("用户启用了数据节省模式");
        }
      }

      // 确定严重程度等级
      if (score >= 80) {
        severity.level = "critical";
      } else if (score >= 60) {
        severity.level = "high";
      } else if (score >= 40) {
        severity.level = "medium";
      } else {
        severity.level = "low";
      }

      severity.score = Math.min(100, Math.max(0, score));
    } catch (err) {
      severity.calculation_error = err.message;
    }

    return severity;
  }

  /**
   * 计算存储大小
   * @param {Storage} storage - 存储对象
   * @returns {number} 存储大小（字节）
   */
  calculateStorageSize(storage) {
    let size = 0;
    try {
      for (let key in storage) {
        if (storage.hasOwnProperty(key)) {
          size += storage[key].length + key.length;
        }
      }
    } catch (err) {
      // 某些浏览器可能不允许访问存储
      size = -1;
    }
    return size;
  }
}

/**
 * 错误管理服务
 */
export class ErrorManagementService {
  /**
   * 获取错误汇总列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 错误汇总列表
   */
  async getErrorSummaries(params = {}) {
    try {
      const response = await get(API_ENDPOINTS.ERRORS.SUMMARIES, params);
      return response;
    } catch (error) {
      console.error("获取错误汇总失败:", error);
      throw error;
    }
  }

  /**
   * 获取错误详情
   * @param {string} errorHash - 错误哈希
   * @returns {Promise<Object>} 错误详情
   */
  async getErrorDetails(errorHash) {
    try {
      const response = await get(API_ENDPOINTS.ERRORS.DETAILS(errorHash));
      return response;
    } catch (error) {
      console.error("获取错误详情失败:", error);
      throw error;
    }
  }

  /**
   * 更新错误状态
   * @param {string} errorHash - 错误哈希
   * @param {string} status - 新状态
   * @returns {Promise<Object>} 更新结果
   */
  async updateErrorStatus(errorHash, status) {
    try {
      const response = await put(API_ENDPOINTS.ERRORS.STATUS(errorHash), {
        status,
      });
      return response;
    } catch (error) {
      console.error("更新错误状态失败:", error);
      throw error;
    }
  }

  /**
   * 更新错误优先级
   * @param {string} errorHash - 错误哈希
   * @param {string} priority - 新优先级
   * @returns {Promise<Object>} 更新结果
   */
  async updateErrorPriority(errorHash, priority) {
    try {
      const response = await put(API_ENDPOINTS.ERRORS.PRIORITY(errorHash), {
        priority,
      });
      return response;
    } catch (error) {
      console.error("更新错误优先级失败:", error);
      throw error;
    }
  }

  /**
   * 添加处理备注
   * @param {string} errorHash - 错误哈希
   * @param {string} notes - 备注内容
   * @returns {Promise<Object>} 添加结果
   */
  async addResolutionNotes(errorHash, notes) {
    try {
      const response = await post(API_ENDPOINTS.ERRORS.NOTES(errorHash), {
        notes,
      });
      return response;
    } catch (error) {
      console.error("添加处理备注失败:", error);
      throw error;
    }
  }

  /**
   * 获取错误概览数据
   * @returns {Promise<Object>} 概览数据
   */
  async getOverview() {
    try {
      const response = await get(API_ENDPOINTS.ERRORS.OVERVIEW);
      return response;
    } catch (error) {
      console.error("获取错误概览失败:", error);
      throw error;
    }
  }

  /**
   * 获取错误统计数据
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 统计数据
   */
  async getStatistics(params = {}) {
    try {
      const response = await get(API_ENDPOINTS.ERRORS.STATISTICS, params);
      return response;
    } catch (error) {
      console.error("获取错误统计失败:", error);
      throw error;
    }
  }
}

// 创建单例实例
export const errorReportService = new ErrorReportService();
export const errorManagementService = new ErrorManagementService();

// 默认导出
export default {
  errorReportService,
  errorManagementService,
  ErrorReportService,
  ErrorManagementService,
};
