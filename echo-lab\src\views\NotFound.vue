<!--
  404页面
  当路由不存在时显示
-->
<template>
  <div class="not-found">
    <div class="not-found-content">
      <h1>404</h1>
      <p>页面不存在</p>
      <el-button type="primary" @click="goHome">返回首页</el-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

function goHome() {
  router.push({ name: 'Home' });
}
</script>

<style scoped>
.not-found {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

.not-found-content {
  text-align: center;
  padding: 2rem;
}

.not-found-content h1 {
  font-size: 6rem;
  color: #409EFF;
  margin: 0;
}

.not-found-content p {
  font-size: 1.5rem;
  color: #606266;
  margin: 1rem 0 2rem;
}
</style>
