<!--
  等级选择页面
  用户选择学习等级
-->
<template>
  <div class="level-selection-page">
    <!-- 统一标题栏 -->
    <SmartPageHeader :title="`选择${currentLanguageLabel}水平`">
      <template #actions>
        <div class="language-indicator" @click="changeLanguage" title="点击更改语言">
          <span class="language-flag">{{ getLanguageFlag(currentLanguage) }}</span>
          <span class="language-name">{{ currentLanguageLabel }}</span>
          <el-icon class="change-icon">
            <i-ep-edit />
          </el-icon>
        </div>
      </template>
    </SmartPageHeader>

    <div class="page-container">
      <div class="content-wrapper">
        <!-- 欢迎信息 -->
        <div class="welcome-section">
          <img src="@/assets/logo.jpg" alt="Echo Lab" class="logo" />
          <p class="welcome-subtitle">为了给你推荐合适的学习内容，请选择你的当前水平</p>
        </div>

      <!-- 主要内容 -->
      <div class="page-content">
        <div class="level-options">
          <div v-for="level in currentLanguageLevels" :key="level.key" class="level-card"
            :class="{ 'selected': selectedLevel === level.key }" @click="handleLevelSelect(level.key)">
            <div class="level-badge">{{ level.name }}</div>
            <div class="level-info">
              <h3 class="level-title">{{ level.name }}</h3>
              <p class="level-desc">{{ level.description }}</p>
            </div>
            <div class="selection-indicator">
              <el-icon v-if="selectedLevel === level.key" class="check-icon">
                <i-ep-check />
              </el-icon>
            </div>
          </div>
        </div>

        <div class="page-actions">
          <el-button @click="handleBack" size="large" class="back-button">
            <el-icon class="el-icon--left">
              <i-ep-arrow-left />
            </el-icon>
            上一步
          </el-button>

          <el-button @click="handleSkip" size="large" class="skip-button">
            跳过
          </el-button>

          <el-button type="primary" size="large" @click="handleComplete" :disabled="!selectedLevel"
            class="complete-button">
            开始学习
            <el-icon class="el-icon--right">
              <i-ep-right />
            </el-icon>
          </el-button>
        </div>
      </div>

      <!-- 页面底部 -->
        <div class="page-footer">
          <p class="footer-text">
            不确定自己的水平？没关系，你可以随时在设置中调整
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useLanguageStore } from '@/stores/languageStore';
import { getLanguageLabel } from '@/config/languages';
import { getLanguageLevels, getLanguageFlag } from '@/config/languageLevels';
import { setOnboardingCompleted } from '@/utils/userSettings';
import { ElMessage } from 'element-plus';
import SmartPageHeader from '@/components/common/SmartPageHeader.vue';

const router = useRouter();
const route = useRoute();
const languageStore = useLanguageStore();

// 选择的等级
const selectedLevel = ref(null);

// 当前语言（从路由参数或语言store获取）
const currentLanguage = computed(() => {
  return route.query.language || languageStore.currentLearningLanguage;
});

// 当前语言标签
const currentLanguageLabel = computed(() => {
  return getLanguageLabel(currentLanguage.value);
});

// 当前语言的等级选项
const currentLanguageLevels = computed(() => {
  return getLanguageLevels(currentLanguage.value);
});

/**
 * 处理等级选择
 */
function handleLevelSelect(levelKey) {
  selectedLevel.value = levelKey;
}

/**
 * 处理返回上一步
 */
function handleBack() {
  router.push('/onboarding/language');
}

/**
 * 更改语言
 */
function changeLanguage() {
  router.push('/onboarding/language');
}

/**
 * 处理跳过等级选择
 */
function handleSkip() {
  // 跳过时显式清除等级设置，避免旧数据影响
  languageStore.clearUserLevel();
  completeOnboarding(null);
}

/**
 * 处理完成等级选择
 */
function handleComplete() {
  if (!selectedLevel.value) {
    ElMessage.warning('请先选择你的水平等级');
    return;
  }

  completeOnboarding(selectedLevel.value);
}

/**
 * 完成引导流程
 */
async function completeOnboarding(level) {
  try {
    // 保存用户等级
    if (level) {
      const success = languageStore.setUserLevel(level);
      if (!success) {
        ElMessage.error('保存等级失败，请重试');
        return;
      }
      console.log('用户等级已保存:', level);
    }

    // 标记引导完成
    setOnboardingCompleted();

    // 显示欢迎消息
    const languageLabel = currentLanguageLabel.value;
    const levelText = level ? `，当前水平：${level}` : '';

    // 使用 replace 避免在历史记录中留下引导页面
    // 添加一个短暂的延迟让用户看到成功消息
    ElMessage.success(`欢迎来到 Echo Lab！开始你的${languageLabel}学习之旅吧${levelText}！`);

    console.log('引导流程完成');

    // 使用 nextTick 确保消息显示后再跳转
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 使用 replace 进行更优雅的跳转
    router.replace('/');

  } catch (error) {
    console.error('完成引导流程失败:', error);
    ElMessage.error('设置失败，请重试');
  }
}

// 组件挂载时检查语言设置
onMounted(() => {
  if (!currentLanguage.value) {
    console.warn('未找到语言设置，返回语言选择页面');
    router.push('/onboarding/language');
    return;
  }

  // 确保语言store中的设置是正确的
  if (route.query.language && route.query.language !== languageStore.currentLearningLanguage) {
    languageStore.setLearningLanguage(route.query.language);
  }

  console.log('等级选择页面加载，当前语言:', currentLanguage.value);
});
</script>

<style scoped>
.level-selection-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.page-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.content-wrapper {
  max-width: 56rem;
  width: 100%;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 欢迎区域 */
.welcome-section {
  text-align: center;
  padding: 2rem 2rem 1.5rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.logo {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  margin-bottom: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.welcome-subtitle {
  font-size: 1rem;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.5;
}

/* 语言指示器样式 */
.language-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  background: rgba(64, 158, 255, 0.1);
  transition: all 0.3s ease;
}

.language-indicator:hover {
  background: rgba(64, 158, 255, 0.2);
  transform: scale(1.05);
}

.change-icon {
  font-size: 0.875rem;
  color: #409eff;
  margin-left: 0.25rem;
}

.language-flag {
  font-size: 1.5rem;
}

.language-name {
  font-size: 1rem;
  font-weight: 600;
  color: #409eff;
}



.page-content {
  padding: 2rem;
}

.level-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 3rem;
}

.level-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  border: 2px solid #e9ecef;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
}

.level-card:hover {
  border-color: #409eff;
  background: #f8faff;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.1);
}

.level-card.selected {
  border-color: #409eff;
  background: #ecf5ff;
  box-shadow: 0 0 0 1px #409eff;
}

.level-badge {
  background: #409eff;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-weight: 600;
  font-size: 0.875rem;
  margin-right: 1rem;
  flex-shrink: 0;
  min-width: 4rem;
  text-align: center;
}

.level-card.selected .level-badge {
  background: #67c23a;
}

.level-info {
  flex: 1;
}

.level-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
}

.level-desc {
  font-size: 0.875rem;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.4;
}

.selection-indicator {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.check-icon {
  color: #67c23a;
  font-size: 1.5rem;
}

.page-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.back-button,
.skip-button,
.complete-button {
  min-width: 8rem;
  height: 2.75rem;
  font-weight: 600;
}

.page-footer {
  text-align: center;
  padding: 1.5rem 2rem;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.footer-text {
  font-size: 0.875rem;
  color: #6c757d;
  margin: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .page-container {
    padding: 1rem;
  }

  .welcome-section {
    padding: 1.5rem 1rem 1rem;
  }

  .logo {
    width: 2.5rem;
    height: 2.5rem;
  }



  .level-card {
    padding: 1rem;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }

  .level-badge {
    margin-right: 0;
    margin-bottom: 0.75rem;
    align-self: flex-start;
  }

  .selection-indicator {
    align-self: flex-end;
    margin-top: -2rem;
  }

  .page-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .back-button,
  .skip-button,
  .complete-button {
    width: 100%;
  }

  .page-content {
    padding: 1.5rem;
  }

  .page-footer {
    padding: 1rem;
  }
}
</style>
