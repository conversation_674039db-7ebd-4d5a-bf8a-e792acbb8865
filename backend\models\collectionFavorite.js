/**
 * 合集收藏模型
 * 用于存储用户收藏的合集
 */
module.exports = (sequelize, DataTypes) => {
  const CollectionFavorite = sequelize.define(
    "CollectionFavorite",
    {
      // 主键ID，使用nanoid
      id: {
        type: DataTypes.STRING(21),
        primaryKey: true,
        allowNull: false,
      },

      // 合集ID
      collectionId: {
        type: DataTypes.STRING(21),
        allowNull: false,
        field: "collection_id",
        comment: "合集ID",
      },

      // 用户ID
      userId: {
        type: DataTypes.STRING(50),
        allowNull: false,
        field: "user_id",
        comment: "用户ID",
      },
    },
    {
      // 表名
      tableName: "collection_favorites",
      // 时间戳
      timestamps: true,
      underscored: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      // 索引
      indexes: [
        {
          fields: ["collection_id"],
        },
        {
          fields: ["user_id"],
        },
        {
          unique: true,
          fields: ["user_id", "collection_id"],
          name: "unique_user_collection",
        },
      ],
    }
  );

  CollectionFavorite.associate = function (models) {
    // 与合集表关联
    CollectionFavorite.belongsTo(models.Collection, {
      foreignKey: "collectionId",
      as: "collection",
    });

    // 与用户表关联
    CollectionFavorite.belongsTo(models.User, {
      foreignKey: "userId",
      as: "user",
    });
  };

  return CollectionFavorite;
};
