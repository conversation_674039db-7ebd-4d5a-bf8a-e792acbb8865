# 阿里云OSS代理设置指南

本文档介绍如何通过Nginx代理阿里云OSS资源，以解决跨域限制问题。

## 为什么需要代理OSS资源？

在Echo Lab项目中，我们使用阿里云OSS存储音频、图片等资源。直接从前端访问这些资源可能会遇到以下问题：

1. **跨域限制**：浏览器的同源策略可能会阻止直接访问OSS资源
2. **缓存控制**：无法完全控制OSS资源的缓存策略
3. **安全考虑**：直接暴露OSS地址可能导致资源被滥用

通过在自己的服务器上代理OSS资源，我们可以解决这些问题，提供更好的用户体验。

## 实现方案

我们的解决方案包括以下几个部分：

1. **Nginx代理配置**：设置Nginx将特定路径的请求转发到阿里云OSS
2. **后端URL转换**：修改后端返回的OSS URL，使其指向我们的代理路径
3. **前端缓存配置**：更新PWA缓存策略，支持代理URL

## 1. Nginx配置

已经在`echolab.club.conf`文件中添加了以下代理设置：

```nginx
# 阿里云OSS代理配置
location /oss-resources/ {
    # 移除前缀，只保留路径的后半部分
    rewrite ^/oss-resources/(.*) /$1 break;

    # 代理到阿里云OSS
    proxy_pass https://echolab.oss-cn-hongkong.aliyuncs.com/;

    # 缓存设置
    proxy_cache_valid 200 302 304 7d;  # 缓存成功的响应7天
    proxy_cache_valid 404 1m;          # 缓存404响应1分钟

    # 代理头信息
    proxy_set_header Host echolab.oss-cn-hongkong.aliyuncs.com;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    # CORS设置
    add_header Access-Control-Allow-Origin '*';
    add_header Access-Control-Allow-Methods 'GET, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';

    # 处理OPTIONS请求
    if ($request_method = 'OPTIONS') {
        add_header Access-Control-Allow-Origin '*';
        add_header Access-Control-Allow-Methods 'GET, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
        add_header Access-Control-Max-Age 1728000;
        add_header Content-Type 'text/plain charset=UTF-8';
        add_header Content-Length 0;
        return 204;
    }

    # 缓存控制
    add_header Cache-Control "public, max-age=604800"; # 7天缓存

    # 支持范围请求（对音频和视频很重要）
    proxy_force_ranges on;

    # 大文件支持
    client_max_body_size 50M;
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;

    # 启用压缩
    gzip on;
    gzip_types application/javascript text/css image/svg+xml;
    gzip_min_length 1000;
}
```

## 2. 后端配置

### 环境变量设置

在后端的 `.env` 文件中添加以下配置：

```
# OSS代理配置
SERVER_DOMAIN=https://echolab.club
OSS_PROXY_PATH=/oss-resources
```

对于开发环境，使用：

```
# OSS代理配置
SERVER_DOMAIN=http://localhost:3000
OSS_PROXY_PATH=/oss-resources
```

### OSS服务修改

我们修改了 `ossService.js` 中的方法，使其返回完整URL而不是相对路径：

```javascript
// 获取环境变量中的域名配置，如果没有则使用默认值
const DOMAIN = process.env.SERVER_DOMAIN || "https://echolab.club";
const OSS_PROXY_PATH = process.env.OSS_PROXY_PATH || "/oss-resources";

/**
 * 获取资源的代理URL
 * @param {string} ossKey OSS存储键
 * @returns {string} 代理URL（完整URL）
 */
function getProxyUrl(ossKey) {
  return `${DOMAIN}${OSS_PROXY_PATH}/${ossKey}`;
}
```

所有返回OSS URL的方法都已更新为使用完整URL：

- `uploadAudio`
- `put`
- `getSignedUrl`

## 3. 前端配置

### PWA缓存配置

在 `vite.config.js` 中，我们优化了PWA的缓存配置，确保所有通过代理的资源和多媒体文件都能被缓存：

```javascript
workbox: {
  globPatterns: ["**/*.{js,css,html,ico}"], // 基础静态资源
  runtimeCaching: [
    {
      // 所有代理的OSS资源缓存（包括音频、图片等）
      urlPattern: /^(https?:\/\/[^\/]+)?\/oss-resources\/.+$/i,
      handler: "CacheFirst", // 使用缓存优先策略
      options: {
        cacheName: "oss-resources-cache",
        fetchOptions: {
          credentials: "omit", // 不发送凭证
        },
        expiration: {
          maxEntries: 1000, // 增加缓存条目数量
          maxAgeSeconds: 60 * 60 * 24 * 30, // 30天
        },
        cacheableResponse: {
          statuses: [0, 200], // 缓存成功响应和CORS响应
        },
        rangeRequests: true, // 支持范围请求
      },
    },
    {
      // 所有多媒体文件缓存（通用匹配）
      urlPattern: /\.(mp3|wav|ogg|m4a|jpg|jpeg|png|gif|webp|svg)$/i,
      handler: "CacheFirst",
      options: {
        cacheName: "media-cache",
        fetchOptions: {
          credentials: "omit",
        },
        expiration: {
          maxEntries: 500,
          maxAgeSeconds: 60 * 60 * 24 * 30, // 30天
        },
        cacheableResponse: {
          statuses: [0, 200],
        },
        rangeRequests: true,
      },
    },
  ],
}
```

## 部署步骤

1. **更新Nginx配置**：
   - 确保`echolab.club.conf`中包含OSS代理配置
   - 重启Nginx: `sudo systemctl restart nginx`

2. **更新环境变量**：
   - 在生产环境的 `.env` 文件中设置 `SERVER_DOMAIN` 和 `OSS_PROXY_PATH`

3. **部署后端**：
   - 确保后端代码包含最新的OSS代理相关修改
   - 重启后端服务: `pm2 restart echo-lab-backend`

4. **部署前端**：
   - 重新构建前端，确保PWA配置更新: `npm run build`
   - 将构建结果部署到Nginx服务的根目录

## 测试

部署完成后，可以通过以下方式测试代理是否正常工作：

1. 访问一个通过代理的资源URL，例如：`https://echolab.club/oss-resources/audio/ja/example.mp3`
2. 检查浏览器开发者工具中的网络请求，确认请求是否成功
3. 检查PWA缓存是否正常工作（在Application > Cache Storage中查看）

## 故障排除

如果遇到问题，请检查：

1. **Nginx日志**：`/var/log/nginx/error.log` 和 `/var/log/nginx/access.log`
2. **后端日志**：检查PM2日志或应用日志
3. **CORS问题**：确保Nginx配置中的CORS头设置正确
4. **缓存问题**：尝试清除浏览器缓存和PWA缓存

## 注意事项

- 此代理方案会增加服务器的负载，特别是对于大文件
- 考虑设置适当的缓存策略，减少对服务器的请求
- 监控服务器带宽使用情况，必要时调整配置
