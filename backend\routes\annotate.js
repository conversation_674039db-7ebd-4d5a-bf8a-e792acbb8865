/**
 * 标注API路由
 * 提供文本标注功能
 */
const express = require("express");
const router = express.Router();
const yahooFuriganaService = require("../services/yahooFuriganaService");

router.post("/", async (req, res) => {
  try {
    const { segments, language } = req.body;

    // 验证请求参数
    if (!segments || !Array.isArray(segments)) {
      return res.status(400).json({ error: "缺少有效的segments参数" });
    }

    console.log("收到标注请求:", {
      segmentsCount: segments.length,
      language,
    });

    const annotations = {};

    // 将段落按语言分组
    const japaneseSegments = [];
    const otherSegments = [];

    for (const segment of segments) {
      const segmentLanguage = segment.language || language || "";

      if (segmentLanguage === "ja") {
        japaneseSegments.push(segment);
      } else {
        otherSegments.push(segment);
      }
    }

    // 处理日语段落（批量处理）
    if (japaneseSegments.length > 0) {
      try {
        const japaneseResults = await yahooFuriganaService.batchGetFurigana(
          japaneseSegments
        );

        // 将结果添加到annotations对象
        for (const id in japaneseResults) {
          annotations[id] = japaneseResults[id];
        }
      } catch (error) {
        console.error("批量生成假名失败:", error);
        // 失败时返回原文
        for (const segment of japaneseSegments) {
          annotations[segment.id] = {
            reading: segment.content || "",
            characters: [],
          };
        }
      }
    }

    // 处理其他语言段落（直接返回原文）
    for (const segment of otherSegments) {
      annotations[segment.id] = {
        reading: segment.content || "",
        characters: [],
      };
    }

    console.log("返回标注结果:", {
      annotationsCount: Object.keys(annotations).length,
    });

    res.json({ annotations });
  } catch (error) {
    console.error("标注处理错误:", error);
    res.status(500).json({ error: "标注处理失败" });
  }
});

module.exports = router;
