<!--
  标准说话人映射对话框
  基于StandardDialog组件实现的说话人映射设置对话框
-->
<template>
  <div>
    <standard-dialog v-model="dialogVisible" title="说话人映射设置" width="800px" :show-confirm="false" :cancel-text="'关闭'"
      :extra-buttons="[
        { text: '保存', type: 'primary', closeDialog: true, event: 'save' }
      ]" @save="saveMapping">
      <el-container v-loading="loading" class="speaker-mapping-container">
        <!-- 左侧语言选择 -->
        <el-aside width="25%" class="language-sidebar">
          <h3 class="sidebar-title">选择语言</h3>
          <el-menu :default-active="currentLanguage" @select="handleLanguageChange" class="language-menu">
            <el-menu-item v-for="lang in availableLanguages" :key="lang.code" :index="lang.code">
              {{ lang.name }}
            </el-menu-item>
          </el-menu>
        </el-aside>

        <!-- 主内容区 -->
        <el-main class="mapping-main">
          <div class="mapping-header">
            <h3 class="language-title">{{ currentLanguageName }} 说话人映射</h3>
            <div class="header-buttons">
              <el-button type="primary" size="small" @click="openAddDialog" class="add-button">添加映射</el-button>
              <el-button type="warning" size="small" plain @click="resetToDefault"
                class="reset-button">重置为默认</el-button>
            </div>
          </div>

          <!-- 映射列表 -->
          <div class="mapping-list">
            <el-table :data="mappingTableData" border stripe class="mapping-table">
              <el-table-column label="说话人" prop="speaker" width="180" align="center">
                <template #default="{ row }">
                  <el-tag v-if="row.speaker === 'default'" type="info" class="speaker-tag" effect="plain">默认</el-tag>
                  <el-tag v-else-if="row.speaker === 'A'" type="success" class="speaker-tag" effect="plain">A</el-tag>
                  <el-tag v-else-if="row.speaker === 'B'" type="primary" class="speaker-tag" effect="plain">B</el-tag>
                  <el-tag v-else-if="row.speaker === 'C'" type="warning" class="speaker-tag" effect="plain">C</el-tag>
                  <span v-else class="speaker-name">{{ row.speaker }}</span>
                </template>
              </el-table-column>
              <el-table-column label="声音" prop="voiceName" align="center">
                <template #default="{ row }">
                  <span class="voice-name-cell">{{ row.voiceName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150" align="center">
                <template #default="{ row }">
                  <div class="operation-buttons">
                    <el-button type="primary" size="small" circle @click="editMapping(row)"
                      :disabled="row.speaker === 'default'" class="edit-button">
                      <el-icon>
                        <i-ep-edit />
                      </el-icon>
                    </el-button>
                    <el-button type="danger" size="small" circle @click="deleteMapping(row.speaker)"
                      :disabled="row.speaker === 'default'" class="delete-button">
                      <el-icon>
                        <i-ep-delete />
                      </el-icon>
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-main>
      </el-container>
    </standard-dialog>

    <!-- 添加/编辑映射弹框 -->
    <el-dialog v-model="mappingDialogVisible" :title="isEditing ? '编辑说话人映射' : '添加说话人映射'" width="500px"
      :append-to-body="true" :destroy-on-close="true" :close-on-click-modal="false" @closed="handleDialogClosed">
      <el-form class="mapping-dialog-form">
        <el-form-item label="说话人名称" required>
          <el-input v-model="editForm.speakerName" placeholder="输入说话人名称" :disabled="isEditing && isDefaultSpeaker" />
        </el-form-item>
        <el-form-item label="声音" required>
          <el-select v-model="editForm.voiceId" placeholder="选择声音" style="width: 100%">
            <el-option-group label="可用声音">
              <el-option v-for="voice in currentVoices" :key="voice.id"
                :label="`${voice.name}${voice.is_premium ? ' (高级)' : ''}`" :value="voice.id">
                <div class="voice-option">
                  <span class="voice-name">{{ voice.name }}</span>
                  <el-tag v-if="voice.is_premium" size="small" type="danger" class="voice-tag">高级</el-tag>
                  <el-tag v-else size="small" type="success" class="voice-tag">免费</el-tag>
                </div>
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeMappingDialog">取消</el-button>
          <el-button type="primary" @click="confirmMapping">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { getTtsInfo, getDefaultMappings } from '@/services/ttsInfoService';
import {
  saveSpeakerMappings,
  loadSpeakerMappings
} from '@/services/speakerMappingService';
import StandardDialog from '../common/StandardDialog.vue';
import { cloneDeep } from '@/utils/cloneUtils';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'saved']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

// 数据状态
const loading = ref(false);
const ttsInfo = ref(null);
const originalMappings = ref({}); // 原始数据
const editableMappings = ref({}); // 可编辑的副本
const currentLanguage = ref('');
const hasUnsavedChanges = ref(false);

// 编辑状态
const mappingDialogVisible = ref(false);
const isEditing = ref(false);
const isDefaultSpeaker = ref(false);
const editForm = ref({
  speakerName: '',
  voiceId: null,
  originalName: ''
});

// 计算属性
const availableLanguages = computed(() => {
  if (!ttsInfo.value) return [];
  return Object.keys(ttsInfo.value.languages).map(code => ({
    code,
    name: ttsInfo.value.languages[code].name
  }));
});

const currentLanguageName = computed(() => {
  const lang = availableLanguages.value.find(l => l.code === currentLanguage.value);
  return lang ? lang.name : '未知语言';
});

const currentVoices = computed(() => {
  if (!ttsInfo.value || !currentLanguage.value) return [];

  // 获取当前语言的声音列表

  // 获取当前语言的声音ID列表
  const languageInfo = ttsInfo.value.languages[currentLanguage.value];
  if (!languageInfo || !languageInfo.voice_ids || !languageInfo.voice_ids.length) {
    console.log(`没有找到语言 ${currentLanguage.value} 的声音`);
    return [];
  }

  // 根据ID列表从voices数组中获取完整声音信息
  const voices = languageInfo.voice_ids
    .map(id => ttsInfo.value.voices.find(voice => voice.id === id))
    .filter(voice => voice && !voice.disabled);

  // 声音对象中已经包含了service_name，直接使用
  return voices.map(voice => {
    return {
      ...voice,
      serviceName: voice.service_name || '未知服务'
    };
  });
});

const currentMappings = computed(() => {
  if (!editableMappings.value || !currentLanguage.value) return {};
  return editableMappings.value[currentLanguage.value] || {};
});

const mappingTableData = computed(() => {
  // 构建映射表格数据

  return Object.entries(currentMappings.value).map(([speaker, voiceId]) => {
    // 查找声音信息
    const voice = currentVoices.value.find(v => v.id === voiceId);


    // 构建显示名称
    let displayName;
    if (voice) {
      displayName = voice.name;
    } else {
      // 如果在当前语言的声音中找不到，尝试在所有声音中查找
      if (ttsInfo.value) {
        const allVoice = ttsInfo.value.voices.find(v => v.id === voiceId);
        if (allVoice) {
          // 使用声音的name属性和语言代码
          displayName = `${allVoice.name} (${allVoice.language_code})`;
        } else {
          displayName = `未知声音 (ID: ${voiceId})`;
        }
      } else {
        displayName = `未知声音 (ID: ${voiceId})`;
      }
    }

    return {
      speaker,
      voiceId,
      voiceName: displayName
    };
  });
});

// 初始化数据
async function fetchData() {
  loading.value = true;
  try {
    // 获取TTS服务信息
    ttsInfo.value = await getTtsInfo();

    // 尝试从本地存储加载用户自定义映射
    const savedMappings = loadSpeakerMappings();

    if (savedMappings) {
      // 使用本地存储的映射
      originalMappings.value = cloneDeep(savedMappings);
    } else {
      // 使用默认映射
      const defaultMappings = await getDefaultMappings();
      originalMappings.value = cloneDeep(defaultMappings);
    }

    // 创建可编辑副本
    editableMappings.value = cloneDeep(originalMappings.value);
    hasUnsavedChanges.value = false;

    // 设置初始语言
    if (availableLanguages.value.length > 0) {
      currentLanguage.value = availableLanguages.value[0].code;
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    ElMessage.error('获取数据失败: ' + error.message);
  } finally {
    loading.value = false;
  }
}

// 语言切换处理
function handleLanguageChange(langCode) {
  currentLanguage.value = langCode;
}

// 打开添加映射弹框
function openAddDialog() {
  isEditing.value = false;
  isDefaultSpeaker.value = false;
  editForm.value = {
    speakerName: '',
    voiceId: null,
    originalName: ''
  };
  mappingDialogVisible.value = true;
}

// 编辑映射
function editMapping(row) {
  isEditing.value = true;
  isDefaultSpeaker.value = row.speaker === 'default' || row.speaker === 'A' || row.speaker === 'B' || row.speaker === 'C';
  editForm.value = {
    speakerName: row.speaker,
    voiceId: row.voiceId,
    originalName: row.speaker
  };
  mappingDialogVisible.value = true;
}

// 关闭映射弹框
function closeMappingDialog() {
  mappingDialogVisible.value = false;
}

// 处理弹框关闭事件
function handleDialogClosed() {
  isEditing.value = false;
  isDefaultSpeaker.value = false;
  editForm.value = {
    speakerName: '',
    voiceId: null,
    originalName: ''
  };
}

// 确认添加/编辑映射
function confirmMapping() {
  // 验证表单
  if (!editForm.value.speakerName.trim()) {
    ElMessage.warning('请输入说话人名称');
    return;
  }

  if (!editForm.value.voiceId) {
    ElMessage.warning('请选择声音');
    return;
  }

  // 确保当前语言的映射对象存在
  if (!editableMappings.value[currentLanguage.value]) {
    editableMappings.value[currentLanguage.value] = {};
  }

  // 检查是否存在同名映射（非编辑模式或名称已更改）
  const nameChanged = isEditing.value && editForm.value.originalName !== editForm.value.speakerName;
  const speakerExists = editableMappings.value[currentLanguage.value][editForm.value.speakerName] !== undefined &&
    (!isEditing.value || nameChanged);

  if (speakerExists) {
    ElMessageBox.confirm(
      `说话人 "${editForm.value.speakerName}" 已存在，是否覆盖现有映射？`,
      '覆盖确认',
      {
        confirmButtonText: '覆盖',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      // 用户确认覆盖
      applyMapping();
    }).catch(() => {
      // 用户取消，不做操作
    });
  } else {
    // 不存在同名映射，直接更新
    applyMapping();
  }
}

// 应用映射更改
function applyMapping() {
  // 如果是编辑模式且名称已更改，需要删除旧映射
  if (isEditing.value && editForm.value.originalName !== editForm.value.speakerName) {
    delete editableMappings.value[currentLanguage.value][editForm.value.originalName];
  }

  // 添加或更新映射
  editableMappings.value[currentLanguage.value][editForm.value.speakerName] = editForm.value.voiceId;

  // 标记有未保存的更改
  hasUnsavedChanges.value = true;

  // 关闭弹框
  mappingDialogVisible.value = false;

  ElMessage.success(isEditing.value ? '映射已更新，请点击保存按钮使更改生效' : '映射已添加，请点击保存按钮使更改生效');
}

// 删除映射
function deleteMapping(speakerName) {
  ElMessageBox.confirm(
    `确定要删除说话人 "${speakerName}" 的映射吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    if (editableMappings.value[currentLanguage.value]) {
      delete editableMappings.value[currentLanguage.value][speakerName];
      hasUnsavedChanges.value = true;
      ElMessage.success('映射已删除，请点击保存按钮使更改生效');
    }
  }).catch(() => {
    // 用户取消，不做操作
  });
}

// 重置为默认映射
function resetToDefault() {
  ElMessageBox.confirm(
    '确定要重置为默认映射吗？当前的自定义映射将被覆盖。',
    '重置确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // 获取默认映射
      const defaultMappings = await getDefaultMappings();

      // 重置可编辑副本为默认映射
      editableMappings.value = cloneDeep(defaultMappings);
      hasUnsavedChanges.value = true;

      ElMessage.success('已重置为默认映射，请点击保存按钮使更改生效');
    } catch (error) {
      console.error('重置为默认映射失败:', error);
      ElMessage.error('重置失败: ' + error.message);
    }
  }).catch(() => {
    // 用户取消，不做操作
  });
}

// 保存映射
function saveMapping() {
  // 如果没有未保存的更改，不执行保存操作
  if (!hasUnsavedChanges.value) {
    return;
  }

  // 保存到本地存储
  saveSpeakerMappings(editableMappings.value);

  // 更新原始数据
  originalMappings.value = cloneDeep(editableMappings.value);
  hasUnsavedChanges.value = false;

  // 通知父组件映射已保存
  emit('saved', editableMappings.value);

  ElMessage.success('说话人映射已保存');
}

// 监听对话框关闭事件，如果有未保存的更改，恢复到原始数据
watch(dialogVisible, (newVal, oldVal) => {
  if (oldVal && !newVal && hasUnsavedChanges.value) {
    // 对话框关闭时，如果有未保存的更改，恢复到原始数据
    editableMappings.value = cloneDeep(originalMappings.value);
    hasUnsavedChanges.value = false;
  }
});

// 组件挂载时获取数据
onMounted(() => {
  fetchData();
});
</script>

<style src="./StandardSpeakerMappingDialog.css" scoped></style>
