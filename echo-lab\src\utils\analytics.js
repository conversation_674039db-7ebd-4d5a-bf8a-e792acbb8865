/**
 * 统计分析工具函数
 * 提供 Google Analytics 和百度统计的跟踪功能
 */

import {
  shouldEnableAnalytics,
  GA_MEASUREMENT_ID,
} from "@/config/analytics.js";

/**
 * 等待 GA 加载完成
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise<boolean>} - 是否加载成功
 */
function waitForGA(timeout = 5000) {
  return new Promise((resolve) => {
    if (window.gtag) {
      resolve(true);
      return;
    }

    const startTime = Date.now();
    const checkInterval = setInterval(() => {
      if (window.gtag) {
        clearInterval(checkInterval);
        resolve(true);
      } else if (Date.now() - startTime > timeout) {
        clearInterval(checkInterval);
        resolve(false);
      }
    }, 100);
  });
}

/**
 * 通用的 gtag 调用包装器
 * @param {Function} gtagCall - gtag 调用函数
 * @returns {Promise<boolean>} - 是否成功
 */
async function executeGtagCall(gtagCall) {
  if (!shouldEnableAnalytics()) {
    return false;
  }

  // 等待 GA 加载完成
  const gaReady = await waitForGA(2000);
  if (!gaReady) {
    return false;
  }

  try {
    gtagCall();
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * 跟踪页面浏览
 * @param {string} pagePath - 页面路径
 * @param {string} [pageTitle] - 页面标题
 * @returns {Promise<boolean>} - 是否成功发送事件
 */
export async function trackPageView(pagePath, pageTitle) {
  const title = pageTitle || document.title;

  // Google Analytics 跟踪
  const gaResult = await executeGtagCall(() => {
    window.gtag("config", GA_MEASUREMENT_ID, {
      page_path: pagePath,
      page_title: title,
    });
  });

  // 百度统计跟踪
  let baiduResult = false;
  try {
    if (window._hmt && shouldEnableAnalytics()) {
      window._hmt.push(["_trackPageview", pagePath]);
      baiduResult = true;
    }
  } catch (error) {
    baiduResult = false;
  }

  return gaResult || baiduResult;
}

/**
 * 跟踪用户事件
 * @param {string} eventName - 事件名称
 * @param {Object} [eventData={}] - 事件数据
 * @returns {Promise<boolean>} - 是否成功发送事件
 */
export async function trackEvent(eventName, eventData = {}) {
  // Google Analytics 跟踪
  const gaResult = await executeGtagCall(() => {
    window.gtag("event", eventName, {
      event_category: "user_action",
      ...eventData,
    });
  });

  // 百度统计跟踪
  let baiduResult = false;
  try {
    if (window._hmt && shouldEnableAnalytics()) {
      // 百度统计事件跟踪格式：['_trackEvent', category, action, label, value]
      window._hmt.push([
        "_trackEvent",
        "user_action",
        eventName,
        JSON.stringify(eventData),
      ]);
      baiduResult = true;
    }
  } catch (error) {
    baiduResult = false;
  }

  return gaResult || baiduResult;
}

/**
 * 常用事件名称常量
 * 使用常量可以确保事件名称的一致性
 */
export const EVENTS = {
  // 核心页面访问
  HOME_PAGE_VISIT: "home_page_visit",
  PLAYER_PAGE_VISIT: "player_page_visit",
  PROJECT_LOADED: "project_loaded",

  // 核心用户行为
  COLLECTION_FAVORITE: "collection_favorite",
  COLLECTION_UNFAVORITE: "collection_unfavorite",
  
  // 播放器设置
  PLAYBACK_SETTINGS_OPEN: "playback_settings_open",
};

export default {
  trackPageView,
  trackEvent,
  EVENTS,
};
