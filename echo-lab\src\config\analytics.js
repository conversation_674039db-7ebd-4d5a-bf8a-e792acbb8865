/**
 * Google Analytics 配置
 */

// Google Analytics 测量 ID
export const GA_MEASUREMENT_ID = "G-601C2V1EC4";

// 百度统计 ID
export const BAIDU_ANALYTICS_ID = "c524045722690cf554704ca69374ee3e";

// Google Analytics 配置
export const ANALYTICS_CONFIG = {
  // 是否只在生产环境启用
  enableOnlyInProduction: false,

  // 允许的域名列表
  allowedDomains: ["echolab.club"],
};

/**
 * 获取完整的 gtag.js 脚本 URL
 * @returns {string} 完整的脚本 URL
 */
export function getGtagScriptUrl() {
  return `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
}

/**
 * 获取 Google Analytics 配置对象
 * @returns {Object} GA 配置对象
 */
export function getGAConfig() {
  return {
    debug_mode: import.meta.env.DEV, // 开发环境启用调试模式
  };
}

/**
 * 检查是否应该启用统计
 * @returns {boolean} 是否启用统计
 */
export function shouldEnableAnalytics() {
  // 检查localStorage中的skipAnalytics标记
  try {
    const skipAnalytics = localStorage.getItem('skipAnalytics');
    if (skipAnalytics === 'true') {
      return false;
    }
  } catch (error) {
    // localStorage访问失败时继续执行其他检查
  }

  // 检查域名是否在允许列表中
  const currentHostname = window.location.hostname;
  if (!ANALYTICS_CONFIG.allowedDomains.includes(currentHostname)) {
    return false;
  }

  // 如果配置为只在生产环境启用，则检查环境
  if (ANALYTICS_CONFIG.enableOnlyInProduction) {
    return import.meta.env.PROD;
  }

  return true;
}

/**
 * 初始化百度统计
 * @returns {Promise<boolean>} 是否初始化成功
 */
export function initializeBaiduAnalytics() {
  return new Promise((resolve) => {
    if (!shouldEnableAnalytics()) {
      resolve(false);
      return;
    }

    // 检查是否已经加载
    if (window._hmt) {
      resolve(true);
      return;
    }

    try {
      // 初始化百度统计
      window._hmt = window._hmt || [];

      const script = document.createElement("script");
      script.async = true; // 异步加载
      script.src = `https://hm.baidu.com/hm.js?${BAIDU_ANALYTICS_ID}`;

      script.onload = () => {
        resolve(true);
      };

      script.onerror = () => {
        resolve(false);
      };

      const firstScript = document.getElementsByTagName("script")[0];
      firstScript.parentNode.insertBefore(script, firstScript);
    } catch (error) {
      resolve(false);
    }
  });
}

/**
 * 动态初始化 Google Analytics
 * @returns {Promise<boolean>} 是否初始化成功
 */
export function initializeAnalytics() {
  return new Promise((resolve) => {
    if (!shouldEnableAnalytics()) {
      resolve(false);
      return;
    }

    // 检查是否已经加载
    if (window.gtag) {
      resolve(true);
      return;
    }

    const scriptUrl = getGtagScriptUrl();
    const config = getGAConfig();

    // 动态加载 gtag.js
    const script = document.createElement("script");
    script.async = true;
    script.src = scriptUrl;

    script.onload = () => {
      // 初始化 dataLayer 和 gtag 函数
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      window.gtag = gtag;

      gtag("js", new Date());
      gtag("config", GA_MEASUREMENT_ID, config);

      resolve(true);
    };

    script.onerror = () => {
      resolve(false);
    };

    document.head.appendChild(script);
  });
}
