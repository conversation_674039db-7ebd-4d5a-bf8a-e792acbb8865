#!/bin/bash

# 数据库备份脚本
# 用于备份MySQL数据库到指定目录

# 加载环境变量（如果存在）
if [ -f ../.env ]; then
  source ../.env
fi

# 数据库配置
DB_USER=${DB_USERNAME:-"root"}
DB_PASSWORD=${DB_PASSWORD:-""}
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"3306"}
DB_NAME=${DB_NAME:-"echo-lab"}

# 备份目录配置
BACKUP_DIR="../backups"
BACKUP_FILENAME="echo-lab_$(date +%Y%m%d_%H%M%S).sql"
BACKUP_PATH="$BACKUP_DIR/$BACKUP_FILENAME"

# 保留的备份文件数量（默认保留最近7个备份）
KEEP_BACKUPS=7

# 创建备份目录（如果不存在）
mkdir -p $BACKUP_DIR

# 显示备份信息
echo "开始备份数据库..."
echo "数据库: $DB_NAME"
echo "备份文件: $BACKUP_PATH"

# 执行备份
if [ -z "$DB_PASSWORD" ]; then
  # 无密码情况
  mysqldump -h $DB_HOST -P $DB_PORT -u $DB_USER $DB_NAME > $BACKUP_PATH
else
  # 有密码情况
  mysqldump -h $DB_HOST -P $DB_PORT -u $DB_USER -p"$DB_PASSWORD" $DB_NAME > $BACKUP_PATH
fi

# 检查备份是否成功
if [ $? -eq 0 ]; then
  echo "备份成功！"

  # 压缩备份文件
  gzip $BACKUP_PATH
  echo "备份文件已压缩: $BACKUP_PATH.gz"

  # 删除旧备份文件，只保留最近的N个备份
  cd $BACKUP_DIR
  ls -t *.gz | tail -n +$((KEEP_BACKUPS+1)) | xargs -r rm
  echo "已清理旧备份，保留最近 $KEEP_BACKUPS 个备份"

  # 显示当前备份列表
  echo "当前备份列表:"
  ls -lh *.gz | sort -r
else
  echo "备份失败，请检查数据库连接信息"
  exit 1
fi
