<!--
  资源节点标注组件
  处理文本标注功能
-->
<template>
  <div class="annotation-component">
    <div class="preview-section">
      <div class="preview-header">
        <div>标注：</div>
        <el-button-group>
          <el-button size="small" type="primary" @click="showDialog" :disabled="!hasSourceNode">
            <el-icon class="el-icon--left">
              <i-ep-edit />
            </el-icon>
            编辑标注
          </el-button>
        </el-button-group>
      </div>
      <div class="preview-info"
        :class="{ 'success-bg': getAnnotationPercentage() === 100 && totalAnnotationCount > 0, 'warning-bg': getAnnotationPercentage() < 100 && totalAnnotationCount > 0 }">
        <div class="preview-progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: getAnnotationPercentage() + '%' }"></div>
          </div>
          <div class="progress-text">
            已标注: {{ annotationCount }}/{{ totalAnnotationCount }}
            <span v-if="getAnnotationPercentage() === 100" class="status-tag success">完成</span>
            <span v-else-if="totalAnnotationCount > 0" class="status-tag warning">未完成</span>
            <span v-else class="status-tag info">无需标注</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 标注编辑对话框 -->
    <standard-dialog v-model="dialogVisible" title="编辑标注" width="80%">
      <div class="dialog-content">
        <div class="dialog-toolbar">
          <div class="dialog-actions">
            <el-input v-model="searchText" placeholder="搜索内容" clearable style="width: 15rem;">
              <template #prefix>
                <el-icon>
                  <i-ep-search />
                </el-icon>
              </template>
            </el-input>
          </div>
        </div>

        <div class="annotations-table-container">
          <el-table :data="filteredAnnotations" border style="width: 100%" max-height="60vh">
            <el-table-column label="序号" width="80" align="center" fixed="left">
              <template #default="{ $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column>

            <el-table-column label="类型" width="100" fixed="left">
              <template #default="{ row }">
                <el-tag v-if="row.isKeyword || row.type === 'keyword'" size="small" type="warning">关键词</el-tag>
                <el-tag v-else-if="row.type === 'transition'" size="small" type="info">转场</el-tag>
                <el-tag v-else size="small" type="primary">普通文本</el-tag>
              </template>
            </el-table-column>

            <el-table-column label="原文" min-width="200" fixed="left">
              <template #default="{ row }">
                <div class="original-text-cell">{{ row.content }}</div>
                <div class="language-detection">
                  <span class="language-label">语言:</span>
                  <el-tag size="small" type="info">{{ getLanguageLabel(row.detectedLanguage) }}</el-tag>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="标注" min-width="300">
              <template #default="{ row }">
                <div v-if="row.detectedLanguage === 'ja'" class="annotation-container">
                  <div v-if="row.characters && row.characters.length > 0" class="character-annotation-preview">
                    <CharacterAnnotation :characters="row.characters"
                      @update:characters="updateCharacters(row, $event)" />
                  </div>
                </div>
                <el-input v-else v-model="row.annotation" type="textarea" :rows="3"
                  :class="{ 'empty-annotation': !row.annotation || !row.annotation.trim() }" />
              </template>
            </el-table-column>

            <el-table-column label="操作" width="120" align="center" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="annotateItem(row)">
                  <el-icon style="margin-right: 0.25rem;">
                    <i-ep-edit />
                  </el-icon>
                  标注
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveAnnotations">保存修改</el-button>
          <el-dropdown>
            <el-button type="success" :loading="isAnnotating">
              <el-icon style="margin-right: 0.25rem;">
                <i-ep-edit />
              </el-icon>
              批量标注
              <el-icon class="el-icon--right">
                <i-ep-arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="batchAnnotateAll">重新标注(全部)</el-dropdown-item>
                <el-dropdown-item @click="batchAnnotateUnannotated">补全(未标注内容)</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>
    </standard-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import StandardDialog from '../common/StandardDialog.vue';
import CharacterAnnotation from '../common/CharacterAnnotation.vue';
import { getLanguageLabel } from '@/config/languages';

const props = defineProps({
  nodeId: {
    type: String,
    required: true
  },
  processedResult: {
    type: Object,
    default: () => null
  },
  params: {
    type: Object,
    required: true
  },
  hasSourceNode: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:params', 'process-node']);

// 对话框状态
const dialogVisible = ref(false);
const isAnnotating = ref(false);
const searchText = ref('');

// 标注项
const annotationItems = ref([]);

// 标注相关计算属性
const annotationCount = computed(() => {
  if (!props.processedResult || !props.processedResult.sourceSegments || !props.processedResult.annotations) return 0;

  // 计算有内容的标注项数量
  const annotations = props.processedResult.annotations;
  let count = 0;

  // 遍历所有分句和关键词，检查是否有对应的标注
  props.processedResult.sourceSegments.forEach(segment => {
    // 检查分句标注
    if (annotations[segment.id]) {
      const annotation = annotations[segment.id];
      if (typeof annotation === 'object' && annotation !== null) {
        // 对象类型标注（日语）
        if (annotation.reading && annotation.reading.trim()) {
          count++;
        }
      } else if (annotation && typeof annotation === 'string' && annotation.trim()) {
        // 字符串类型标注（非日语）
        count++;
      }
    }

    // 检查关键词标注
    if (segment.keywords && segment.keywords.length > 0) {
      segment.keywords.forEach(keyword => {
        if (annotations[keyword.id]) {
          const annotation = annotations[keyword.id];
          if (typeof annotation === 'object' && annotation !== null) {
            // 对象类型标注（日语）
            if (annotation.reading && annotation.reading.trim()) {
              count++;
            }
          } else if (annotation && typeof annotation === 'string' && annotation.trim()) {
            // 字符串类型标注（非日语）
            count++;
          }
        }
      });
    }
  });

  return count;
});

const totalAnnotationCount = computed(() => {
  if (!props.processedResult || !props.processedResult.sourceSegments) return 0;

  // 计算所有需要标注的项目数量（包括分句和关键词）
  let total = 0;
  props.processedResult.sourceSegments.forEach(segment => {
    // 计算分句本身
    total++;

    // 计算关键词
    if (segment.keywords && segment.keywords.length > 0) {
      total += segment.keywords.length;
    }
  });

  return total;
});

// 过滤标注
const filteredAnnotations = computed(() => {
  if (!searchText.value) return annotationItems.value;

  const searchLower = searchText.value.toLowerCase();

  return annotationItems.value.filter(item => {
    // 检查内容是否匹配
    if (item.content.toLowerCase().includes(searchLower)) {
      return true;
    }

    // 检查标注是否匹配
    if (item.annotation) {
      if (typeof item.annotation === 'object') {
        // 对象类型标注（日语）
        return item.annotation.reading &&
          item.annotation.reading.toLowerCase().includes(searchLower);
      } else {
        // 字符串类型标注（非日语）
        return typeof item.annotation === 'string' &&
          item.annotation.toLowerCase().includes(searchLower);
      }
    }

    return false;
  });
});

// 获取标注完成百分比
function getAnnotationPercentage() {
  if (!props.processedResult || !props.processedResult.annotations || totalAnnotationCount.value === 0) {
    return 0;
  }
  return Math.round((annotationCount.value / totalAnnotationCount.value) * 100);
}

// 显示对话框
function showDialog() {
  // 强制重新处理节点
  emit('process-node');

  // 准备标注项
  prepareAnnotationItems();

  // 显示对话框
  dialogVisible.value = true;
}

// 准备标注项
function prepareAnnotationItems() {
  annotationItems.value = [];

  // 如果没有处理结果，但有参数中的标注数据，尝试从源节点获取分句
  if (!props.processedResult || !props.processedResult.sourceSegments) {
    // 如果有参数中的标注数据，直接使用
    if (props.params.annotations && Object.keys(props.params.annotations).length > 0) {
      Object.entries(props.params.annotations).forEach(([id, annotation]) => {
        // 尝试从音频项中获取内容
        let content = '';
        let detectedLanguage = 'ja'; // 默认为日语

        // 如果有音频项，尝试从中获取内容
        if (props.params.audioItems) {
          const audioItem = props.params.audioItems.find(item => item.id === id);
          if (audioItem) {
            content = audioItem.text || '';
            detectedLanguage = audioItem.language || 'ja';
          }
        }

        // 处理日语标注
        if (detectedLanguage === 'ja') {
          // 确保annotation是对象格式
          const annotationObj = typeof annotation === 'object' ? annotation : { reading: annotation || '', characters: [] };

          // 创建标注项
          annotationItems.value.push({
            id: id,
            content: content || '未找到原文',
            annotation: annotationObj,
            characters: annotationObj.characters || [],
            detectedLanguage: detectedLanguage,
            isAnnotating: false
          });
        } else {
          // 非日语标注
          annotationItems.value.push({
            id: id,
            content: content || '未找到原文',
            annotation: annotation,
            detectedLanguage: detectedLanguage,
            isAnnotating: false
          });
        }
      });
      return;
    }
  }

  // 正常处理流程：从处理结果中获取数据
  if (props.processedResult && props.processedResult.sourceSegments && props.processedResult.sourceSegments.length > 0) {
    // 处理所有分句和它们的关键词
    props.processedResult.sourceSegments.forEach(segment => {
      // 使用源节点提供的语言信息
      const detectedLanguage = segment.language || 'auto';

      // 获取标注数据，优先使用处理结果中的标注
      const annotation = (props.processedResult.annotations && props.processedResult.annotations[segment.id]) ||
        (props.params.annotations && props.params.annotations[segment.id]) || '';

      // 处理日语标注
      if (detectedLanguage === 'ja') {
        // 确保annotation是对象格式
        const annotationObj = typeof annotation === 'object' ? annotation : { reading: annotation || '', characters: [] };

        // 创建标注项
        annotationItems.value.push({
          id: segment.id,
          content: segment.content,
          annotation: annotationObj,
          characters: annotationObj.characters || [],
          detectedLanguage: detectedLanguage,
          isAnnotating: false,
          type: segment.type || 'normal',
          isKeyword: segment.isKeyword || segment.type === 'keyword'
        });
      } else {
        // 非日语标注
        annotationItems.value.push({
          id: segment.id,
          content: segment.content,
          annotation: annotation,
          detectedLanguage: detectedLanguage,
          isAnnotating: false,
          type: segment.type || 'normal',
          isKeyword: segment.isKeyword || segment.type === 'keyword'
        });
      }

      // 处理关键词
      if (segment.keywords && segment.keywords.length > 0) {
        segment.keywords.forEach(keyword => {
          // 获取关键词的标注数据
          const keywordAnnotation = (props.processedResult.annotations && props.processedResult.annotations[keyword.id]) ||
            (props.params.annotations && props.params.annotations[keyword.id]) || '';

          // 关键词的语言通常与其所属分句相同
          const keywordLanguage = keyword.language || segment.language || 'auto';

          // 处理日语关键词标注
          if (keywordLanguage === 'ja') {
            // 确保annotation是对象格式
            const keywordAnnotationObj = typeof keywordAnnotation === 'object' ?
              keywordAnnotation : { reading: keywordAnnotation || '', characters: [] };

            // 创建关键词标注项
            annotationItems.value.push({
              id: keyword.id,
              content: keyword.text,
              annotation: keywordAnnotationObj,
              characters: keywordAnnotationObj.characters || [],
              detectedLanguage: keywordLanguage,
              isAnnotating: false,
              type: 'keyword',
              isKeyword: true,
              sourceSegmentId: segment.id
            });
          } else {
            // 非日语关键词标注
            annotationItems.value.push({
              id: keyword.id,
              content: keyword.text,
              annotation: keywordAnnotation,
              detectedLanguage: keywordLanguage,
              isAnnotating: false,
              type: 'keyword',
              isKeyword: true,
              sourceSegmentId: segment.id
            });
          }
        });
      }
    });
  }
}

// 批量标注文本
async function batchAnnotate(mode = 'all') {
  if (annotationItems.value.length === 0) {
    ElMessage.warning('没有可标注的内容');
    return;
  }

  try {
    isAnnotating.value = true;

    // 显示加载提示
    const loadingText = mode === 'all' ? '正在重新标注(全部)...' : '正在补全(未标注内容)...';
    const loadingInstance = ElLoading.service({
      text: loadingText,
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 筛选需要标注的日语项
    let itemsToAnnotate;
    if (mode === 'all') {
      // 标注所有日语项
      itemsToAnnotate = annotationItems.value.filter(item => item.detectedLanguage === 'ja');
    } else {
      // 只标注未标注的日语项
      itemsToAnnotate = annotationItems.value.filter(item => {
        if (item.detectedLanguage !== 'ja') return false;

        // 检查是否已有标注
        if (typeof item.annotation === 'object' && item.annotation !== null) {
          // 对于日语项，检查是否有reading和characters
          return !item.annotation.reading || !item.annotation.reading.trim() ||
            !item.annotation.characters || item.annotation.characters.length === 0;
        } else {
          // 如果annotation不是对象，则视为未标注
          return true;
        }
      });
    }

    if (itemsToAnnotate.length === 0) {
      loadingInstance.close();
      const message = mode === 'all' ? '没有可标注的日语内容' : '(未标注内容)已全部标注';
      ElMessage.info(message);
      isAnnotating.value = false;
      return;
    }

    // 准备批量标注请求
    const segments = itemsToAnnotate.map(item => ({
      id: item.id,
      content: item.content,
      language: item.detectedLanguage,
      type: item.type || 'normal',
      isKeyword: item.isKeyword || item.type === 'keyword'
    }));

    // 调用批量标注API
    const response = await fetch('/api/annotate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        segments: segments,
        language: 'ja'
      })
    });

    const result = await response.json();

    if (result.annotations) {
      // 更新标注结果
      Object.keys(result.annotations).forEach(id => {
        const items = annotationItems.value.filter(i => i.id === id);
        items.forEach(item => {
          // 更新标注数据
          item.annotation = result.annotations[id];

          // 如果返回了字符级标注，更新字符数组
          if (item.annotation.characters && item.annotation.characters.length > 0) {
            item.characters = item.annotation.characters;
          }
        });
      });
    }

    // 关闭加载提示
    loadingInstance.close();

    const successMessage = mode === 'all'
      ? '重新标注(全部)完成'
      : `已补全 ${Object.keys(result.annotations || {}).length} 个(未标注内容)`;
    ElMessage.success(successMessage);
  } catch (error) {
    console.error('批量标注失败:', error);
    ElMessage.error('批量标注失败: ' + error.message);
  } finally {
    isAnnotating.value = false;
  }
}

// 批量标注所有文本
function batchAnnotateAll() {
  return batchAnnotate('all');
}

// 只标注未标注的文本
function batchAnnotateUnannotated() {
  return batchAnnotate('unannotated');
}

// 标注单个项
async function annotateItem(item) {
  if (!item || !item.content || item.detectedLanguage !== 'ja') {
    return;
  }

  try {
    // 设置标注状态
    item.isAnnotating = true;

    // 调用批量标注API（单个项也使用批量接口）
    const response = await fetch('/api/annotate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        segments: [{
          id: item.id,
          content: item.content,
          language: item.detectedLanguage,
          type: item.type || 'normal',
          isKeyword: item.isKeyword || item.type === 'keyword'
        }],
        language: item.detectedLanguage
      })
    });

    const result = await response.json();

    if (result.annotations && result.annotations[item.id]) {
      // 更新标注数据
      item.annotation = result.annotations[item.id];

      // 如果返回了字符级标注，更新字符数组
      if (item.annotation.characters && item.annotation.characters.length > 0) {
        item.characters = item.annotation.characters;
      }
    }
  } catch (error) {
    console.error('标注失败:', error);
    throw error;
  } finally {
    item.isAnnotating = false;
  }
}

// 更新字符级标注
function updateCharacters(item, characters) {
  if (!item) return;

  // 更新字符数组
  item.characters = characters;

  // 更新标注的reading字段，确保与字符级标注一致
  if (item.annotation && typeof item.annotation === 'object') {
    // 从字符级标注重新生成reading
    const reading = characters.map(char => char.reading || char.char).join('');
    item.annotation.reading = reading;
  }
}

// 保存标注
function saveAnnotations() {
  if (annotationItems.value.length === 0) {
    dialogVisible.value = false;
    return;
  }

  // 更新标注
  const annotations = {};

  annotationItems.value.forEach(item => {
    // 对于日语项，保存完整的标注对象（包含reading和characters）
    if (item.detectedLanguage === 'ja') {
      // 确保annotation是一个对象
      if (typeof item.annotation !== 'object' || item.annotation === null) {
        item.annotation = {
          reading: item.annotation || '',
          characters: item.characters || []
        };
      }

      // 确保characters存在，并且与item.characters保持同步
      if (item.characters) {
        // 始终使用最新的字符级标注
        item.annotation.characters = [...item.characters];

        // 从字符级标注重新生成reading
        const reading = item.characters.map(char => char.reading || char.char).join('');
        item.annotation.reading = reading;
      }

      annotations[item.id] = item.annotation;
    } else {
      // 对于非日语项，直接保存标注文本
      annotations[item.id] = item.annotation;
    }
  });

  // 更新节点参数
  const updatedParams = { ...props.params, annotations };
  emit('update:params', updatedParams);

  // 重新处理节点
  emit('process-node');

  // 关闭对话框
  dialogVisible.value = false;

  ElMessage.success('标注已保存');
}
</script>

<style scoped>
/* 预览区域样式 */
.preview-section {
  margin-top: 0.75rem;
  padding: 0.625rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-weight: bold;
  color: #606266;
}

.preview-info {
  text-align: center;
  font-size: 0.875rem;
  padding: 0.625rem;
  border-radius: 0.25rem;
  margin-top: 0.75rem;
}

/* 统一的预览背景颜色 */
.success-bg {
  color: #67c23a;
  background-color: #f0f9eb;
}

.warning-bg {
  color: #e6a23c;
  background-color: #fdf6ec;
}

.preview-progress {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.progress-bar {
  height: 0.5rem;
  background-color: #e4e7ed;
  border-radius: 0.25rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #67C23A;
  border-radius: 0.25rem;
  transition: width 0.3s ease;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.status-tag {
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

.status-tag.success {
  background-color: #f0f9eb;
  color: #67c23a;
  border: 0.0625rem solid #e1f3d8;
}

.status-tag.warning {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 0.0625rem solid #faecd8;
}

.status-tag.info {
  background-color: #f4f4f5;
  color: #909399;
  border: 0.0625rem solid #e9e9eb;
}

/* 对话框样式 */
.dialog-content {
  max-height: 100%;
  overflow-y: auto;
}

.dialog-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.dialog-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.annotations-table-container {
  max-height: 60vh;
  overflow-y: auto;
}

.original-text-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  max-height: 6.25rem;
  overflow-y: auto;
  word-break: break-all;
  white-space: pre-wrap;
  padding: 0.5rem;
  border-radius: 0.25rem;
  line-height: 1.5;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
}

.language-detection {
  display: flex;
  align-items: center;
  margin-top: 0.5rem;
  font-size: 0.75rem;
}

.language-label {
  margin-right: 0.5rem;
}

.empty-annotation {
  border-color: #E6A23C;
  background-color: rgba(230, 162, 60, 0.05);
}

.annotation-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.character-annotation-preview {
  width: 100%;
  min-height: 2rem;
  line-height: 1.5;
  font-size: 0.95rem;
  margin-bottom: 0.5rem;
}
</style>
