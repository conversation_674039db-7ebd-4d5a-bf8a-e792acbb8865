<!--
  语言选择引导组件
  引导用户选择学习语言
-->
<template>
  <el-dialog
    v-model="visible"
    title="选择学习语言"
    :width="isMobile ? '90%' : '32rem'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    class="language-guide-dialog"
  >
    <div class="language-guide">
      <div class="welcome-section">
        <div class="welcome-icon">🎯</div>
        <h2 class="welcome-title">欢迎来到 Echo Lab！</h2>
        <p class="guide-text">请选择你要学习的语言，我们将为你提供个性化的学习内容</p>
      </div>
      
      <div class="language-options">
        <div
          v-for="lang in SUPPORTED_LANGUAGES"
          :key="lang.value"
          class="language-option"
          :class="{ 'selected': selectedLanguage === lang.value }"
          @click="handleLanguageSelect(lang.value)"
        >
          <div class="language-flag">{{ getLanguageFlag(lang.value) }}</div>
          <div class="language-info">
            <div class="language-name">{{ lang.label }}</div>
            <div class="language-desc">{{ getLanguageDescription(lang.value) }}</div>
          </div>
          <div class="selection-indicator">
            <el-icon v-if="selectedLanguage === lang.value" class="check-icon">
              <i-ep-check />
            </el-icon>
          </div>
        </div>
      </div>
      
      <div class="guide-actions">
        <el-button 
          type="primary" 
          @click="handleNext" 
          :disabled="!selectedLanguage"
          size="large"
          class="next-button"
        >
          下一步
          <el-icon class="el-icon--right">
            <i-ep-arrow-right />
          </el-icon>
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue';
import { isMobileDevice } from '@/utils/deviceDetector';
import { SUPPORTED_LANGUAGES } from '@/config/languages';
import { getLanguageFlag } from '@/config/languageLevels';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  selectedLanguage: {
    type: String,
    default: null
  }
});

const emit = defineEmits(['update:modelValue', 'update:selectedLanguage', 'next']);

// 响应式状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const selectedLanguage = computed({
  get: () => props.selectedLanguage,
  set: (value) => emit('update:selectedLanguage', value)
});

// 设备检测
const isMobile = computed(() => isMobileDevice());

// 语言描述
const languageDescriptions = {
  ja: '学习日语，探索日本文化',
  en: '提升英语，连接世界',
  'zh-CN': '学习中文，了解中华文化',
  'zh-TW': '學習繁體中文，體驗台灣文化'
};

/**
 * 获取语言描述
 */
function getLanguageDescription(languageCode) {
  return languageDescriptions[languageCode] || '开始你的语言学习之旅';
}

/**
 * 处理语言选择
 */
function handleLanguageSelect(languageCode) {
  selectedLanguage.value = languageCode;
}

/**
 * 处理下一步
 */
function handleNext() {
  if (!selectedLanguage.value) {
    return;
  }
  emit('next');
}
</script>

<style scoped>
.language-guide {
  padding: 1rem 0;
}

.welcome-section {
  text-align: center;
  margin-bottom: 2rem;
}

.welcome-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.welcome-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 0.75rem 0;
}

.guide-text {
  color: #606266;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

.language-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 2rem;
}

.language-option {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 2px solid #e4e7ed;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
}

.language-option:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.language-option.selected {
  border-color: #409eff;
  background: #ecf5ff;
  box-shadow: 0 0 0 1px #409eff;
}

.language-flag {
  font-size: 2rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.language-info {
  flex: 1;
}

.language-name {
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
  margin-bottom: 0.25rem;
}

.language-desc {
  font-size: 0.75rem;
  color: #909399;
}

.selection-indicator {
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.check-icon {
  color: #409eff;
  font-size: 1.25rem;
}

.guide-actions {
  display: flex;
  justify-content: center;
}

.next-button {
  min-width: 8rem;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .language-guide {
    padding: 0.5rem 0;
  }

  .welcome-title {
    font-size: 1.25rem;
  }

  .language-option {
    padding: 0.875rem;
  }

  .language-flag {
    font-size: 1.75rem;
    margin-right: 0.75rem;
  }

  .language-name {
    font-size: 0.875rem;
  }

  .language-desc {
    font-size: 0.6875rem;
  }
}

/* 对话框样式 */
:deep(.language-guide-dialog .el-dialog__body) {
  padding: 1.5rem;
}

:deep(.language-guide-dialog .el-dialog__header) {
  padding: 1.5rem 1.5rem 0;
}

:deep(.language-guide-dialog .el-dialog__title) {
  font-size: 1.25rem;
  font-weight: 600;
}
</style>
