<!--
  节点属性面板组件
  用于显示和编辑节点属性
-->
<template>
  <div class="node-properties" v-if="selectedNode">
    <div class="properties-header">
      <h3>{{ nodeTitle }}</h3>
      <div class="node-type">{{ nodeTypeLabel }}</div>
    </div>

    <div class="properties-content">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本属性" name="basic">
          <div class="property-group">
            <div class="property-item">
              <div class="property-label">节点ID</div>
              <div class="property-value id-value">{{ selectedNode.id }}</div>
            </div>

            <div class="property-item">
              <div class="property-label">节点名称</div>
              <div class="property-value">
                <el-input v-model="customName" placeholder="自定义节点名称" @change="updateCustomName" />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="连接" name="connections">
          <div class="property-group">
            <div class="property-label">输入源</div>
            <div class="property-value">
              <div v-if="sourceNodes.length === 0" class="empty-state">
                无输入源
              </div>
              <div v-else class="connection-list">
                <div v-for="sourceNode in sourceNodes" :key="sourceNode.id" class="connection-item">
                  <span>{{ getNodeLabel(sourceNode) }}</span>
                  <el-button type="danger" size="small" circle @click="disconnectNode(sourceNode.id)">
                    <el-icon>
                      <i-ep-delete />
                    </el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <div class="property-group">
            <div class="property-label">添加输入源</div>
            <div class="property-value">
              <div class="add-connection">
                <el-select v-model="newSourceNodeId" placeholder="选择节点" filterable>
                  <el-option v-for="node in availableSourceNodes" :key="node.id" :label="getNodeLabel(node)"
                    :value="node.id" />
                </el-select>
                <el-button type="primary" @click="connectNode" :disabled="!newSourceNodeId">添加</el-button>
              </div>
            </div>
          </div>

          <div class="property-group">
            <div class="property-label">目标节点</div>
            <div class="property-value">
              <div v-if="targetNodes.length === 0" class="empty-state">
                无目标节点
              </div>
              <div v-else class="connection-list">
                <div v-for="targetNode in targetNodes" :key="targetNode.id" class="connection-item">
                  <span>{{ getNodeLabel(targetNode) }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="结果" name="preview">
          <div class="preview-container">
            <div v-if="nodeResult" class="result-display">
              <div class="result-header">
                <div class="result-title">处理结果</div>
                <div class="result-actions">
                  <el-button size="small" type="primary" @click="copyFullResult">
                    <el-icon class="el-icon--left">
                      <i-ep-copy-document />
                    </el-icon>复制完整JSON
                  </el-button>
                  <el-tag size="small" type="success" v-if="!nodeResult.isEmpty">有效</el-tag>
                  <el-tag size="small" type="warning" v-else>空</el-tag>
                </div>
              </div>

              <div class="result-content">
                <pre class="result-json">{{ formatResult(nodeResult) }}</pre>
              </div>
            </div>
            <div v-else class="empty-state">
              无处理结果，请确保节点配置正确并有有效的输入源
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
  <div v-else class="no-selection">
    <div class="empty-state-large">
      <el-icon><i-ep-select /></el-icon>
      <p>请选择一个节点</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { useNodeStore } from '@/core/stores/nodeStore';
import nodeFactory from '@/core/factories/NodeFactory';
import { ElMessage } from 'element-plus';

const props = defineProps({
  selectedNodeId: {
    type: String,
    default: null
  }
});

const emit = defineEmits(['node-updated']);

const nodeStore = useNodeStore();
const activeTab = ref('basic');
const customName = ref('');
const newSourceNodeId = ref('');

// 选中的节点
const selectedNode = computed(() => {
  if (!props.selectedNodeId) return null;
  return nodeStore.getNode(props.selectedNodeId);
});

// 节点标题
const nodeTitle = computed(() => {
  if (!selectedNode.value) return '';

  if (selectedNode.value.customName) {
    return selectedNode.value.customName;
  }

  const typeLabel = nodeFactory.getNodeTypeConfig(selectedNode.value.type)?.label || selectedNode.value.type;
  return `${typeLabel} #${selectedNode.value.number || 1}`;
});

// 节点类型标签
const nodeTypeLabel = computed(() => {
  if (!selectedNode.value) return '';
  return nodeFactory.getNodeTypeConfig(selectedNode.value.type)?.label || selectedNode.value.type;
});

// 源节点
const sourceNodes = computed(() => {
  if (!selectedNode.value) return [];
  return nodeStore.getNodeSources(props.selectedNodeId);
});

// 目标节点
const targetNodes = computed(() => {
  if (!selectedNode.value) return [];
  return nodeStore.getNodeTargets(props.selectedNodeId);
});

// 可用的源节点
const availableSourceNodes = computed(() => {
  if (!selectedNode.value) return [];

  // 获取所有节点
  const allNodes = nodeStore.getAllNodes;

  // 过滤掉已经是源节点的节点和当前选中的节点
  return allNodes.filter(node => {
    // 排除当前选中的节点
    if (node.id === props.selectedNodeId) return false;

    // 排除已经是源节点的节点
    if (selectedNode.value.sourceIds.includes(node.id)) return false;

    // 排除会形成循环依赖的节点
    if (nodeStore.wouldFormCycle(node.id, props.selectedNodeId)) return false;

    // 检查连接是否有效
    return nodeFactory.isValidConnection(node.type, selectedNode.value.type);
  });
});

// 节点处理结果
const nodeResult = computed(() => {
  if (!selectedNode.value) return null;
  return nodeStore.getNodeResult(props.selectedNodeId);
});

// 获取节点标签
function getNodeLabel(node) {
  if (!node) return '';

  if (node.customName) {
    return node.customName;
  }

  const typeLabel = nodeFactory.getNodeTypeConfig(node.type)?.label || node.type;
  return `${typeLabel} #${node.number || 1}`;
}

// 更新自定义名称
function updateCustomName() {
  if (!selectedNode.value) return;

  nodeStore.updateNodeCustomName(props.selectedNodeId, customName.value);
  emit('node-updated', props.selectedNodeId);

  ElMessage.success('节点名称已更新');
}

// 连接节点
function connectNode() {
  if (!selectedNode.value || !newSourceNodeId.value) return;

  const result = nodeStore.connectNodes(newSourceNodeId.value, props.selectedNodeId);

  if (result) {
    ElMessage.success('节点已连接');
    newSourceNodeId.value = '';
    emit('node-updated', props.selectedNodeId);
  } else {
    ElMessage.error('无法连接节点');
  }
}

// 断开节点连接
function disconnectNode(sourceId) {
  if (!selectedNode.value) return;

  const result = nodeStore.disconnectNodes(sourceId, props.selectedNodeId);

  if (result) {
    ElMessage.success('节点连接已断开');
    emit('node-updated', props.selectedNodeId);
  } else {
    ElMessage.error('无法断开节点连接');
  }
}

// 复制完整JSON结果
function copyFullResult() {
  if (!nodeResult.value) {
    ElMessage.warning('没有可复制的结果');
    return;
  }

  try {
    // 将完整结果转换为JSON字符串
    const fullResultJson = JSON.stringify(nodeResult.value, null, 2);

    // 复制到剪贴板
    navigator.clipboard.writeText(fullResultJson)
      .then(() => {
        ElMessage.success('完整JSON已复制到剪贴板');
      })
      .catch(err => {
        console.error('复制失败:', err);
        ElMessage.error('复制失败，请手动复制');
      });
  } catch (error) {
    console.error('准备复制数据失败:', error);
    ElMessage.error('复制失败，请手动复制');
  }
}

// 格式化结果
function formatResult(result) {
  if (!result) return '';

  try {
    // 创建一个简化版本的结果，移除过长的内容
    const simplifiedResult = { ...result };

    // 处理不同类型的结果 - 不再截断文本内容
    // 保持文本内容完整显示

    // 分句节点结果 - 显示更多项目
    if (simplifiedResult.segments && simplifiedResult.segments.length > 10) {
      // 保留前10个项目
      const displayedSegments = simplifiedResult.segments.slice(0, 10);

      // 确保每个分句项目都显示完整内容（创建副本以避免修改原始数据）
      const processedSegments = displayedSegments.map(segment => {
        // 创建分句的副本，避免修改原始数据
        const segmentCopy = { ...segment };

        // 检查内容是否包含 "source": "unknown" 或类似字符串
        if (segmentCopy.content && typeof segmentCopy.content === 'string') {
          // 如果内容看起来像JSON字符串的一部分，尝试清理它
          if (segmentCopy.content.includes('source') && segmentCopy.content.includes('unknown')) {
            segmentCopy.content = segmentCopy.content.replace(/["']source["']\s*:\s*["']unknown["']/g, '');
            segmentCopy.content = segmentCopy.content.trim();
          }

          // 不再截断内容，保持完整显示
          // if (segmentCopy.content.length > 50) {
          //   segmentCopy.content = segmentCopy.content.substring(0, 50) + '...';
          // }
        }

        // 完全移除源信息，因为对用户没有意义
        if (segmentCopy.source) {
          delete segmentCopy.source; // 从JSON中删除原始source字段
        }

        return segmentCopy;
      });

      simplifiedResult.segments = [
        ...processedSegments,
        { note: `还有 ${simplifiedResult.segments.length - 10} 项...` }
      ];
    } else if (simplifiedResult.segments) {
      // 如果分句数量不多，显示所有分句但仍然处理每个分句（创建副本以避免修改原始数据）
      simplifiedResult.segments = simplifiedResult.segments.map(segment => {
        // 创建分句的副本，避免修改原始数据
        const segmentCopy = { ...segment };

        // 检查内容是否包含 "source": "unknown" 或类似字符串
        if (segmentCopy.content && typeof segmentCopy.content === 'string') {
          // 如果内容看起来像JSON字符串的一部分，尝试清理它
          if (segmentCopy.content.includes('source') && segmentCopy.content.includes('unknown')) {
            segmentCopy.content = segmentCopy.content.replace(/["']source["']\s*:\s*["']unknown["']/g, '');
            segmentCopy.content = segmentCopy.content.trim();
          }

          // 不再截断内容，保持完整显示
          // if (segmentCopy.content.length > 50) {
          //   segmentCopy.content = segmentCopy.content.substring(0, 50) + '...';
          // }
        }

        // 完全移除源信息，因为对用户没有意义
        if (segmentCopy.source) {
          delete segmentCopy.source; // 从JSON中删除原始source字段
        }

        return segmentCopy;
      });
    }

    // 标注节点结果 - 显示标注内容
    if (simplifiedResult.annotations && simplifiedResult.annotations.length > 0) {
      // 创建一个更友好的显示格式
      const formattedAnnotations = [];

      // 只显示前5个标注项
      const displayCount = Math.min(5, simplifiedResult.annotations.length);

      for (let i = 0; i < displayCount; i++) {
        const annotation = simplifiedResult.annotations[i];
        let content = annotation.content || '';
        if (content.length > 30) {
          content = content.substring(0, 30) + '...';
        }

        formattedAnnotations.push({
          id: annotation.id,
          content: content,
          annotation: annotation.annotation || '(未标注)',
          language: annotation.language || 'auto'
        });
      }

      // 如果有更多项，添加提示
      if (simplifiedResult.annotations.length > displayCount) {
        formattedAnnotations.push({
          note: `还有 ${simplifiedResult.annotations.length - displayCount} 项...`
        });
      }

      simplifiedResult.annotations = formattedAnnotations;

      // 确保结果不为空
      simplifiedResult.isEmpty = false;
    }

    // 翻译节点结果 - 显示翻译内容
    if (simplifiedResult.translations) {
      const formattedTranslations = {};
      const targets = Object.keys(simplifiedResult.translations);

      if (targets.length > 0) {
        targets.forEach(target => {
          const translations = simplifiedResult.translations[target];
          const keys = Object.keys(translations);

          // 为每个目标语言创建一个更友好的显示格式
          const formattedItems = [];

          // 只显示前3个翻译项
          const displayCount = Math.min(3, keys.length);

          for (let i = 0; i < displayCount; i++) {
            const key = keys[i];
            const translation = translations[key];

            // 获取原文内容
            let originalText = '';
            if (simplifiedResult.sourceSegments) {
              const segment = simplifiedResult.sourceSegments.find(s => s.id === key);
              if (segment) {
                originalText = segment.content;
                if (originalText.length > 30) {
                  originalText = originalText.substring(0, 30) + '...';
                }
              }
            } else if (simplifiedResult.sourceContent && key === simplifiedResult.sourceId) {
              originalText = simplifiedResult.sourceContent;
              if (originalText.length > 30) {
                originalText = originalText.substring(0, 30) + '...';
              }
            }

            formattedItems.push({
              id: key,
              original: originalText,
              translation: translation || '(未翻译)'
            });
          }

          // 如果有更多项，添加提示
          if (keys.length > displayCount) {
            formattedItems.push({
              note: `还有 ${keys.length - displayCount} 项...`
            });
          }

          formattedTranslations[target] = formattedItems;
        });

        simplifiedResult.translations = formattedTranslations;

        // 确保结果不为空
        simplifiedResult.isEmpty = false;
      }
    }

    if (simplifiedResult.sequence && simplifiedResult.sequence.length > 3) {
      simplifiedResult.sequence = [
        ...simplifiedResult.sequence.slice(0, 3),
        { note: `还有 ${simplifiedResult.sequence.length - 3} 项...` }
      ];
    }

    if (simplifiedResult.audioItems && simplifiedResult.audioItems.length > 3) {
      simplifiedResult.audioItems = [
        ...simplifiedResult.audioItems.slice(0, 3),
        { note: `还有 ${simplifiedResult.audioItems.length - 3} 项...` }
      ];
    }

    if (simplifiedResult.steps && simplifiedResult.steps.length > 3) {
      simplifiedResult.steps = [
        ...simplifiedResult.steps.slice(0, 3),
        { note: `还有 ${simplifiedResult.steps.length - 3} 项...` }
      ];
    }

    return JSON.stringify(simplifiedResult, null, 2);
  } catch (error) {
    console.error('格式化结果失败:', error);
    return JSON.stringify(result);
  }
}

// 监听选中节点变化
watch(() => props.selectedNodeId, () => {
  if (selectedNode.value) {
    customName.value = selectedNode.value.customName || '';
    newSourceNodeId.value = '';
    activeTab.value = 'basic';
  }
});

// 初始化
if (selectedNode.value) {
  customName.value = selectedNode.value.customName || '';
}
</script>

<style scoped>
.node-properties {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-left: 0.0625rem solid #e4e7ed;
}

.properties-header {
  padding: 1rem;
  border-bottom: 0.0625rem solid #e4e7ed;
}

.properties-header h3 {
  margin: 0;
  font-size: 1.125rem;
  color: #303133;
}

.node-type {
  font-size: 0.875rem;
  color: #909399;
  margin-top: 0.25rem;
}

.properties-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

/* 修复tab选中样式 */
:deep(.el-tabs__item) {
  outline: none;
  box-shadow: none;
  border: none;
}

:deep(.el-tabs__item.is-active) {
  outline: none;
  box-shadow: none;
  border: none;
}

:deep(.el-tabs__item:focus) {
  outline: none;
  box-shadow: none;
  border: none;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-tabs__active-bar) {
  height: 2px;
}

.property-group {
  margin-bottom: 1.5rem;
}

.property-item {
  margin-bottom: 1rem;
}

.property-label {
  font-size: 0.875rem;
  color: #606266;
  margin-bottom: 0.5rem;
}

.property-value {
  width: 100%;
}

.id-value {
  font-family: monospace;
  background-color: #f5f7fa;
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #606266;
  word-break: break-all;
}

.empty-state {
  color: #909399;
  font-style: italic;
  padding: 0.5rem;
  background-color: #f5f7fa;
  border-radius: 0.25rem;
  text-align: center;
}

.connection-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.connection-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: #f5f7fa;
  border-radius: 0.25rem;
}

.add-connection {
  display: flex;
  gap: 0.5rem;
}

.preview-container {
  min-height: 12.5rem;
}

.result-display {
  border: 0.0625rem solid #e4e7ed;
  border-radius: 0.25rem;
  overflow: hidden;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background-color: #f5f7fa;
  border-bottom: 0.0625rem solid #e4e7ed;
}

.result-title {
  font-weight: bold;
  color: #606266;
}

.result-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.result-content {
  padding: 0.75rem;
  max-height: 25rem;
  overflow-y: auto;
}

.result-json {
  margin: 0;
  font-family: monospace;
  font-size: 0.875rem;
  white-space: pre-wrap;
  word-break: break-all;
}

.no-selection {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
}

.empty-state-large {
  text-align: center;
  color: #909399;
}

.empty-state-large .el-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-state-large p {
  font-size: 1.125rem;
  margin: 0;
}
</style>
