# Echo Lab UI 组件规范

本文档定义了 Echo Lab 项目中各类 UI 组件的设计规范，以确保整个应用的一致性和用户体验。

## 设计原则

Echo Lab 的 UI 设计遵循以下原则：

1. **一致性**：保持整个应用的视觉和交互一致性
2. **简洁性**：简洁明了的界面，避免不必要的视觉元素
3. **可用性**：易于使用和理解的界面
4. **响应性**：适应不同屏幕尺寸的响应式设计
5. **可访问性**：考虑不同用户的需求，包括残障用户

## 颜色系统

Echo Lab 使用以下颜色系统：

### 主要颜色

- **主色**：#409EFF（Element Plus 默认主色）
- **成功色**：#67C23A
- **警告色**：#E6A23C
- **危险色**：#F56C6C
- **信息色**：#909399

### 中性色

- **主要文本**：#303133
- **常规文本**：#606266
- **次要文本**：#909399
- **占位文本**：#C0C4CC

### 边框颜色

- **基础边框**：#DCDFE6
- **浅色边框**：#E4E7ED
- **较浅边框**：#EBEEF5
- **最浅边框**：#F2F6FC

### 背景颜色

- **基础背景**：#FFFFFF
- **页面背景**：#F2F3F5
- **组件背景**：#F8F9FA
- **悬浮背景**：#ECEFF1

## 排版

Echo Lab 使用以下排版规范：

### 字体

- **主要字体**：系统默认字体栈
  ```css
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  ```

### 字号

- **主标题**：1.5rem (24px)
- **次标题**：1.25rem (20px)
- **小标题**：1.125rem (18px)
- **正文**：1rem (16px)
- **小字**：0.875rem (14px)
- **最小字**：0.75rem (12px)

### 行高

- **紧凑**：1.2
- **常规**：1.5
- **宽松**：1.8

## 间距系统

Echo Lab 使用基于 rem 的间距系统：

- **超小间距**：0.25rem (4px)
- **小间距**：0.5rem (8px)
- **中小间距**：0.75rem (12px)
- **中间距**：1rem (16px)
- **中大间距**：1.5rem (24px)
- **大间距**：2rem (32px)
- **超大间距**：3rem (48px)

## 组件规范

### 节点预览区域

节点预览区域是节点内部用于展示处理结果的区域，应遵循以下规范：

#### 布局与样式

1. **预览区域容器**
   - 顶部外边距: 0.75rem
   - 内边距: 0.625rem
   - 背景色: #f8f9fa
   - 边框圆角: 0.25rem

2. **预览区域标题栏**
   - 使用 flex 布局，两端对齐
   - 标题文字靠左，操作按钮靠右
   - 底部外边距: 0.75rem
   - 字体粗细: bold
   - 文字颜色: #606266

3. **预览内容区域**
   - 成功状态背景色: #f0f9eb
   - 成功状态文字颜色: #67c23a
   - 空状态背景色: #f5f7fa
   - 空状态文字颜色: #909399
   - 文字大小: 0.875rem
   - 内边距: 0.625rem
   - 边框圆角: 0.25rem
   - 顶部外边距: 0.75rem
   - 文字居中对齐

#### 按钮规范

1. **操作按钮**
   - 尺寸: small
   - 类型: primary
   - 位置: 标题栏右侧
   - 包含图标和文字

2. **按钮图标**
   - 使用 Element Plus 图标库
   - 图标位于文字左侧

#### 示例代码

```vue
<div class="preview-section">
  <div class="preview-header">
    <div>预览标题：</div>
    <el-button size="small" type="primary" @click="handleAction">
      <el-icon class="el-icon--left">
        <Document />
      </el-icon>
      操作按钮
    </el-button>
  </div>

  <div v-if="hasContent" class="preview-info success-bg">
    已生成 {{ contentCount }} 个项目
  </div>
  <div v-else class="preview-empty">
    点击按钮添加内容
  </div>
</div>
```

#### CSS 样式

```css
.preview-section {
  margin-top: 0.75rem;
  padding: 0.625rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-weight: bold;
  color: #606266;
}

.preview-info {
  text-align: center;
  font-size: 0.875rem;
  padding: 0.625rem;
  border-radius: 0.25rem;
  margin-top: 0.75rem;
}

.success-bg {
  color: #67c23a;
  background-color: #f0f9eb;
}

.preview-empty {
  text-align: center;
  color: #909399;
  font-size: 0.875rem;
  padding: 0.625rem;
  background-color: #f5f7fa;
  border-radius: 0.25rem;
  margin-top: 0.75rem;
}
```

### 表单组件

表单组件用于用户输入和配置，应遵循以下规范：

#### 布局与样式

1. **表单容器**
   - 内边距: 1rem
   - 背景色: #ffffff
   - 边框: 1px solid #dcdfe6
   - 边框圆角: 0.25rem
   - 阴影: 0 2px 12px 0 rgba(0, 0, 0, 0.1)

2. **表单项**
   - 底部外边距: 1rem
   - 标签宽度: 6rem
   - 标签位置: 顶部对齐

3. **表单控件**
   - 宽度: 100%
   - 高度: 2.5rem (对于输入框)
   - 内边距: 0 0.75rem
   - 边框: 1px solid #dcdfe6
   - 边框圆角: 0.25rem
   - 聚焦边框颜色: #409eff

#### 响应式设计

1. **小屏幕**
   - 标签宽度: 100%
   - 标签位置: 顶部
   - 控件宽度: 100%

2. **大屏幕**
   - 标签宽度: 6rem
   - 标签位置: 左侧
   - 控件宽度: calc(100% - 6rem)

### 对话框组件

对话框组件用于显示临时内容和交互，应遵循以下规范：

#### 布局与样式

1. **对话框容器**
   - 宽度: 30rem (最大宽度: 80%)
   - 背景色: #ffffff
   - 边框圆角: 0.25rem
   - 阴影: 0 2px 12px 0 rgba(0, 0, 0, 0.1)
   - 居中显示

2. **对话框标题**
   - 内边距: 1rem
   - 边框底: 1px solid #dcdfe6
   - 字体大小: 1.125rem
   - 字体粗细: bold
   - 文字颜色: #303133

3. **对话框内容**
   - 内边距: 1rem
   - 最大高度: 60vh
   - 溢出处理: auto

4. **对话框底部**
   - 内边距: 1rem
   - 边框顶: 1px solid #dcdfe6
   - 文本对齐: 右对齐

#### 移动端适配

1. **移动端对话框**
   - 宽度: 90%
   - 最大高度: 80vh
   - 底部按钮: 垂直排列

## 响应式设计断点

Echo Lab 使用以下响应式设计断点：

- **超小屏幕**：< 576px
- **小屏幕**：≥ 576px
- **中等屏幕**：≥ 768px
- **大屏幕**：≥ 992px
- **超大屏幕**：≥ 1200px

## 图标使用

Echo Lab 使用 Element Plus 图标库，应遵循以下规范：

1. **图标大小**
   - 小图标: 1rem (16px)
   - 中图标: 1.25rem (20px)
   - 大图标: 1.5rem (24px)

2. **图标颜色**
   - 主要图标: 主色 (#409EFF)
   - 次要图标: 常规文本色 (#606266)
   - 禁用图标: 占位文本色 (#C0C4CC)

3. **图标间距**
   - 图标与文本间距: 0.25rem (4px)

## 动画与过渡

Echo Lab 使用以下动画与过渡规范：

1. **过渡时长**
   - 快速过渡: 0.15s
   - 常规过渡: 0.3s
   - 慢速过渡: 0.5s

2. **过渡曲线**
   - 常规过渡: ease-in-out
   - 弹性过渡: cubic-bezier(0.34, 1.56, 0.64, 1)

3. **常用过渡**
   - 淡入淡出: opacity
   - 缩放: transform: scale()
   - 滑动: transform: translate()

## 最佳实践

1. **组件复用**
   - 将常用的 UI 模式抽象为可复用的组件
   - 使用 props 和插槽使组件具有灵活性

2. **响应式设计**
   - 使用相对单位 (rem) 而不是绝对单位 (px)
   - 使用媒体查询适配不同屏幕尺寸
   - 测试不同设备上的显示效果

3. **可访问性**
   - 提供足够的颜色对比度
   - 使用语义化 HTML 元素
   - 添加适当的 ARIA 属性
   - 确保键盘可访问性
