/**
 * 管理员中间件
 * 用于保护管理API路由
 */
const authService = require("../services/authService");
const db = require("../models");
const adminLogService = require("../services/adminLogService");

/**
 * 验证用户是否为管理员
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 * @param {Function} next 下一个中间件
 */
async function adminAuth(req, res, next) {
  try {
    // 从请求头中获取令牌
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      console.log("[AdminMiddleware] adminAuth: 未提供令牌");
      return res.status(401).json({
        success: false,
        error: "未授权，请先登录",
      });
    }

    // 提取令牌
    const token = authHeader.split(" ")[1];
    console.log("[AdminMiddleware] adminAuth: 提取到令牌");

    // 验证令牌 (异步调用)
    const result = await authService.verifyToken(token);

    if (!result.success) {
      console.log(`[AdminMiddleware] adminAuth: 令牌验证失败: ${result.error}`);
      return res.status(401).json({
        success: false,
        error: result.error,
        code: result.code || "TOKEN_INVALID",
      });
    }

    // 检查用户角色
    if (result.decoded.role !== "admin") {
      console.log(
        `[AdminMiddleware] adminAuth: 用户角色不是管理员: ${result.decoded.role}`
      );
      return res.status(403).json({
        success: false,
        error: "权限不足，需要管理员权限",
      });
    }

    console.log(
      `[AdminMiddleware] adminAuth: 管理员验证成功，用户ID: ${result.decoded.id}`
    );

    // 将用户信息添加到请求对象
    req.admin = result.decoded;
    req.user = result.decoded; // 同时添加到 req.user，以便兼容其他中间件
    next();
  } catch (error) {
    console.error("[AdminMiddleware] 管理员认证中间件错误:", error);
    return res.status(500).json({
      success: false,
      error: "服务器内部错误",
    });
  }
}

/**
 * 记录管理员操作日志
 * @param {string} action 操作类型
 * @param {Object} options 选项
 * @param {string} options.targetType 操作目标类型
 * @param {string} options.targetIdParam 操作目标ID参数名
 * @param {Function} options.getDetails 获取操作详情的函数
 * @param {Function} options.getBeforeData 获取操作前数据的函数
 * @param {Function} options.getAfterData 获取操作后数据的函数
 * @returns {Function} 中间件函数
 */
function logAdminAction(action, options = {}) {
  const { targetType, targetIdParam, getDetails, getBeforeData, getAfterData } =
    options;

  return async (req, res, next) => {
    // 保存原始的 res.json 方法
    const originalJson = res.json;

    // 获取操作前数据
    let beforeData = null;
    if (getBeforeData) {
      try {
        beforeData = await getBeforeData(req);
      } catch (error) {
        console.error("获取操作前数据失败:", error);
      }
    }

    // 重写 res.json 方法
    res.json = async function (data) {
      // 恢复原始的 res.json 方法
      res.json = originalJson;

      // 获取操作后数据
      let afterData = null;
      if (getAfterData) {
        try {
          afterData = await getAfterData(req, data);
        } catch (error) {
          console.error("获取操作后数据失败:", error);
        }
      }

      // 获取操作详情
      let details = null;
      if (getDetails) {
        try {
          details = getDetails(req, data);
        } catch (error) {
          console.error("获取操作详情失败:", error);
        }
      }

      // 获取目标ID
      let targetId = null;
      if (targetIdParam) {
        targetId = req.params[targetIdParam] || req.body[targetIdParam] || null;
      }

      // 获取客户端IP
      const ipAddress =
        req.headers["x-forwarded-for"] || req.connection.remoteAddress;

      // 记录操作日志
      await adminLogService.logAdminAction({
        adminId: req.admin.id,
        adminEmail: req.admin.email,
        action,
        targetType: targetType || null,
        targetId,
        details,
        beforeData,
        afterData,
        ipAddress,
        result: data.success ? "success" : "failure",
        message: data.message || data.error || null,
      });

      // 调用原始的 res.json 方法
      return originalJson.call(this, data);
    };

    next();
  };
}

/**
 * 只允许管理员访问
 * 与 adminAuth 不同，此中间件依赖于 authMiddleware 已经验证过的用户信息
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 * @param {Function} next - 下一个中间件函数
 */
function adminOnly(req, res, next) {
  // 确保用户已认证
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: "未授权访问，请先登录",
    });
  }

  // 检查用户是否是管理员
  if (req.user.role !== "admin") {
    return res.status(403).json({
      success: false,
      error: "权限不足，只有管理员可以访问此功能",
    });
  }

  // 用户是管理员，允许访问
  next();
}

module.exports = {
  adminAuth,
  logAdminAction,
  adminOnly,
};
