# 前端服务 API

本文档详细说明了 Echo Lab 前端的服务 API，包括 API 服务、TTS 服务、翻译服务和存储服务。

## API 服务

API 服务负责与后端 API 进行通信，封装了 HTTP 请求方法。

### 基本用法

```javascript
import apiService from '@/services/api';

// GET 请求
const data = await apiService.get('/content');

// POST 请求
const result = await apiService.post('/content', { title: '标题', description: '描述' });

// PUT 请求
const updated = await apiService.put('/content/1', { title: '新标题' });

// DELETE 请求
const deleted = await apiService.delete('/content/1');
```

### API 方法

#### get

发送 GET 请求。

```javascript
async function get(url, params = {})
```

**参数**:
- `url`: API 路径（不包含基础 URL）
- `params`: 查询参数对象

**返回值**:
- 返回响应数据

#### post

发送 POST 请求。

```javascript
async function post(url, data = {})
```

**参数**:
- `url`: API 路径（不包含基础 URL）
- `data`: 请求体数据

**返回值**:
- 返回响应数据

#### put

发送 PUT 请求。

```javascript
async function put(url, data = {})
```

**参数**:
- `url`: API 路径（不包含基础 URL）
- `data`: 请求体数据

**返回值**:
- 返回响应数据

#### delete

发送 DELETE 请求。

```javascript
async function delete(url)
```

**参数**:
- `url`: API 路径（不包含基础 URL）

**返回值**:
- 返回响应数据

### 错误处理

API 服务会自动处理 HTTP 错误，并将错误信息转换为可读格式。

```javascript
function handleResponse(response) {
  if (!response.ok) {
    // 处理错误
    if (response.status === 401) {
      // 未授权，重定向到登录页面
      router.push('/login');
    }
    
    // 尝试解析错误信息
    return response.json().then(data => {
      throw new Error(data.message || '请求失败');
    }).catch(() => {
      throw new Error(`请求失败: ${response.status}`);
    });
  }
  
  // 返回 JSON 数据
  return response.json();
}
```

### 认证处理

API 服务会自动在请求中添加认证令牌。

```javascript
function getAuthHeaders() {
  const token = localStorage.getItem('token');
  
  if (token) {
    return {
      'Authorization': `Bearer ${token}`
    };
  }
  
  return {};
}
```

## 内容服务

内容服务负责管理内容数据，包括创建、获取、更新和删除内容。

### 基本用法

```javascript
import contentService from '@/services/content';

// 获取内容列表
const contentList = await contentService.getContentList();

// 获取内容详情
const content = await contentService.getContentById(1);

// 创建内容
const newContent = await contentService.createContent({
  title: '标题',
  description: '描述',
  data: {}
});

// 更新内容
const updatedContent = await contentService.updateContent(1, {
  title: '新标题',
  description: '新描述',
  data: {}
});

// 删除内容
await contentService.deleteContent(1);
```

### 内容方法

#### getContentList

获取内容列表。

```javascript
async function getContentList()
```

**返回值**:
- 返回内容列表数组

#### getContentById

获取内容详情。

```javascript
async function getContentById(id)
```

**参数**:
- `id`: 内容 ID

**返回值**:
- 返回内容详情对象

#### createContent

创建内容。

```javascript
async function createContent(content)
```

**参数**:
- `content`: 内容对象，包含 title、description 和 data

**返回值**:
- 返回创建的内容对象

#### updateContent

更新内容。

```javascript
async function updateContent(id, content)
```

**参数**:
- `id`: 内容 ID
- `content`: 内容对象，包含要更新的字段

**返回值**:
- 返回更新后的内容对象

#### deleteContent

删除内容。

```javascript
async function deleteContent(id)
```

**参数**:
- `id`: 内容 ID

**返回值**:
- 返回删除结果

## 资源服务

资源服务负责管理资源数据，包括音频、翻译和标注。

### 基本用法

```javascript
import resourceService from '@/services/resource';

// 生成音频
const audio = await resourceService.generateAudio('こんにちは', 'ja');

// 生成翻译
const translation = await resourceService.generateTranslation('こんにちは', 'ja', 'zh-CN');

// 生成标注
const annotation = await resourceService.generateAnnotation('今日は良い天気ですね', 'ja', 'furigana');

// 上传文件
const file = event.target.files[0];
const url = await resourceService.uploadFile(file, 'image');
```

### 资源方法

#### generateAudio

生成音频。

```javascript
async function generateAudio(text, language, ignoreCache = false)
```

**参数**:
- `text`: 要转换为语音的文本
- `language`: 语言代码
- `ignoreCache`: 是否忽略缓存

**返回值**:
- 返回音频 URL 和时长

#### generateTranslation

生成翻译。

```javascript
async function generateTranslation(text, sourceLanguage, targetLanguage, ignoreCache = false)
```

**参数**:
- `text`: 要翻译的文本
- `sourceLanguage`: 源语言代码
- `targetLanguage`: 目标语言代码
- `ignoreCache`: 是否忽略缓存

**返回值**:
- 返回翻译文本

#### generateAnnotation

生成标注。

```javascript
async function generateAnnotation(text, language, method, ignoreCache = false)
```

**参数**:
- `text`: 要标注的文本
- `language`: 语言代码
- `method`: 标注方法
- `ignoreCache`: 是否忽略缓存

**返回值**:
- 返回标注文本

#### uploadFile

上传文件。

```javascript
async function uploadFile(file, type)
```

**参数**:
- `file`: 文件对象
- `type`: 文件类型

**返回值**:
- 返回文件 URL

## 用户服务

用户服务负责用户认证和管理。

### 基本用法

```javascript
import userService from '@/services/user';

// 用户注册
const user = await userService.register('username', '<EMAIL>', 'password');

// 用户登录
const loggedInUser = await userService.login('username', 'password');

// 获取用户信息
const profile = await userService.getProfile();

// 检查是否已登录
const isLoggedIn = userService.isLoggedIn();

// 用户登出
userService.logout();
```

### 用户方法

#### register

用户注册。

```javascript
async function register(username, email, password)
```

**参数**:
- `username`: 用户名
- `email`: 邮箱地址
- `password`: 密码

**返回值**:
- 返回用户信息和令牌

#### login

用户登录。

```javascript
async function login(username, password)
```

**参数**:
- `username`: 用户名
- `password`: 密码

**返回值**:
- 返回用户信息和令牌

#### getProfile

获取用户信息。

```javascript
async function getProfile()
```

**返回值**:
- 返回用户信息

#### isLoggedIn

检查是否已登录。

```javascript
function isLoggedIn()
```

**返回值**:
- 返回布尔值，表示是否已登录

#### logout

用户登出。

```javascript
function logout()
```

## 反馈服务

反馈服务负责用户反馈的管理。

### 基本用法

```javascript
import feedbackService from '@/services/feedback';

// 创建反馈
const feedback = await feedbackService.createFeedback('反馈内容', '<EMAIL>');

// 获取用户反馈列表
const feedbackList = await feedbackService.getFeedbackList();

// 获取所有反馈（管理员）
const allFeedback = await feedbackService.getAllFeedback();

// 更新反馈状态（管理员）
const updatedFeedback = await feedbackService.updateFeedbackStatus(1, 'completed');
```

### 反馈方法

#### createFeedback

创建反馈。

```javascript
async function createFeedback(content, email)
```

**参数**:
- `content`: 反馈内容
- `email`: 联系邮箱（可选）

**返回值**:
- 返回创建的反馈对象

#### getFeedbackList

获取用户反馈列表。

```javascript
async function getFeedbackList()
```

**返回值**:
- 返回反馈列表数组

#### getAllFeedback

获取所有反馈（管理员）。

```javascript
async function getAllFeedback()
```

**返回值**:
- 返回所有反馈列表数组

#### updateFeedbackStatus

更新反馈状态（管理员）。

```javascript
async function updateFeedbackStatus(id, status)
```

**参数**:
- `id`: 反馈 ID
- `status`: 新状态

**返回值**:
- 返回更新后的反馈对象
