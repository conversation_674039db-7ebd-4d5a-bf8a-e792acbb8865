<!--
  节点编辑器组件
  整合节点画布和属性面板
-->
<template>
  <div class="node-editor">
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button type="success" @click="importProject">
            <el-icon class="el-icon--left">
              <i-ep-upload />
            </el-icon>导入
          </el-button>
          <el-button type="success" @click="exportProject">
            <el-icon class="el-icon--left">
              <i-ep-download />
            </el-icon>导出
          </el-button>
        </el-button-group>
        <!-- 隐藏的文件输入框，用于导入文件 -->
        <input type="file" ref="fileInput" style="display: none" accept=".json" @change="handleFileImport" />
      </div>

      <div class="toolbar-center">
        <h2 class="editor-title">{{ projectTitle }}</h2>
      </div>

      <div class="toolbar-right">
        <el-button @click="openSpeakerMappingDialog">
          <el-icon class="el-icon--left">
            <i-ep-user />
          </el-icon>说话人映射
        </el-button>
        <el-button class="save-button" type="primary" @click="saveProject">
          <el-icon class="el-icon--left">
            <i-ep-document />
          </el-icon>保存
        </el-button>
      </div>
    </div>

    <div class="editor-content">
      <div class="canvas-container">
        <NodeCanvas @node-selected="handleNodeSelected" ref="canvasRef" />
      </div>

      <div class="properties-container" v-if="showProperties">
        <NodeProperties :selectedNodeId="selectedNodeId" @node-updated="handleNodeUpdated" />
      </div>
    </div>

    <!-- 保存对话框 -->
    <standard-dialog v-model="saveDialogVisible" title="保存内容" width="40%" :show-confirm="true"
      :confirm-text="saving ? '保存中...' : '保存'" cancel-text="取消" :close-on-click-modal="false" 
      :confirm-closes-dialog="false" :confirm-disabled="saving"
      @confirm="handleSave">
      <el-form :model="saveForm" label-width="5rem">
        <el-form-item label="标题" required>
          <el-input v-model="saveForm.title" placeholder="请输入标题（1-100个字符）" :maxlength="100" show-word-limit
            :disabled="saving"></el-input>
        </el-form-item>
        <el-form-item label="学习语言" required>
          <el-select v-model="saveForm.learningLanguage" placeholder="请选择内容的目标学习语言" :disabled="saving">
            <el-option v-for="language in supportedLanguages" :key="language.value" :label="language.label" :value="language.value">
              <span class="language-option">
                <span class="language-flag">{{ getLanguageFlag(language.value) }}</span>
                <span class="language-name">{{ language.label }}</span>
              </span>
            </el-option>
          </el-select>
          <div class="form-help-text">选择此内容适合学习的语言</div>
        </el-form-item>
        <el-form-item label="标签">
          <el-select v-model="tagList" multiple filterable allow-create default-first-option
            placeholder="请输入标签，按回车确认（最多10个）" @change="handleTagChange" :disabled="saving">
            <el-option v-for="tag in tagList" :key="tag" :label="tag" :value="tag" />
          </el-select>
          <div class="form-help-text">标签数量：{{ tagList.length }}/10</div>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="saveForm.description" type="textarea" :rows="4" placeholder="请输入描述（选填，最多500个字符）"
            :maxlength="500" show-word-limit :disabled="saving"></el-input>
        </el-form-item>
      </el-form>
    </standard-dialog>

    <!-- 说话人映射对话框 -->
    <standard-speaker-mapping-dialog v-model="speakerMappingDialogVisible" @saved="handleSpeakerMappingSaved" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import NodeCanvas from './NodeCanvas.vue';
import NodeProperties from './NodeProperties.vue';
import { useNodeStore } from '@/core/stores/nodeStore';
import contentService from "@/services/contentService";
import { ElMessage, ElMessageBox } from 'element-plus';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import StandardDialog from '@/components/common/StandardDialog.vue';
import StandardSpeakerMappingDialog from '@/components/settings/StandardSpeakerMappingDialog.vue';
import { trackEvent, EVENTS } from '@/utils/analytics';
import { checkFeaturePermission } from '@/services/featurePermissionService';
import { useLanguageStore } from '@/stores/languageStore';
import { SUPPORTED_LANGUAGES } from '@/config/languages';
import { getLanguageFlag } from '@/config/languageLevels';

const props = defineProps({
  projectId: {
    type: [String, Number],
    default: null
  },
  title: {
    type: String,
    default: '节点编辑器'
  },
  initialData: {
    type: Object,
    default: null
  },
  isEditing: {
    type: Boolean,
    default: false
  }
});

const router = useRouter();
const nodeStore = useNodeStore();
const languageStore = useLanguageStore();
const canvasRef = ref(null);
const selectedNodeId = ref(null);
const showProperties = ref(true);
const fileInput = ref(null);

// 支持的语言列表
const supportedLanguages = SUPPORTED_LANGUAGES;

// 保存相关
const saveDialogVisible = ref(false);
const saveForm = ref({
  title: '',
  description: '',
  tags: '',
  learningLanguage: '' // 添加学习语言字段
});

// 标签列表
const tagList = ref([]);

// 说话人映射对话框
const speakerMappingDialogVisible = ref(false);

// 处理标签变化
function handleTagChange(value) {
  // 更新 saveForm 中的 tags 字段
  saveForm.value.tags = value.join(',');
}

// 在组件挂载时初始化数据
onMounted(() => {
  if (props.initialData) {
    // 设置表单数据
    saveForm.value = {
      title: props.initialData.name || '',
      description: props.initialData.description || '',
      tags: '',
      learningLanguage: props.initialData.learningLanguage || languageStore.currentLearningLanguage || 'ja' // 使用现有数据或用户当前语言或默认日语
    };

    // 初始化标签列表
    if (props.initialData.tags) {
      // 如果是字符串，拆分为数组
      if (typeof props.initialData.tags === 'string') {
        tagList.value = props.initialData.tags.split(',').filter(tag => tag.trim());
      }
      // 如果已经是数组，直接使用
      else if (Array.isArray(props.initialData.tags)) {
        tagList.value = [...props.initialData.tags];
      }

      // 同步到 saveForm
      saveForm.value.tags = tagList.value.join(',');
    }

    // 如果有配置数据，导入到编辑器
    if (props.initialData.configJson) {
      const config = typeof props.initialData.configJson === 'string'
        ? JSON.parse(props.initialData.configJson)
        : props.initialData.configJson;
      nodeStore.importNodes(config);
    }
  } else {
    // 新建内容时设置默认语言
    saveForm.value.learningLanguage = languageStore.currentLearningLanguage || 'ja';
  }
});

// 组件销毁时清理数据
onUnmounted(() => {
  // 清空节点数据
  nodeStore.clearAllNodes();
});

// 项目标题
const projectTitle = computed(() => {
  if (props.isEditing) {
    return `编辑内容: ${saveForm.value.title || props.title}`;
  }
  return '新建内容';
});

// 处理节点选中
function handleNodeSelected(nodeId) {
  selectedNodeId.value = nodeId;
}

// 处理节点更新
function handleNodeUpdated(nodeId) {
  // ResourceNode 已经处理完成并缓存了结果，这里不需要额外处理
}

// 导出项目为JSON文件
async function exportProject() {
  try {
    // 导出节点数据
    const data = nodeStore.exportNodes();

    // 导入加密工具
    const { encryptJSON } = await import('@/utils/cryptoService');

    // 创建导出对象
    const exportData = {
      title: saveForm.value.title || '未命名项目',
      description: saveForm.value.description || '',
      tags: tagList.value,
      configJson: encryptJSON(data) // 加密配置数据
    };

    // 跟踪导出事件
    trackEvent(EVENTS.CONFIG_EXPORTED, {
      title: exportData.title,
      nodeCount: Object.keys(data.nodes || {}).length
    });

    // 创建JSON字符串（压缩格式）
    const jsonString = JSON.stringify(exportData);

    // 创建Blob对象
    const blob = new Blob([jsonString], { type: 'application/json' });

    // 创建下载链接
    const url = URL.createObjectURL(blob);

    // 创建临时下载链接
    const link = document.createElement('a');
    link.href = url;

    // 设置文件名 - 使用当前日期时间作为文件名
    const date = new Date();
    const fileName = `echo-lab-project-${date.getFullYear()}${(date.getMonth() + 1).toString().padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}-${date.getHours().toString().padStart(2, '0')}${date.getMinutes().toString().padStart(2, '0')}.json`;
    link.download = fileName;

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    ElMessage.success('项目已导出');
  } catch (error) {
    console.error('导出项目失败:', error);
    ElMessage.error('导出项目失败: ' + error.message);
  }
}

// 触发文件选择对话框
function importProject() {
  // 触发文件输入框的点击事件
  fileInput.value.click();
}

// 处理文件导入
function handleFileImport(event) {
  const file = event.target.files[0];
  if (!file) {
    return;
  }

  // 确认是否导入
  ElMessageBox.confirm('导入项目将覆盖当前画布，是否继续？', '确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    // 创建文件读取器
    const reader = new FileReader();

    // 设置读取完成的回调
    reader.onload = async (e) => {
      try {
        // 解析JSON数据
        const importData = JSON.parse(e.target.result);

        // 导入解密工具
        const { decryptJSON } = await import('@/utils/cryptoService');

        let nodeData;

        // 检查是否是导出的完整项目文件（包含标题、描述等）
        if (importData.configJson) {
          // 尝试解密configJson
          nodeData = decryptJSON(importData.configJson);

          // 更新表单数据
          if (importData.title) saveForm.value.title = importData.title;
          if (importData.description) saveForm.value.description = importData.description;
          if (importData.tags) {
            if (Array.isArray(importData.tags)) {
              tagList.value = [...importData.tags];
            } else if (typeof importData.tags === 'string') {
              tagList.value = importData.tags.split(',').filter(tag => tag.trim());
            }
          }
        } else {
          // 直接使用导入的数据作为节点数据
          nodeData = importData;
        }

        // 导入节点数据
        nodeStore.importNodes(nodeData);

        // 跟踪导入事件
        trackEvent(EVENTS.CONFIG_IMPORTED, {
          title: importData.title || '未命名项目',
          nodeCount: Object.keys(nodeData.nodes || {}).length
        });

        ElMessage.success('项目已导入');
      } catch (error) {
        console.error('导入项目失败:', error);
        ElMessage.error('导入项目失败: ' + (error.message || '文件格式不正确'));
      }
    };

    // 设置读取错误的回调
    reader.onerror = () => {
      ElMessage.error('读取文件失败');
    };

    // 读取文件
    reader.readAsText(file);
  }).catch(() => {
    // 取消操作
  }).finally(() => {
    // 重置文件输入框，以便于下次选择同一个文件时也能触发change事件
    event.target.value = '';
  });
}



// 保存状态
const saving = ref(false);

// 数据验证函数
function validateSaveData() {
  // 1. 验证标题
  const title = saveForm.value.title?.trim();
  if (!title) {
    return { valid: false, message: '请输入标题' };
  }
  if (title.length > 100) {
    return { valid: false, message: '标题长度不能超过100个字符' };
  }

  // 2. 验证描述
  const description = saveForm.value.description?.trim() || '';
  if (description.length > 500) {
    return { valid: false, message: '描述长度不能超过500个字符' };
  }

  // 3. 验证节点数据
  const data = nodeStore.exportNodes();
  if (!data || !data.nodes || Object.keys(data.nodes).length === 0) {
    return { valid: false, message: '请至少添加一个节点后再保存' };
  }

  // 4. 验证标签
  if (tagList.value.length > 10) {
    return { valid: false, message: '标签数量不能超过10个' };
  }

  for (const tag of tagList.value) {
    if (tag.length > 20) {
      return { valid: false, message: '单个标签长度不能超过20个字符' };
    }
  }

  // 5. 验证必要节点类型
  const nodeTypes = Object.values(data.nodes).map(node => node.type);
  const hasTextContent = nodeTypes.includes('textContent');
  const hasTextSequence = nodeTypes.includes('textSequence');

  if (!hasTextContent && !hasTextSequence) {
    return { valid: false, message: '项目必须包含文本内容节点或文本序列节点' };
  }

  return { valid: true, data, title, description };
}

// 保存项目
async function saveProject() {
  // 直接打开保存对话框，因为初始数据已经在组件挂载时设置好了
  saveDialogVisible.value = true;
}

// 处理保存
async function handleSave() {
  // 防止重复保存
  if (saving.value) {
    ElMessage.warning('正在保存中，请稍候...');
    return;
  }

  saving.value = true;

  try {
    // 1. 权限检查
    const hasPermission = await checkFeaturePermission('content_creation');
    if (!hasPermission) {
      ElMessage.warning('您没有内容创建权限，请联系管理员或升级会员等级');
      return;
    }

    // 2. 数据验证
    const validation = validateSaveData();
    if (!validation.valid) {
      ElMessage.warning(validation.message);
      return;
    }

    const { data, title, description } = validation;

    // 3. 处理加密
    let configJson;
    try {
      const { encryptJSON } = await import('@/utils/cryptoService');
      configJson = encryptJSON(data);
    } catch (encryptError) {
      console.warn('加密失败，使用原始数据:', encryptError);
      configJson = data; // 降级到不加密
      ElMessage.warning('数据加密失败，将以明文保存');
    }

    // 4. 查找视频配置节点，获取封面图链接
    let coverImageUrl = '';
    let coverDuration = 1.5;

    if (data && data.nodes) {
      const videoConfigNode = Object.values(data.nodes).find(node => node.type === 'videoConfig');
      if (videoConfigNode && videoConfigNode.params && videoConfigNode.params.cover) {
        coverImageUrl = videoConfigNode.params.cover.imageUrl || '';
        coverDuration = videoConfigNode.params.cover.duration || 1.5;
      }
    }

    // 5. 准备保存数据
    const content = {
      title: title,
      description: description,
      tags: tagList.value,
      configJson: configJson,
      coverImageUrl: coverImageUrl,
      coverDuration: coverDuration
    };

    // 6. 调用保存接口
    let response;
    let isNewContent = !props.isEditing;

    try {
      if (props.isEditing && props.projectId) {
        // 更新现有内容
        response = await contentService.updateContent(props.projectId, content);
      } else {
        // 创建新内容
        response = await contentService.createContent(content);
      }
    } catch (apiError) {
      // 处理API错误 - httpClient已经统一处理了错误格式
      let errorMessage = '保存失败';

      // httpClient抛出的错误已经是处理过的格式
      if (apiError.message) {
        errorMessage = apiError.message;
      } else if (typeof apiError === 'string') {
        errorMessage = apiError;
      }

      throw new Error(errorMessage);
    }

    // 7. 检查响应
    if (!response || !response.success) {
      throw new Error(response?.error || "保存失败");
    }

    const savedContent = response.content;

    // 8. 跟踪保存事件
    trackEvent(EVENTS.PROJECT_SAVED, {
      id: savedContent.id,
      title: content.title,
      isNew: isNewContent,
      nodeCount: Object.keys(data.nodes || {}).length
    });

    ElMessage.success('保存成功');
    
    // 9. 如果是新建内容，重定向到编辑模式
    if (isNewContent) {
      try {
        window.location.href = `/editor/${savedContent.id}`;
      } catch (redirectError) {
        console.error('页面跳转失败:', redirectError);
        ElMessage.warning('保存成功，但页面跳转失败，请手动刷新页面');
      }
    }

    // 保存成功后关闭对话框
    saveDialogVisible.value = false;
    return savedContent;
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error(error.message || '保存失败，请稍后重试');
    // 保存失败时不关闭对话框，让用户可以重试
  } finally {
    saving.value = false;
  }
}

// 打开说话人映射对话框
function openSpeakerMappingDialog() {
  speakerMappingDialogVisible.value = true;
}

// 处理说话人映射保存
function handleSpeakerMappingSaved(mappings) {
  ElMessage.success('说话人映射已保存');
}
</script>

<style scoped>
.node-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #ffffff;
  border-bottom: 0.0625rem solid #e4e7ed;
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.05);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.toolbar-center {
  flex: 2;
  text-align: center;
}

.toolbar-right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.editor-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 500;
  color: #303133;
}

.save-button {
  min-width: 5.625rem;
}

.editor-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.canvas-container {
  flex: 1;
  overflow: hidden;
}

.properties-container {
  width: 25rem;
  overflow-y: auto;
  background-color: #ffffff;
  border-left: 0.0625rem solid #e4e7ed;
}

.form-help-text {
  font-size: 0.75rem;
  color: #909399;
  margin-top: 0.25rem;
}
</style>
