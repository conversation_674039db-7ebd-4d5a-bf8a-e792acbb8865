/**
 * 安全警报模型
 * 用于记录安全相关的警报信息
 */
module.exports = (sequelize, DataTypes) => {
  const SecurityAlert = sequelize.define(
    "SecurityAlert",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      ip: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "IP地址",
      },
      type: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "警报类型",
      },
      details: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "详细信息",
      },
      status: {
        type: DataTypes.ENUM("new", "reviewed", "resolved", "false_positive"),
        defaultValue: "new",
        comment: "处理状态",
      },
      reviewedBy: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "审核人",
      },
      reviewedAt: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: "审核时间",
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: "创建时间",
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: "更新时间",
      },
    },
    {
      tableName: "security_alerts",
      timestamps: true,
      indexes: [
        {
          name: "idx_security_alerts_ip",
          fields: ["ip"],
        },
        {
          name: "idx_security_alerts_type",
          fields: ["type"],
        },
        {
          name: "idx_security_alerts_status",
          fields: ["status"],
        },
        {
          name: "idx_security_alerts_created_at",
          fields: ["created_at"],
        },
      ],
    }
  );

  // 添加自动清理过期记录的方法
  SecurityAlert.cleanupOldAlerts = async function (days = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    try {
      const deletedCount = await this.destroy({
        where: {
          createdAt: {
            [sequelize.Sequelize.Op.lt]: cutoffDate,
          },
          status: {
            [sequelize.Sequelize.Op.in]: ["resolved", "false_positive"],
          },
        },
      });

      console.log(`[SecurityAlert] 已清理 ${deletedCount} 条过期的安全警报记录`);
      return deletedCount;
    } catch (error) {
      console.error("[SecurityAlert] 清理过期记录失败:", error);
      throw error;
    }
  };

  return SecurityAlert;
};
