"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 添加增强上下文字段到 error_logs 表
    await queryInterface.addColumn("error_logs", "enhanced_context", {
      type: Sequelize.JSON,
      allowNull: true,
      comment: "增强的错误上下文信息（性能、环境、用户行为等）",
    });

    // 添加严重程度等级字段
    await queryInterface.addColumn("error_logs", "severity_level", {
      type: Sequelize.ENUM("low", "medium", "high", "critical"),
      allowNull: true,
      comment: "错误严重程度等级",
    });

    // 添加严重程度分数字段
    await queryInterface.addColumn("error_logs", "severity_score", {
      type: Sequelize.INTEGER,
      allowNull: true,
      comment: "错误严重程度分数(0-100)",
    });

    // 添加索引以提高查询性能
    await queryInterface.addIndex("error_logs", ["severity_level"], {
      name: "idx_error_logs_severity_level",
    });

    await queryInterface.addIndex("error_logs", ["severity_score"], {
      name: "idx_error_logs_severity_score",
    });

    console.log("增强错误上下文字段添加完成");
  },

  async down(queryInterface, Sequelize) {
    // 删除索引
    await queryInterface.removeIndex(
      "error_logs",
      "idx_error_logs_severity_level"
    );
    await queryInterface.removeIndex(
      "error_logs",
      "idx_error_logs_severity_score"
    );

    // 删除字段
    await queryInterface.removeColumn("error_logs", "enhanced_context");
    await queryInterface.removeColumn("error_logs", "severity_level");
    await queryInterface.removeColumn("error_logs", "severity_score");

    console.log("增强错误上下文字段回滚完成");
  },
};
