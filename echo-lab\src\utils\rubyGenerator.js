/**
 * Ruby标签生成工具
 * 用于生成带有假名标注的HTML内容
 */

/**
 * 判断字符是否为汉字
 * @param {string} char - 单个字符
 * @returns {boolean} - 是否为汉字
 */
function isKanji(char) {
  // 日语汉字Unicode范围
  return /[\u4E00-\u9FAF]/.test(char);
}

/**
 * 判断字符是否需要标注
 * @param {string} char - 单个字符
 * @returns {boolean} - 是否需要标注
 */
function needsAnnotation(char) {
  return isKanji(char);
}

/**
 * 生成带有Ruby标签的HTML内容
 * @param {string} content - 原始文本内容
 * @param {Object} annotation - 标注对象，包含reading和characters字段
 * @returns {string} - 带有Ruby标签的HTML内容
 */
export function generateRubyHTML(content, annotation) {
  // 如果没有内容或标注，直接返回加粗的原始内容
  if (!content || !annotation) {
    return `<b>${content}</b>`;
  }

  // 如果没有字符级标注，尝试使用reading字段
  if (!annotation.characters || annotation.characters.length === 0) {
    if (annotation.reading) {
      return `<b>${content}</b>`;
    }
    return `<b>${content}</b>`;
  }

  // 使用字符级标注生成Ruby标签
  const characters = annotation.characters;
  let html = "<b>"; // 整段话开始加粗
  let currentGroup = "";
  let currentReadingGroup = "";

  for (let i = 0; i < characters.length; i++) {
    const char = characters[i];

    // 如果是需要标注的字符（汉字），添加到当前组
    if (needsAnnotation(char.char)) {
      currentGroup += char.char;
      currentReadingGroup += char.reading || char.char;
    } else {
      // 如果当前组不为空，生成Ruby标签
      if (currentGroup) {
        html += `<ruby>${currentGroup}<rt>${currentReadingGroup}</rt></ruby>`;
        currentGroup = "";
        currentReadingGroup = "";
      }

      // 添加不需要标注的字符
      html += char.char;
    }
  }

  // 处理最后一个组
  if (currentGroup) {
    html += `<ruby>${currentGroup}<rt>${currentReadingGroup}</rt></ruby>`;
  }

  html += "</b>"; // 整段话结束加粗
  return html;
}
