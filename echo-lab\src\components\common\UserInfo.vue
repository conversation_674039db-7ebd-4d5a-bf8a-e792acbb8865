<template>
  <div class="user-info">
    <template v-if="isLoggedIn">
      <!-- 已登录状态 -->
      <el-dropdown trigger="click" @command="handleCommand">
        <div class="user-avatar">
          <el-avatar :size="32" :src="avatarUrl">
            {{ userInitials }}
          </el-avatar>
          <span class="username">{{ displayName }}</span>
          <el-icon>
            <i-ep-arrow-down />
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="content">
              <el-icon>
                <i-ep-document />
              </el-icon>
              内容管理
            </el-dropdown-item>
            <el-dropdown-item command="profile">
              <el-icon>
                <i-ep-user />
              </el-icon>
              个人信息
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon>
                <i-ep-setting />
              </el-icon>
              设置
            </el-dropdown-item>
            <el-dropdown-item command="favorites">
              <el-icon>
                <i-ep-star />
              </el-icon>
              我的收藏
            </el-dropdown-item>
            <el-dropdown-item command="feedback">
              <el-icon>
                <i-ep-chat-line-round />
              </el-icon>
              我的反馈
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon>
                <i-ep-switch-button />
              </el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </template>
    <template v-else>
      <!-- 未登录状态 -->
      <el-button type="primary" @click="goToLogin">登录</el-button>
    </template>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { useRouter } from "vue-router";
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { useUserStore } from "@/stores/userStore";

// 路由
const router = useRouter();

// 用户状态存储
const userStore = useUserStore();

// 计算属性：是否已登录
const isLoggedIn = computed(() => userStore.isLoggedIn);

// 计算属性：用户信息
const user = computed(() => userStore.user);

// 计算属性：显示名称
const displayName = computed(() => {
  if (!user.value) return "";
  return user.value.username || user.value.email.split("@")[0];
});

// 计算属性：用户头像URL
const avatarUrl = computed(() => {
  if (!user.value) return "";
  return user.value.avatar || "";
});

// 计算属性：用户名首字母（用于头像显示）
const userInitials = computed(() => {
  if (!user.value) return "";
  if (user.value.username) {
    return user.value.username.substring(0, 1).toUpperCase();
  }
  return user.value.email.substring(0, 1).toUpperCase();
});

// 处理下拉菜单命令
function handleCommand(command) {
  switch (command) {
    case "content":
      // 跳转到内容管理页面
      router.push("/content");
      break;
    case "profile":
      // 跳转到个人信息页面
      router.push("/profile");
      break;
    case "settings":
      // 跳转到设置页面
      router.push("/settings");
      break;
    case "favorites":
      // 跳转到收藏页面
      router.push("/favorites");
      break;
    case "feedback":
      // 跳转到反馈页面
      router.push("/feedback");
      break;
    case "logout":
      // 确认退出登录
      ElMessageBox.confirm("确定要退出登录吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 执行退出登录
          userStore.logout();
          // 跳转到首页
          router.push("/");
        })
        .catch(() => {
          // 取消退出登录
        });
      break;
  }
}

// 跳转到登录页面
function goToLogin() {
  router.push("/login");
}
</script>

<style scoped>
.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background-color 0.3s;
}

.user-avatar:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.username {
  margin: 0 0.5rem;
  font-size: 0.875rem;
  color: #303133;
}

/* 统一图标大小 */
.user-avatar .el-icon,
:deep(.el-dropdown-menu .el-icon) {
  font-size: 1.125rem !important;
  width: 1.125rem;
  height: 1.125rem;
}
</style>
