/**
 * TTS API路由
 * 提供文本转语音功能，支持OSS存储
 */
const express = require("express");
const router = express.Router();
const ttsService = require("../services/ttsService");

/**
 * 批量生成TTS接口
 * POST /api/tts/batch
 */
router.post("/batch", async (req, res) => {
  const { items, ignoreCache } = req.body;

  if (!items || !Array.isArray(items) || items.length === 0) {
    return res.status(400).send({ error: "缺少有效的音频项数组" });
  }

  // 设置超时处理
  const requestTimeout = setTimeout(() => {
    if (!res.headersSent) {
      console.error(`[TTS] 请求超时: 处理${items.length}个项目超过了5分钟`);
      return res.status(504).send({
        error: "请求处理超时，请减少批量处理的数量或稍后重试",
        timeout: true,
      });
    }
  }, 290000); // 设置为290秒，略小于Nginx的300秒超时

  try {
    console.log(
      `[TTS] 收到批量生成请求: ${items.length}个项目, 忽略缓存: ${
        ignoreCache ? "是" : "否"
      }`
    );

    // 限制批量处理的数量，避免服务器过载
    if (items.length > 20) {
      clearTimeout(requestTimeout);
      return res.status(400).send({
        error: "批量处理项目过多，请减少数量后重试（最多20个）",
      });
    }

    // 使用新的TTS服务批量生成音频，传递ignoreCache参数
    const results = await ttsService.batchGenerateAudio(items, ignoreCache);

    console.log(`[TTS] 批量生成完成: ${results.length}个结果`);

    // 清除超时定时器
    clearTimeout(requestTimeout);

    // 返回所有结果
    res.json({
      success: true,
      results,
    });
  } catch (error) {
    // 清除超时定时器
    clearTimeout(requestTimeout);

    console.error(`[TTS] 批量生成失败: ${error.message}`);
    res.status(500).send({ error: error.message });
  }
});

// 单个音频生成接口已移除，所有音频生成请求都通过批量接口实现

module.exports = router;
