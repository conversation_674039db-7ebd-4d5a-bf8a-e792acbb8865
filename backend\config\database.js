/**
 * 数据库配置文件
 * 使用环境变量或默认值配置数据库连接
 * 根据环境加载不同的配置文件
 */
const path = require("path");
const dotenv = require("dotenv");

// 根据环境加载不同的配置文件
const env = process.env.NODE_ENV || "development";
const envFile = env === "development" ? ".env.development" : ".env";
const envPath = path.resolve(process.cwd(), envFile);

// 加载环境变量
dotenv.config({ path: envPath });

// 如果指定的环境文件不存在，回退到默认的 .env 文件
if (env === "development" && !require("fs").existsSync(envPath)) {
  console.warn(`Warning: ${envFile} not found, falling back to .env`);
  dotenv.config();
}

module.exports = {
  development: {
    username: process.env.DB_USERNAME || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME || "echo-lab",
    host: process.env.DB_HOST || "127.0.0.1",
    port: process.env.DB_PORT || 3306,
    dialect: "mysql",
    logging: console.log,
    timezone: "+08:00",
    define: {
      underscored: true,
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
    },
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
  },
  production: {
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME || "echo-lab",
    host: process.env.DB_HOST,
    port: process.env.DB_PORT || 3306,
    dialect: "mysql",
    logging: false,
    timezone: "+08:00",
    define: {
      underscored: true,
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
    },
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
  },
};
