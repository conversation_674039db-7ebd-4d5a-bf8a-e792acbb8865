/**
 * 初始化用户等级数据
 *
 * 使用方法：
 * 1. 在项目根目录执行: node backend/scripts/initUserLevels.js
 */

const db = require("../models");

/**
 * 初始化用户等级数据
 */
async function initUserLevels() {
  try {
    console.log("开始初始化用户等级数据...");

    // 检查用户等级表是否存在
    try {
      await db.sequelize.query("SELECT 1 FROM user_levels LIMIT 1");
      console.log("用户等级表已存在");
    } catch (error) {
      console.log("用户等级表不存在，尝试创建...");
      await db.sequelize.query(`
        CREATE TABLE IF NOT EXISTS user_levels (
          id INT AUTO_INCREMENT PRIMARY KEY,
          level INT NOT NULL UNIQUE,
          name VARCHAR(100) NOT NULL,
          description TEXT,
          is_default BOOLEAN NOT NULL DEFAULT FALSE,
          created_at DATETIME NOT NULL,
          updated_at DATETIME NOT NULL,
          INDEX idx_level (level)
        )
      `);
      console.log("用户等级表创建成功");
    }

    // 查询现有记录
    const existingLevels = await db.UserLevel.findAll();
    console.log(`找到 ${existingLevels.length} 条现有记录`);

    // 如果没有记录，添加默认数据
    if (existingLevels.length === 0) {
      console.log("没有找到用户等级记录，添加默认数据...");

      const now = new Date();

      // 添加默认用户等级
      await db.UserLevel.bulkCreate([
        {
          level: 0,
          name: "免费用户",
          description: "基本功能，有使用限制",
          isDefault: true,
          createdAt: now,
          updatedAt: now,
        },
        {
          level: 1,
          name: "基础会员",
          description: "解锁更多功能，提高使用限制",
          isDefault: false,
          createdAt: now,
          updatedAt: now,
        },
        {
          level: 2,
          name: "高级会员",
          description: "解锁所有功能，无使用限制",
          isDefault: false,
          createdAt: now,
          updatedAt: now,
        },
      ]);

      console.log("默认用户等级数据添加成功");
    } else {
      // 更新现有记录
      console.log("更新现有用户等级记录...");

      // 定义默认等级数据
      const defaultLevels = [
        {
          level: 0,
          name: "免费用户",
          description: "基本功能，有使用限制",
        },
        {
          level: 1,
          name: "基础会员",
          description: "解锁更多功能，提高使用限制",
        },
        {
          level: 2,
          name: "高级会员",
          description: "解锁所有功能，无使用限制",
        },
      ];

      // 更新每个等级
      for (const levelData of defaultLevels) {
        const level = await db.UserLevel.findOne({
          where: { level: levelData.level },
        });

        if (level) {
          // 如果记录存在但名称或描述为空，则更新
          if (!level.name || !level.description) {
            await level.update({
              name: levelData.name,
              description: levelData.description,
            });
            console.log(`已更新等级 ${levelData.level} (${levelData.name})`);
          } else {
            console.log(
              `等级 ${levelData.level} (${level.name}) 已存在且有数据，跳过更新`
            );
          }
        } else {
          // 如果记录不存在，创建新记录
          await db.UserLevel.create({
            level: levelData.level,
            name: levelData.name,
            description: levelData.description,
            isDefault: levelData.level === 0,
          });
          console.log(`已创建新等级 ${levelData.level} (${levelData.name})`);
        }
      }
    }

    // 查询更新后的记录
    const updatedLevels = await db.UserLevel.findAll({
      order: [["level", "ASC"]],
    });

    console.log("\n更新后的用户等级表数据:");
    updatedLevels.forEach((level) => {
      console.log(
        `ID: ${level.id}, 等级: ${level.level}, 名称: ${level.name}, 描述: ${level.description}, 默认: ${level.isDefault}`
      );
    });

    console.log("\n用户等级数据初始化完成!");
    return true;
  } catch (error) {
    console.error("初始化用户等级数据失败:", error);
    return false;
  }
}

// 如果直接执行此脚本，则初始化用户等级数据
if (require.main === module) {
  (async () => {
    try {
      await initUserLevels();
    } catch (error) {
      console.error("执行脚本失败:", error);
    } finally {
      // 关闭数据库连接
      await db.sequelize.close();
      process.exit(0);
    }
  })();
}

// 导出函数
module.exports = initUserLevels;
