/**
 * 手动运行数据库迁移脚本
 * 用于执行音频字段添加迁移
 */

const { Sequelize } = require("sequelize");
const path = require("path");

// 加载环境配置
require("dotenv").config();

// 数据库配置
const sequelize = new Sequelize(
  process.env.DB_NAME || "echo-lab",
  process.env.DB_USER || "root",
  process.env.DB_PASSWORD || "",
  {
    host: process.env.DB_HOST || "localhost",
    port: process.env.DB_PORT || 3306,
    dialect: "mysql",
    logging: console.log,
    timezone: "+08:00",
    define: {
      charset: "utf8mb4",
      collate: "utf8mb4_unicode_ci",
    },
  }
);

async function runMigration() {
  try {
    console.log("开始连接数据库...");

    // 测试数据库连接
    await sequelize.authenticate();
    console.log("数据库连接成功");

    // 手动执行迁移逻辑
    console.log("开始执行音频字段迁移...");

    // 检查表是否存在
    const [tables] = await sequelize.query("SHOW TABLES LIKE 'audios'");

    if (tables.length === 0) {
      console.log("audios 表不存在，跳过迁移");
      return;
    }

    console.log("audios 表存在，检查字段...");

    // 检查字段是否已存在
    const [columns] = await sequelize.query("DESCRIBE audios");
    const existingColumns = columns.map((col) => col.Field);

    console.log("现有字段:", existingColumns);

    // 添加 audio_source 字段
    if (!existingColumns.includes("audio_source")) {
      console.log("添加 audio_source 字段...");
      await sequelize.query(`
        ALTER TABLE audios
        ADD COLUMN audio_source ENUM('tts', 'manual', 'upload')
        NOT NULL DEFAULT 'tts'
        COMMENT '音频来源：tts-TTS生成，manual-手动切割，upload-直接上传'
      `);
      console.log("✅ audio_source 字段添加成功");
    } else {
      console.log("⏭️  audio_source 字段已存在，跳过");
    }

    // 不再添加多余字段：content_id, segment_id, audio_type
    // 只保留 audio_source 字段即可

    // 添加索引
    console.log("添加索引...");

    try {
      await sequelize.query(
        "CREATE INDEX idx_audio_source ON audios (audio_source)"
      );
      console.log("✅ audio_source 索引添加成功");
    } catch (error) {
      if (error.original && error.original.code === "ER_DUP_KEYNAME") {
        console.log("⏭️  audio_source 索引已存在，跳过");
      } else {
        console.log("❌ audio_source 索引添加失败:", error.message);
      }
    }

    try {
      await sequelize.query(
        "CREATE INDEX idx_content_id ON audios (content_id)"
      );
      console.log("✅ content_id 索引添加成功");
    } catch (error) {
      if (error.original && error.original.code === "ER_DUP_KEYNAME") {
        console.log("⏭️  content_id 索引已存在，跳过");
      } else {
        console.log("❌ content_id 索引添加失败:", error.message);
      }
    }

    try {
      await sequelize.query(
        "CREATE INDEX idx_segment_id ON audios (segment_id)"
      );
      console.log("✅ segment_id 索引添加成功");
    } catch (error) {
      if (error.original && error.original.code === "ER_DUP_KEYNAME") {
        console.log("⏭️  segment_id 索引已存在，跳过");
      } else {
        console.log("❌ segment_id 索引添加失败:", error.message);
      }
    }

    console.log("🎉 迁移执行完成！");

    // 验证迁移结果
    console.log("验证迁移结果...");
    const [newColumns] = await sequelize.query("DESCRIBE audios");
    console.log("迁移后的字段:");
    newColumns.forEach((col) => {
      console.log(
        `  - ${col.Field} (${col.Type}) ${
          col.Null === "NO" ? "NOT NULL" : "NULL"
        } ${col.Default ? `DEFAULT ${col.Default}` : ""}`
      );
    });
  } catch (error) {
    console.error("❌ 迁移执行失败:", error);
    throw error;
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log("数据库连接已关闭");
  }
}

// 执行迁移
if (require.main === module) {
  runMigration()
    .then(() => {
      console.log("迁移脚本执行成功");
      process.exit(0);
    })
    .catch((error) => {
      console.error("迁移脚本执行失败:", error);
      process.exit(1);
    });
}

module.exports = { runMigration };
