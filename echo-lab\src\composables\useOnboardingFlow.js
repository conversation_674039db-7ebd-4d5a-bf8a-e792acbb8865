/**
 * 引导流程管理器
 * 管理新用户的语言选择和等级设置流程
 */

import { ref, computed } from 'vue';
import { useLanguageStore } from '@/stores/languageStore';
import { setUserLevel } from '@/utils/userPreferences';
import { SUPPORTED_LANGUAGES } from '@/config/languages';
import { getLanguageLevels } from '@/config/languageLevels';

// 引导流程步骤
export const ONBOARDING_STEPS = {
  LANGUAGE_SELECTION: 1,
  LEVEL_SELECTION: 2,
  COMPLETED: 3
};

export function useOnboardingFlow() {
  const languageStore = useLanguageStore();
  
  // 当前步骤
  const currentStep = ref(ONBOARDING_STEPS.LANGUAGE_SELECTION);
  
  // 用户选择
  const selectedLanguage = ref(null);
  const selectedLevel = ref(null);
  
  // 是否显示引导流程
  const shouldShowOnboarding = computed(() => {
    // 检查是否首次访问（没有完成过引导）
    const hasCompletedOnboarding = localStorage.getItem('echolab_onboarding_completed');
    
    // 检查是否已设置学习语言
    const hasLanguageSet = localStorage.getItem('echolab_learning_language');
    
    // 如果没有完成引导且没有设置语言，则显示引导
    return !hasCompletedOnboarding && !hasLanguageSet;
  });

  // 当前步骤的语言等级选项
  const currentLanguageLevels = computed(() => {
    if (!selectedLanguage.value) return [];
    return getLanguageLevels(selectedLanguage.value);
  });

  // 当前选择语言的标签
  const selectedLanguageLabel = computed(() => {
    if (!selectedLanguage.value) return '';
    const lang = SUPPORTED_LANGUAGES.find(l => l.value === selectedLanguage.value);
    return lang ? lang.label : '';
  });

  /**
   * 进入下一步
   */
  const nextStep = () => {
    if (currentStep.value === ONBOARDING_STEPS.LANGUAGE_SELECTION) {
      if (!selectedLanguage.value) {
        console.warn('请先选择学习语言');
        return false;
      }
      currentStep.value = ONBOARDING_STEPS.LEVEL_SELECTION;
      return true;
    }
    return false;
  };

  /**
   * 返回上一步
   */
  const previousStep = () => {
    if (currentStep.value === ONBOARDING_STEPS.LEVEL_SELECTION) {
      currentStep.value = ONBOARDING_STEPS.LANGUAGE_SELECTION;
      selectedLevel.value = null; // 清除等级选择
      return true;
    }
    return false;
  };

  /**
   * 跳过等级选择
   */
  const skipLevelSelection = () => {
    selectedLevel.value = null;
    completeOnboarding();
  };

  /**
   * 完成引导流程
   */
  const completeOnboarding = () => {
    try {
      // 保存选择的语言
      if (selectedLanguage.value) {
        languageStore.setLearningLanguage(selectedLanguage.value);
        console.log(`引导完成 - 学习语言: ${selectedLanguageLabel.value}`);
      }

      // 保存选择的等级
      if (selectedLevel.value) {
        setUserLevel(selectedLevel.value);
        console.log(`引导完成 - 学习等级: ${selectedLevel.value}`);
      }

      // 标记引导完成
      localStorage.setItem('echolab_onboarding_completed', 'true');
      localStorage.setItem('echolab_onboarding_completed_at', new Date().toISOString());

      // 更新步骤状态
      currentStep.value = ONBOARDING_STEPS.COMPLETED;

      console.log('用户引导流程完成');
      return true;
    } catch (error) {
      console.error('完成引导流程失败:', error);
      return false;
    }
  };

  /**
   * 重置引导流程（用于测试或重新引导）
   */
  const resetOnboarding = () => {
    currentStep.value = ONBOARDING_STEPS.LANGUAGE_SELECTION;
    selectedLanguage.value = null;
    selectedLevel.value = null;
    
    // 清除本地存储
    localStorage.removeItem('echolab_onboarding_completed');
    localStorage.removeItem('echolab_onboarding_completed_at');
    
    console.log('引导流程已重置');
  };

  /**
   * 检查引导流程状态
   */
  const getOnboardingStatus = () => {
    return {
      isCompleted: localStorage.getItem('echolab_onboarding_completed') === 'true',
      completedAt: localStorage.getItem('echolab_onboarding_completed_at'),
      currentStep: currentStep.value,
      selectedLanguage: selectedLanguage.value,
      selectedLevel: selectedLevel.value,
      shouldShow: shouldShowOnboarding.value
    };
  };

  return {
    // 状态
    currentStep,
    selectedLanguage,
    selectedLevel,
    shouldShowOnboarding,
    currentLanguageLevels,
    selectedLanguageLabel,
    
    // 常量
    ONBOARDING_STEPS,
    
    // 方法
    nextStep,
    previousStep,
    skipLevelSelection,
    completeOnboarding,
    resetOnboarding,
    getOnboardingStatus
  };
}

export default useOnboardingFlow;
