/**
 * 收藏服务
 * 管理用户收藏内容的相关操作
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

export default {
  /**
   * 获取用户收藏列表
   * @returns {Promise} 请求结果
   */
  async getFavorites() {
    return await httpClient.get(API_ENDPOINTS.FAVORITES.BASE);
  },

  /**
   * 添加收藏
   * @param {string} contentId 内容ID
   * @returns {Promise} 请求结果
   */
  async addFavorite(contentId) {
    return await httpClient.post(API_ENDPOINTS.FAVORITES.BASE, { contentId });
  },

  /**
   * 删除收藏
   * @param {string} contentId 内容ID
   * @returns {Promise} 请求结果
   */
  async removeFavorite(contentId) {
    return await httpClient.delete(
      `${API_ENDPOINTS.FAVORITES.BASE}/${contentId}`
    );
  },

  /**
   * 检查内容是否已收藏
   * @param {string} contentId 内容ID
   * @returns {Promise} 请求结果，包含 isFavorite 字段
   */
  async checkFavorite(contentId) {
    return await httpClient.get(API_ENDPOINTS.FAVORITES.CHECK(contentId));
  },
};
