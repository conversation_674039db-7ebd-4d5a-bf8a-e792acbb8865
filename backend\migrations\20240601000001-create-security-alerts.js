'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('security_alerts', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      ip: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: "IP地址"
      },
      type: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: "警报类型"
      },
      details: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: "详细信息"
      },
      status: {
        type: Sequelize.ENUM("new", "reviewed", "resolved", "false_positive"),
        defaultValue: "new",
        comment: "处理状态"
      },
      reviewed_by: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: "审核人"
      },
      reviewed_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "审核时间"
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "创建时间"
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        comment: "更新时间"
      }
    });

    // 添加索引
    await queryInterface.addIndex('security_alerts', ['ip'], {
      name: 'idx_security_alerts_ip'
    });
    await queryInterface.addIndex('security_alerts', ['type'], {
      name: 'idx_security_alerts_type'
    });
    await queryInterface.addIndex('security_alerts', ['status'], {
      name: 'idx_security_alerts_status'
    });
    await queryInterface.addIndex('security_alerts', ['created_at'], {
      name: 'idx_security_alerts_created_at'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('security_alerts');
  }
};
