/**
 * 公开内容服务
 * 用于获取和管理公开的视频内容
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

export default {
  /**
   * 获取所有公开内容
   * @param {Object} params 查询参数
   * @returns {Promise} 请求结果
   */
  async getPublicContents(params = {}) {
    return await httpClient.get(API_ENDPOINTS.CONTENTS.PUBLIC, params);
  },

  /**
   * 获取单个公开内容
   * @param {string} id 内容ID
   * @returns {Promise} 请求结果
   */
  async getPublicContent(id) {
    return await httpClient.get(`${API_ENDPOINTS.CONTENTS.PUBLIC}/${id}`);
  },

  /**
   * 获取热门内容
   * @param {number} limit 限制数量
   * @returns {Promise} 请求结果
   */
  async getHotContents(limit = 6) {
    return await httpClient.get(API_ENDPOINTS.CONTENTS.HOT, { limit });
  },

  /**
   * 获取最新内容
   * @param {number} limit 限制数量
   * @returns {Promise} 请求结果
   */
  async getLatestContents(limit = 6) {
    return await httpClient.get(API_ENDPOINTS.CONTENTS.LATEST, { limit });
  },

  /**
   * 按标签获取内容
   * @param {string} tag 标签
   * @param {number} limit 限制数量
   * @returns {Promise} 请求结果
   */
  async getContentsByTag(tag, limit = 20) {
    return await httpClient.get(`${API_ENDPOINTS.CONTENTS.BY_TAG}/${tag}`, {
      limit,
    });
  },

  /**
   * 搜索内容
   * @param {string} keyword 关键词
   * @param {number} limit 限制数量
   * @returns {Promise} 请求结果
   */
  async searchContents(keyword, limit = 20) {
    return await httpClient.get(API_ENDPOINTS.CONTENTS.SEARCH, {
      keyword,
      limit,
    });
  },
};
