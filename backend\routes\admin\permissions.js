/**
 * 管理员权限路由
 * 提供权限管理相关的API
 */
const express = require('express');
const router = express.Router();
const db = require('../../models');
const { adminOnly } = require('../../middleware/adminMiddleware');
const { authenticate } = require('../../middleware/authMiddleware');
const { Op } = require('sequelize');

/**
 * 获取等级权限
 * GET /api/admin/permissions/levels/:level
 */
router.get('/levels/:level', authenticate, adminOnly, async (req, res) => {
  try {
    const { level } = req.params;
    
    // 检查等级是否存在
    const levelObj = await db.UserLevel.findOne({
      where: { level }
    });
    
    if (!levelObj) {
      return res.status(404).json({
        success: false,
        error: '等级不存在'
      });
    }
    
    // 获取等级权限
    const permissions = await db.LevelPermission.findAll({
      where: { level }
    });
    
    res.json({
      success: true,
      permissions
    });
  } catch (error) {
    console.error('获取等级权限失败:', error);
    res.status(500).json({
      success: false,
      error: '获取等级权限失败'
    });
  }
});

/**
 * 更新等级权限
 * PUT /api/admin/permissions/levels/:level
 */
router.put('/levels/:level', authenticate, adminOnly, async (req, res) => {
  const transaction = await db.sequelize.transaction();
  
  try {
    const { level } = req.params;
    const { features } = req.body;
    
    if (!Array.isArray(features)) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        error: 'features必须是数组'
      });
    }
    
    // 检查等级是否存在
    const levelObj = await db.UserLevel.findOne({
      where: { level },
      transaction
    });
    
    if (!levelObj) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        error: '等级不存在'
      });
    }
    
    // 删除现有权限
    await db.LevelPermission.destroy({
      where: { level },
      transaction
    });
    
    // 创建新权限
    if (features.length > 0) {
      await db.LevelPermission.bulkCreate(
        features.map(featureKey => ({
          level,
          featureKey
        })),
        { transaction }
      );
    }
    
    // 提交事务
    await transaction.commit();
    
    res.json({
      success: true,
      message: '权限已更新'
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error('更新等级权限失败:', error);
    res.status(500).json({
      success: false,
      error: '更新等级权限失败'
    });
  }
});

/**
 * 获取等级使用限制
 * GET /api/admin/permissions/limits/:level
 */
router.get('/limits/:level', authenticate, adminOnly, async (req, res) => {
  try {
    const { level } = req.params;
    
    // 检查等级是否存在
    const levelObj = await db.UserLevel.findOne({
      where: { level }
    });
    
    if (!levelObj) {
      return res.status(404).json({
        success: false,
        error: '等级不存在'
      });
    }
    
    // 获取使用限制
    const limits = await db.FeatureUsageLimit.findAll({
      where: { level }
    });
    
    res.json({
      success: true,
      limits
    });
  } catch (error) {
    console.error('获取使用限制失败:', error);
    res.status(500).json({
      success: false,
      error: '获取使用限制失败'
    });
  }
});

/**
 * 更新等级使用限制
 * PUT /api/admin/permissions/limits/:level
 */
router.put('/limits/:level', authenticate, adminOnly, async (req, res) => {
  const transaction = await db.sequelize.transaction();
  
  try {
    const { level } = req.params;
    const { limits } = req.body;
    
    if (!Array.isArray(limits)) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        error: 'limits必须是数组'
      });
    }
    
    // 检查等级是否存在
    const levelObj = await db.UserLevel.findOne({
      where: { level },
      transaction
    });
    
    if (!levelObj) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        error: '等级不存在'
      });
    }
    
    // 删除现有限制
    await db.FeatureUsageLimit.destroy({
      where: { level },
      transaction
    });
    
    // 创建新限制
    if (limits.length > 0) {
      await db.FeatureUsageLimit.bulkCreate(
        limits.map(limit => ({
          level,
          featureKey: limit.featureKey,
          dailyLimit: limit.dailyLimit,
          monthlyLimit: limit.monthlyLimit
        })),
        { transaction }
      );
    }
    
    // 提交事务
    await transaction.commit();
    
    res.json({
      success: true,
      message: '使用限制已更新'
    });
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error('更新使用限制失败:', error);
    res.status(500).json({
      success: false,
      error: '更新使用限制失败'
    });
  }
});

/**
 * 获取功能使用统计
 * GET /api/admin/permissions/statistics/features
 */
router.get('/statistics/features', authenticate, adminOnly, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数: startDate, endDate'
      });
    }
    
    // 构建查询条件
    const where = {
      usedAt: {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      }
    };
    
    // 获取功能使用记录
    const records = await db.FeatureUsageRecord.findAll({
      where,
      attributes: [
        'featureKey',
        [db.sequelize.fn('COUNT', db.sequelize.col('id')), 'count'],
        [db.sequelize.fn('DATE', db.sequelize.col('used_at')), 'date']
      ],
      group: ['featureKey', db.sequelize.fn('DATE', db.sequelize.col('used_at'))],
      order: [
        ['featureKey', 'ASC'],
        [db.sequelize.fn('DATE', db.sequelize.col('used_at')), 'ASC']
      ]
    });
    
    // 获取每个功能的用户ID
    const userRecords = await db.FeatureUsageRecord.findAll({
      where,
      attributes: ['featureKey', 'userId'],
      group: ['featureKey', 'userId']
    });
    
    // 处理数据
    const featureMap = {};
    
    // 初始化功能统计
    records.forEach(record => {
      const featureKey = record.featureKey;
      const date = record.getDataValue('date');
      const count = parseInt(record.getDataValue('count'));
      
      if (!featureMap[featureKey]) {
        featureMap[featureKey] = {
          featureKey,
          totalUsage: 0,
          dailyTrend: [],
          userIds: new Set(),
          uniqueUsers: 0,
          avgPerUser: 0,
          maxDaily: 0
        };
      }
      
      featureMap[featureKey].totalUsage += count;
      featureMap[featureKey].dailyTrend.push({
        date,
        count
      });
      
      if (count > featureMap[featureKey].maxDaily) {
        featureMap[featureKey].maxDaily = count;
      }
    });
    
    // 添加用户统计
    userRecords.forEach(record => {
      const featureKey = record.featureKey;
      const userId = record.userId;
      
      if (featureMap[featureKey]) {
        featureMap[featureKey].userIds.add(userId);
      }
    });
    
    // 计算最终统计
    const statistics = Object.values(featureMap).map(feature => {
      feature.uniqueUsers = feature.userIds.size;
      feature.avgPerUser = feature.uniqueUsers > 0 ? feature.totalUsage / feature.uniqueUsers : 0;
      feature.userIds = Array.from(feature.userIds);
      return feature;
    });
    
    res.json({
      success: true,
      statistics
    });
  } catch (error) {
    console.error('获取功能使用统计失败:', error);
    res.status(500).json({
      success: false,
      error: '获取功能使用统计失败'
    });
  }
});

/**
 * 获取用户使用排行
 * GET /api/admin/permissions/statistics/users
 */
router.get('/statistics/users', authenticate, adminOnly, async (req, res) => {
  try {
    const { startDate, endDate, limit = 10 } = req.query;
    
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数: startDate, endDate'
      });
    }
    
    // 构建查询条件
    const where = {
      usedAt: {
        [Op.between]: [new Date(startDate), new Date(endDate)]
      }
    };
    
    // 获取用户使用记录
    const userCounts = await db.FeatureUsageRecord.findAll({
      where,
      attributes: [
        'userId',
        [db.sequelize.fn('COUNT', db.sequelize.col('id')), 'count']
      ],
      group: ['userId'],
      order: [[db.sequelize.fn('COUNT', db.sequelize.col('id')), 'DESC']],
      limit: parseInt(limit)
    });
    
    // 获取用户信息
    const userIds = userCounts.map(record => record.userId);
    
    const users = await db.User.findAll({
      where: {
        id: {
          [Op.in]: userIds
        }
      },
      attributes: ['id', 'email', 'username', 'level']
    });
    
    // 获取用户功能使用分布
    const featureUsage = await db.FeatureUsageRecord.findAll({
      where: {
        ...where,
        userId: {
          [Op.in]: userIds
        }
      },
      attributes: [
        'userId',
        'featureKey',
        [db.sequelize.fn('COUNT', db.sequelize.col('id')), 'count']
      ],
      group: ['userId', 'featureKey']
    });
    
    // 处理数据
    const userMap = {};
    
    users.forEach(user => {
      userMap[user.id] = user;
    });
    
    // 构建排行榜
    const rankings = userCounts.map(record => {
      const userId = record.userId;
      const totalUsage = parseInt(record.getDataValue('count'));
      const user = userMap[userId];
      
      // 获取用户功能使用分布
      const userFeatureUsage = featureUsage
        .filter(usage => usage.userId === userId)
        .map(usage => ({
          featureKey: usage.featureKey,
          count: parseInt(usage.getDataValue('count'))
        }))
        .sort((a, b) => b.count - a.count);
      
      return {
        user,
        totalUsage,
        featureUsage: userFeatureUsage
      };
    });
    
    res.json({
      success: true,
      rankings
    });
  } catch (error) {
    console.error('获取用户使用排行失败:', error);
    res.status(500).json({
      success: false,
      error: '获取用户使用排行失败'
    });
  }
});

module.exports = router;
