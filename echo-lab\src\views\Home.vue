<!--
  首页视图
  使用Tab切换展示推荐内容、全部内容和热门合集
-->
<template>
  <div class="home-view" :class="{ 'mobile-layout': isMobile }">
    <!-- 左侧侧边栏 -->
    <div class="home-sidebar" :class="{ 'visible': sidebarVisible }">
      <HomeSidebar />
    </div>

    <!-- 主内容区域 -->
    <div class="home-content">
      <!-- 顶部导航栏 -->
      <div class="home-header">
        <!-- 左侧侧边栏按钮 -->
        <div class="sidebar-toggle" @click="toggleSidebar">
          <el-icon :size="isMobile ? 18 : 20">
            <i-ep-menu />
          </el-icon>
        </div>

        <!-- 右侧标题和logo -->
        <div class="header-right">
          <img src="../assets/logo.jpg" alt="Echo Lab" class="header-logo" />
          <h1 class="header-title">Echo Lab</h1>
          <!-- 签到按钮 -->
          <div class="header-checkin">
            <CheckInButton />
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="home-main">
        <!-- Tab切换 -->
        <div class="home-tabs">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="content-tabs" :stretch="true">
            <el-tab-pane label="推荐内容" name="recommended">
              <div v-if="!userLevel" class="no-level-tip">
                <el-empty :description="`请先选择你的${currentLanguageLabel}水平`" :image-size="80">
                  <el-button type="primary" @click="$router.push('/level-settings')">
                    选择等级
                  </el-button>
                </el-empty>
              </div>
              <div v-else class="recommended-wrapper">
                <div class="recommend-header">
                  <h2 class="recommend-title">
                    <el-icon class="title-icon"><i-ep-star /></el-icon>
                    为{{ userLevel }}推荐
                  </h2>
                  <el-button link @click="$router.push('/level-settings')" class="change-level-btn">
                    切换等级
                  </el-button>
                </div>
                <AllContent :contents="recommendedContents" :loading="recommendedLoading" :has-more="hasMoreRecommended"
                  :total="recommendedPagination.total" :filters="{}" :hide-header="true" :hide-filter="true"
                  @update-filters="() => { }" @clear-filters="() => { }" @load-more="loadMoreRecommended" />
              </div>
            </el-tab-pane>

            <el-tab-pane label="全部内容" name="all">
              <AllContent :contents="publicContentStore.contents" :loading="publicContentStore.loading"
                :has-more="hasMoreContents" :total="publicContentStore.pagination.total"
                :filters="publicContentStore.filters" @update-filters="handleFiltersUpdate"
                @clear-filters="handleFiltersClear" @load-more="loadMoreContents" />
            </el-tab-pane>

            <el-tab-pane label="热门合集" name="collections">
              <HotCollections :collections="collectionStore.publicCollections" :loading="collectionStore.loading"
                :total="collectionStore.publicPagination?.total || 0" :has-more="hasMoreCollections"
                @toggle-favorite="handleToggleFavorite" @view-all="goToCollections" @load-more="loadMoreCollections" />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- 遮罩层 - 点击关闭侧边栏 -->
    <div class="sidebar-overlay" v-if="sidebarVisible" @click="toggleSidebar"></div>

    <!-- 引导流程已移至独立页面 -->

  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { usePublicContentStore } from '@/core/stores/publicContentStore';
import { useCollectionStore } from '@/stores/collectionStore';
import { useCollectionFavoriteStore } from '@/stores/collectionFavoriteStore';
import { useUserStore } from '@/stores/userStore';
import { useLanguageStore } from '@/stores/languageStore';
import HomeSidebar from '@/components/common/HomeSidebar.vue';
import RecommendedContent from '@/components/content/RecommendedContent.vue';
import AllContent from '@/components/content/AllContent.vue';
import HotCollections from '@/components/collection/HotCollections.vue';
import CheckInButton from '@/components/common/CheckInButton.vue';
// OnboardingFlow 已移至独立页面

import { isMobileDevice } from '@/utils/deviceDetector';
import { ElMessage } from 'element-plus';
import { trackEvent, EVENTS } from '@/utils/analytics';
import { useSEO, PAGE_SEO_CONFIG } from '@/composables/useSEO';

// 初始化SEO
const { updateSEO } = useSEO(PAGE_SEO_CONFIG.home);

const router = useRouter();
const route = useRoute();
const publicContentStore = usePublicContentStore();
const collectionStore = useCollectionStore();
const collectionFavoriteStore = useCollectionFavoriteStore();
const userStore = useUserStore();
const languageStore = useLanguageStore();

// 侧边栏状态
const sidebarVisible = ref(false);

// 引导流程已移至独立页面

// 当前学习语言
const currentLearningLanguage = computed(() => languageStore.currentLearningLanguage);

// Tab状态
const activeTab = ref('recommended');

// 用户等级（从 languageStore 获取）
const userLevel = computed(() => languageStore.currentUserLevel);

// 推荐内容独立状态
const recommendedContents = ref([]);
const recommendedLoading = ref(false);
const recommendedPagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
});



// 设备检测
const isMobile = computed(() => isMobileDevice());

// 计算是否还有更多内容
const hasMoreContents = computed(() => {
  const { contents, pagination } = publicContentStore;
  return contents.length > 0 && contents.length < pagination.total;
});

// 计算推荐内容是否还有更多
const hasMoreRecommended = computed(() => {
  return recommendedContents.value.length > 0 &&
    recommendedContents.value.length < recommendedPagination.value.total;
});



const hasMoreCollections = computed(() => {
  const collections = collectionStore.publicCollections;
  const pagination = collectionStore.publicPagination;
  return collections.length > 0 && collections.length < (pagination?.total || 0);
});

// 切换侧边栏显示状态
function toggleSidebar() {
  sidebarVisible.value = !sidebarVisible.value;
}

// 处理Tab切换
async function handleTabChange(tabName) {
  activeTab.value = tabName;

  // 根据Tab加载对应数据
  if (tabName === 'recommended' && userLevel.value) {
    // 加载推荐内容
    if (userLevel.value) {
      await loadRecommendedContents(true);
    }
  } else if (tabName === 'all') {
    if (publicContentStore.contents.length === 0) {
      await publicContentStore.fetchPublicContents();
    }
  } else if (tabName === 'collections') {
    if (collectionStore.publicCollections.length === 0) {
      await collectionStore.fetchPublicCollections({ limit: 12, sortBy: 'updated_at', sortOrder: 'DESC' });
    }
  }
}





// 加载更多全部内容
async function loadMoreContents() {
  if (publicContentStore.loading || !hasMoreContents.value) return;

  try {
    await publicContentStore.loadMoreContents();
  } catch (error) {
    console.error('加载更多内容失败:', error);
    ElMessage.error('加载更多内容失败');
  }
}

// 处理过滤器更新
function handleFiltersUpdate(filters) {
  publicContentStore.updateFilters(filters);
}

// 处理过滤器清除
function handleFiltersClear() {
  publicContentStore.clearFilters();
}

// 处理合集收藏切换
async function handleToggleFavorite(collection) {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再收藏合集');
    router.push('/login');
    return;
  }

  try {
    const result = await collectionFavoriteStore.toggleFavorite(collection.id, collection);

    if (result.success) {
      trackEvent(result.isFavorited ? EVENTS.COLLECTION_FAVORITE : EVENTS.COLLECTION_UNFAVORITE, {
        collectionId: collection.id,
        collectionTitle: collection.title,
        device: isMobile.value ? 'mobile' : 'desktop'
      }).catch(console.warn);

      ElMessage.success(result.isFavorited ? '收藏成功' : '取消收藏成功');
    } else {
      throw new Error(result.error || '收藏操作失败');
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    ElMessage.error(error.message || '收藏操作失败');
  }
}

// 加载更多合集
async function loadMoreCollections() {
  if (collectionStore.loading || !hasMoreCollections.value) return;

  try {
    await collectionStore.loadMoreCollections();
  } catch (error) {
    console.error('加载更多合集失败:', error);
    ElMessage.error('加载更多合集失败');
  }
}

// 加载推荐内容
async function loadRecommendedContents(reset = false) {
  if (!userLevel.value) return;

  if (reset) {
    recommendedPagination.value.page = 1;
    recommendedContents.value = [];
  }

  recommendedLoading.value = true;
  try {
    const params = {
      page: recommendedPagination.value.page,
      pageSize: recommendedPagination.value.pageSize
    };

    const response = await fetch(`/api/public/contents/recommended?${new URLSearchParams(params)}`).then(r => r.json());

    if (response && response.success) {
      if (reset) {
        recommendedContents.value = response.contents || [];
      } else {
        recommendedContents.value = [...recommendedContents.value, ...(response.contents || [])];
      }
      recommendedPagination.value.total = response.pagination?.total || 0;
    }
  } catch (error) {
    console.error('加载推荐内容失败:', error);
    ElMessage.error('加载推荐内容失败');
  } finally {
    recommendedLoading.value = false;
  }
}

// 加载更多推荐内容
async function loadMoreRecommended() {
  if (recommendedLoading.value || !hasMoreRecommended.value) return;

  recommendedPagination.value.page += 1;
  await loadRecommendedContents(false);
}

// 跳转到公开合集页面
function goToCollections() {
  router.push('/collections');
}

// 引导流程处理已移至独立页面

// 初始化数据加载
async function initializeData() {
  // 检查URL参数
  const tabFromQuery = route.query.tab;
  if (['recommended', 'all', 'collections'].includes(tabFromQuery)) {
    activeTab.value = tabFromQuery;
  }

  // 如果没有设置等级且默认是推荐Tab，切换到全部内容Tab
  if (!userLevel.value && activeTab.value === 'recommended') {
    activeTab.value = 'all';
  }

  // 根据当前Tab加载数据（只在没有数据时加载）
  if (activeTab.value === 'recommended' && userLevel.value && recommendedContents.value.length === 0) {
    await loadRecommendedContents(true);
  } else if (activeTab.value === 'all' && publicContentStore.contents.length === 0) {
    await publicContentStore.fetchPublicContents();
  } else if (activeTab.value === 'collections' && collectionStore.publicCollections.length === 0) {
    await collectionStore.fetchPublicCollections({ limit: 12, sortBy: 'updated_at', sortOrder: 'DESC' });
  }

  // 加载收藏状态
  if (userStore.isLoggedIn) {
    try {
      await collectionFavoriteStore.fetchFavoriteCollections();
    } catch (error) {
      console.error('加载收藏状态失败:', error);
    }
  }
}

// 监听用户等级变化
watch(userLevel, async (newLevel, oldLevel) => {
  if (newLevel !== oldLevel && newLevel) {
    await loadRecommendedContents(true); // 重置并重新加载
  }
});

// 监听路由变化，从等级设置页面返回时重新加载 store 状态
watch(() => route.path, () => {
  // 当从设置页面返回时，重新加载用户等级
  if (route.path === '/' && route.query.from === 'level-settings') {
    languageStore.reloadUserLevel();
  }
});

// 组件挂载时初始化
onMounted(async () => {
  // 初始化语言状态
  languageStore.loadLearningLanguage();

  await initializeData();

  // 跟踪首页访问
  trackEvent(EVENTS.HOME_PAGE_VISIT, {
    device: isMobile.value ? 'mobile' : 'desktop',
    isLoggedIn: userStore.isLoggedIn,
    activeTab: activeTab.value,
    userLevel: userLevel.value,
    learningLanguage: currentLearningLanguage.value
  }).catch(console.warn);
});
</script>

<style scoped>
.home-view {
  min-height: 100%;
  background-color: #f5f7fa;
  display: flex;
  position: relative;
}

/* 侧边栏切换按钮 */
.sidebar-toggle {
  width: 2.25rem;
  height: 2.25rem;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 95;
}

.sidebar-toggle .el-icon {
  font-size: 1.125rem;
  color: #606266;
}

/* 侧边栏样式 */
.home-sidebar {
  width: 0;
  background-color: #ffffff;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  transition: width 0.3s;
  overflow: hidden;
  box-shadow: 0.125rem 0 0.5rem rgba(0, 0, 0, 0.1);
}

.home-sidebar.visible {
  width: 16rem;
}

/* 遮罩层 */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 90;
}

/* 主内容区域容器 */
.home-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  margin-left: 0;
  transition: margin-left 0.3s;
  position: relative;
  width: 100%;
}

/* 顶部导航栏 */
.home-header {
  background-color: #ffffff;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1000;
  box-sizing: border-box;
  height: auto;
}

.header-right {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.header-logo {
  width: 2.25rem;
  height: 2.25rem;
}

.header-title {
  font-size: 1.25rem;
  color: #303133;
  margin: 0 0 0 0.5rem;
}

/* 主要内容区域 */
.home-main {
  flex: 1;
  padding: 1rem;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  padding-top: 4rem;
}

/* Tab切换区域 */
.home-tabs {
  width: 100%;
}

.content-tabs {
  width: 100%;
}

.content-tabs :deep(.el-tabs__header) {
  margin-bottom: 1.5rem;
}

.content-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0 1rem;
}

.content-tabs :deep(.el-tabs__item) {
  font-size: 1rem;
  font-weight: 500;
  padding: 0 1.5rem;
  height: 2.5rem;
  line-height: 2.5rem;
}

.content-tabs :deep(.el-tabs__content) {
  padding: 0;
}

/* 移动端优化 */
.mobile-layout .header-logo {
  width: 2rem;
  height: 2rem;
}

.mobile-layout .header-title {
  font-size: 1.125rem;
  margin: 0 0 0 0.375rem;
}

.mobile-layout .sidebar-toggle {
  width: 2rem;
  height: 2rem;
}

.mobile-layout .home-main {
  padding: 0.5rem;
  padding-top: 3.5rem;
}

.mobile-layout .home-tabs {
  width: 100%;
}

.mobile-layout .content-tabs :deep(.el-tabs__header) {
  margin-bottom: 1rem;
  padding: 0.125rem;
}

.mobile-layout .content-tabs :deep(.el-tabs__item) {
  font-size: 0.8rem;
  padding: 0 0.875rem;
  height: 2rem;
  line-height: 2rem;
}

/* 推荐内容样式 */
.recommended-wrapper {
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
}

.recommend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 0.75rem;
  border-bottom: 0.125rem solid #f0f2f5;
}

.recommend-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.title-icon {
  font-size: 1.125rem;
  color: #409eff;
}

.change-level-btn {
  font-size: 0.875rem;
  padding: 0.25rem 0.5rem;
}

.no-level-tip {
  padding: 2rem 0;
  text-align: center;
  background: white;
  border-radius: 0.75rem;
}

/* 标题栏签到按钮样式 */
.header-checkin {
  margin-left: 0.75rem;
}

.mobile-layout .header-checkin {
  margin-left: 0.5rem;
}
</style>
