/**
 * 特殊词汇模型
 * 用于存储TTS服务中需要特殊处理的词汇
 */
module.exports = (sequelize, DataTypes) => {
  const SpecialWord = sequelize.define('SpecialWord', {
    // 特殊词汇
    word: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '特殊词汇'
    },
    
    // TTS服务ID，如google、baidu等
    serviceId: {
      type: DataTypes.STRING(50),
      allowNull: false,
      field: 'service_id',
      comment: 'TTS服务ID，如google、baidu等'
    },
    
    // 语言代码，如ja、zh-CN等
    languageCode: {
      type: DataTypes.STRING(10),
      allowNull: false,
      field: 'language_code',
      comment: '语言代码，如ja、zh-CN等'
    },
    
    // 是否为系统词汇
    isSystem: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: 'is_system',
      comment: '是否为系统词汇'
    }
  }, {
    tableName: 'special_words',
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['word', 'service_id'],
        name: 'idx_word_service'
      },
      {
        fields: ['service_id'],
        name: 'idx_service_id'
      },
      {
        fields: ['language_code'],
        name: 'idx_language_code'
      },
      {
        fields: ['is_system'],
        name: 'idx_is_system'
      }
    ]
  });

  return SpecialWord;
};
