<script setup>
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { getLanguageLabel } from '@/config/languages';
import draggable from 'vuedraggable';

// 接收属性
const props = defineProps({
  sections: {
    type: Array,
    required: true
  },
  activeSectionIndex: {
    type: Number,
    default: 0
  }
});

// 事件
const emit = defineEmits(['update:sections', 'select', 'duplicate', 'remove', 'edit']);

// 移除未使用的计算属性

// 拖拽模型
const dragOptions = {
  animation: 200,
  group: "sections",
  disabled: false,
  ghostClass: "ghost",
  handle: ".drag-handle"
};

// 选择环节
const selectSection = (index) => {
  emit('select', index);
};

// 编辑环节
const editSection = (event, index) => {
  event.stopPropagation();
  selectSection(index);
  emit('edit', index);
};

// 复制环节
const duplicateSection = (event, index) => {
  event.stopPropagation();
  emit('duplicate', index);
};

// 删除环节
const removeSection = (event, index) => {
  event.stopPropagation();
  emit('remove', index);
};



// 处理拖拽结束
const handleDragEnd = (event) => {
  // 如果当前选中的环节被移动了，更新选中索引
  if (event.oldIndex === props.activeSectionIndex) {
    emit('select', event.newIndex);
  }
};
</script>

<template>
  <div class="draggable-section-list">
    <draggable :model-value="sections" @update:model-value="$emit('update:sections', $event)" v-bind="dragOptions"
      class="section-cards" @end="handleDragEnd" item-key="id">
      <template #item="{ element, index }">
        <div class="section-card" :class="{ 'active': index === activeSectionIndex }" @click="selectSection(index)">

          <div class="drag-handle">
            <div class="drag-handle-icon">
              <i class="el-icon-d-caret"></i>
            </div>
          </div>

          <div class="section-card-header">
            <div class="section-title">
              <span class="section-name">{{ element.name }}</span>
              <el-icon v-if="element.userEditable === false" class="lock-icon">
                <i-ep-lock />
              </el-icon>
            </div>

            <div class="section-actions">
              <div class="section-buttons">
                <el-button-group>
                  <el-button type="primary" size="small" @click.stop="editSection($event, index)">
                    <el-icon>
                      <i-ep-edit />
                    </el-icon>
                  </el-button>
                  <el-button type="success" size="small" @click.stop="duplicateSection($event, index)">
                    <el-icon>
                      <i-ep-copy-document />
                    </el-icon>
                  </el-button>
                  <el-button type="danger" size="small" @click.stop="removeSection($event, index)"
                    :disabled="sections.length <= 1">
                    <el-icon>
                      <i-ep-delete />
                    </el-icon>
                  </el-button>
                </el-button-group>
              </div>


            </div>
          </div>

          <div class="section-card-content">
            <div class="section-info">
              <!-- 第一行：基本信息 -->
              <div class="info-row">
                <!-- 重复次数 -->
                <div class="info-item">
                  <span class="info-label">重复次数:</span>
                  <span class="info-value">{{ element.repeatCount || 4 }}</span>
                </div>

                <!-- 翻译信息 -->
                <div class="info-item" v-if="element.enableTranslation">
                  <span class="info-label">翻译:</span>
                  <span class="info-value">{{ element.translationLanguage ?
                    getLanguageLabel(element.translationLanguage)
                    : '未设置' }}</span>
                </div>

                <!-- 关键词信息 -->
                <div class="info-item" v-if="element.enableKeywords">
                  <span class="info-label">关键词:</span>
                  <span class="info-value">已启用 ({{ element.keywordRepeatCount || 2 }}次)</span>
                </div>
              </div>



              <!-- 重复参数设置 -->
              <div class="info-row">
                <!-- 语速 -->
                <div class="info-item">
                  <span class="info-label">语速:</span>
                  <span class="info-value">
                    <template v-if="element.repeatSpeeds && element.repeatSpeeds.length > 0">
                      <el-tooltip placement="top">
                        <template #content>
                          <div v-for="(speed, idx) in element.repeatSpeeds" :key="idx">
                            第{{ idx + 1 }}次: {{ speed }}x
                          </div>
                        </template>
                        {{element.repeatSpeeds.map(s => s + 'x').join(', ')}}
                      </el-tooltip>
                    </template>
                    <template v-else>
                      1.0x
                    </template>
                  </span>
                </div>

                <!-- 停顿时长 -->
                <div class="info-item">
                  <span class="info-label">停顿:</span>
                  <span class="info-value">
                    <!-- 如果有重复停顿数组且不是所有值都相同，显示详细信息 -->
                    <template
                      v-if="element.repeatPauses && element.repeatPauses.length > 0 && !element.repeatPauses.every(p => p === element.repeatPauses[0])">
                      <el-tooltip placement="top">
                        <template #content>
                          <div v-for="(pause, idx) in element.repeatPauses" :key="idx">
                            第{{ idx + 1 }}次: {{ pause }}ms
                          </div>
                        </template>
                        {{(element.repeatPauses || []).map(p => p + 'ms').join(', ')}}
                      </el-tooltip>
                    </template>
                    <!-- 否则显示单个停顿时长 -->
                    <template v-else>
                      {{ (element.repeatPauses && element.repeatPauses.length > 0 ? element.repeatPauses[0] :
                        element.pauseDuration || 3000) }}ms
                    </template>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮已移至标题栏 -->
        </div>
      </template>
    </draggable>
  </div>
</template>

<style scoped>
.draggable-section-list {
  width: 100%;
}

.section-cards {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0 0.5rem;
  overflow-y: auto;
}

.section-card {
  position: relative;
  border: 0.0625rem solid #e8e8e8;
  border-radius: 0.5rem;
  padding: 0.75rem 0.75rem 0.75rem 2rem;
  /* 左侧留出拖动把手的空间 */
  background-color: #fff;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
  height: auto;
  /* 使用自适应高度，根据内容自动调整 */
  margin-bottom: 0.5rem;
}

.section-card.active {
  border-color: #409eff;
  background-color: #fafafa;
}



.section-card.ghost {
  opacity: 0.5;
  background: #f5f7fa;
  border-style: dashed;
}

.drag-handle {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
  cursor: move;
}

.drag-handle-icon {
  color: #909399;
  font-size: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.drag-handle-icon::before,
.drag-handle-icon::after {
  content: "";
  width: 0.75rem;
  height: 0.125rem;
  background-color: #c0c4cc;
  border-radius: 0.0625rem;
}

.section-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  flex-wrap: nowrap;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  max-width: 50%;
  flex-shrink: 1;
  min-width: 5rem;
}

.section-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
  flex-wrap: nowrap;
}

.section-buttons {
  display: flex;
  flex-shrink: 0;
}

.section-buttons .el-button {
  padding: 0.375rem 0.375rem;
}

.lock-icon {
  color: #e6a23c;
  margin-left: 0.25rem;
  font-size: 0.75rem;
}

.section-card-content {
  margin-bottom: 0.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.section-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-row {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 0.25rem;
  align-items: center;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  min-width: 30%;
}

.info-label {
  color: #909399;
  font-size: 0.75rem;
  white-space: nowrap;
}

.info-value {
  font-weight: 500;
  font-size: 0.75rem;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #606266;
}

/* 底部操作区域已移除 */
</style>
