<!--
  浮动帮助按钮组件
  只在播放页显示，固定在页面侧边，方便用户查看播放页使用帮助
-->
<template>
  <div v-if="shouldShowButton" class="floating-help-button-container" :class="{ 'mobile-device': isMobile }">
    <el-button type="info" size="small" class="floating-help-button" @click="showHelp" title="查看播放页使用帮助">
      <el-icon>
        <i-ep-question-filled />
      </el-icon>
      帮助
    </el-button>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { useHelp } from '@/composables/useHelp';
import { isMobileDevice } from '@/utils/deviceDetector';

// 检测是否为移动设备
const isMobile = isMobileDevice();

// 获取当前路由
const route = useRoute();

// 帮助功能
const { showHelp: showHelpDialog } = useHelp();

// 只在播放页显示帮助按钮
const shouldShowButton = computed(() => {
  return route.path.startsWith('/player/');
});

// 显示播放页帮助
const showHelp = () => {
  showHelpDialog('player');
};
</script>

<style scoped>
.floating-help-button-container {
  position: fixed;
  right: 1.5rem;
  bottom: 5rem;
  /* 在反馈按钮上方 */
  z-index: 100;
}

.floating-help-button {
  border-radius: 1.5rem;
  padding: 0.5rem 1rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.2);
  background-color: #909399;
  border-color: #909399;
  color: white;
  transition: all 0.3s ease;
}

.floating-help-button:hover {
  background-color: #73767a;
  border-color: #73767a;
  transform: translateY(-0.125rem);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.3);
}

.floating-help-button:focus {
  background-color: #73767a;
  border-color: #73767a;
}

/* 移动端样式 */
.mobile-device .floating-help-button-container {
  right: 1rem;
  bottom: 4rem;
  /* 在反馈按钮上方 */
}

.mobile-device .floating-help-button {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}
</style>
