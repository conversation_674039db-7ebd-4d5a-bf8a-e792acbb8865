/**
 * 文本序列节点处理器
 * 管理文本序列和顺序
 */

import { v4 as uuidv4 } from "uuid";

/**
 * 处理文本序列节点
 * @param {Object} node - 节点对象
 * @param {Array} sourceResults - 源节点结果数组
 * @returns {Object} 处理结果
 */
function textSequenceNodeProcessor(node, sourceResults = []) {
  // 检查源节点结果
  if (!sourceResults || sourceResults.length === 0) {
    return {
      sequence: [],
      sections: node.params.sections || [], // 确保包含环节信息
      isEmpty: true,
    };
  }

  // 如果已有序列，直接返回
  if (node.params.sequence && node.params.sequence.length > 0) {
    console.log("使用已有序列，长度:", node.params.sequence.length);
    return {
      sequence: [...node.params.sequence],
      sections: node.params.sections || [], // 确保包含环节信息
      isEmpty: false,
    };
  }

  console.log("没有已有序列，将从源节点生成序列");

  // 创建序列
  const sequence = [];

  // 处理模式 - 统一为序列模式
  const mode = "sequence"; // 统一使用序列模式
  const combineMode = node.params.combineMode || "sequential";

  // 处理源节点结果
  console.log("处理源节点结果，数量:", sourceResults.length);
  sourceResults.forEach((sourceResult, index) => {
    console.log(`处理源节点 ${index + 1}:`, sourceResult);

    // 如果源节点是分句节点
    if (sourceResult && sourceResult.segments) {
      console.log(`源节点有 ${sourceResult.segments.length} 个分句`);
      sourceResult.segments.forEach((segment) => {
        sequence.push({
          id: `seq_${uuidv4()}`,
          sourceId: segment.id,
          content: segment.content,
          language: segment.language || "ja", // 默认为日语
          type: segment.type || "normal", // 使用分句的类型，默认为普通文本
          contentType: "text", // 内容类型为文本
          duration: segment.duration || 2, // 默认时长2秒
          audioUrl: segment.audioUrl || "", // 音频URL
        });
      });
    }
    // 如果源节点是文本节点
    else if (sourceResult && sourceResult.text) {
      console.log(
        `源节点是文本节点，内容: ${sourceResult.text.substring(0, 20)}...`
      );
      sequence.push({
        id: `seq_${uuidv4()}`,
        sourceId: "text",
        content: sourceResult.text,
        language: sourceResult.language || "ja", // 默认为日语
        type: sourceResult.type || "normal", // 使用源节点的类型，默认为普通文本
        contentType: "text", // 内容类型为文本
        duration: sourceResult.duration || 2, // 默认时长2秒
        audioUrl: sourceResult.audioUrl || "", // 音频URL
      });
    }
    // 如果源节点是资源节点
    else if (sourceResult && sourceResult.sourceSegments) {
      console.log(
        `源节点是资源节点，有 ${sourceResult.sourceSegments.length} 个分句`
      );
      sourceResult.sourceSegments.forEach((segment) => {
        sequence.push({
          id: `seq_${uuidv4()}`,
          sourceId: segment.id,
          content: segment.content,
          language: segment.language || "ja", // 默认为日语
          type: segment.type || "normal", // 使用分句的类型，默认为普通文本
          contentType: "text", // 内容类型为文本
          duration: segment.duration || 2, // 默认时长2秒
          audioUrl: segment.audioUrl || "", // 音频URL
        });
      });
    }
    // 如果源节点是序列节点
    else if (sourceResult && sourceResult.sequence) {
      console.log(
        `源节点是序列节点，有 ${sourceResult.sequence.length} 个序列项`
      );
      sourceResult.sequence.forEach((item) => {
        sequence.push({
          id: `seq_${uuidv4()}`,
          sourceId: item.id || item.sourceId,
          content: item.content,
          language: item.language || "ja", // 默认为日语
          type: item.type || "normal", // 使用序列项的类型，默认为普通文本
          contentType: item.contentType || "text", // 内容类型，默认为文本
          duration: item.duration || 2, // 默认时长2秒
          audioUrl: item.audioUrl || "", // 音频URL
        });
      });
    } else {
      console.log("源节点没有可用内容:", sourceResult);
    }
  });

  console.log(`生成序列完成，共 ${sequence.length} 项`);

  // 统一使用序列模式
  let finalSequence = sequence;

  // 包含环节信息在处理结果中
  const result = {
    sequence: finalSequence,
    sections: node.params.sections || [],
    isEmpty: finalSequence.length === 0,
  };

  console.log("处理结果:", {
    sequenceLength: result.sequence.length,
    sectionsLength: result.sections.length,
    isEmpty: result.isEmpty,
  });

  return result;
}

export default textSequenceNodeProcessor;
