/**
 * 内容服务
 * 管理内容的创建、更新等操作
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

export default {
  /**
   * 创建新内容
   * @param {Object} content 内容数据
   */
  async createContent(content) {
    // 确保 tags 是字符串，如果是数组则转换为逗号分隔的字符串
    const tags = Array.isArray(content.tags)
      ? content.tags.join(",")
      : content.tags || "";

    return await httpClient.post(API_ENDPOINTS.CONTENTS.BASE, {
      name: content.title,
      description: content.description || "",
      tags: tags,
      configJson: content.configJson,
      thumbnailUrl: content.coverImageUrl || "", // 使用封面图链接作为缩略图
    });
  },

  /**
   * 更新内容
   * @param {string|number} id 内容ID
   * @param {Object} content 内容数据
   */
  async updateContent(id, content) {
    // 确保 tags 是字符串，如果是数组则转换为逗号分隔的字符串
    const tags = Array.isArray(content.tags)
      ? content.tags.join(",")
      : content.tags || "";

    return await httpClient.put(`${API_ENDPOINTS.CONTENTS.BASE}/${id}`, {
      name: content.title,
      description: content.description || "",
      tags: tags,
      configJson: content.configJson,
      thumbnailUrl: content.coverImageUrl || "", // 使用封面图链接作为缩略图
    });
  },

  /**
   * 获取所有内容
   */
  async getAllContent() {
    return await httpClient.get(API_ENDPOINTS.CONTENTS.BASE);
  },

  /**
   * 获取单个内容
   * @param {string|number} id 内容ID
   */
  async getContent(id) {
    return await httpClient.get(`${API_ENDPOINTS.CONTENTS.BASE}/${id}`);
  },

  /**
   * 删除内容
   * @param {string|number} id 内容ID
   */
  async deleteContent(id) {
    return await httpClient.delete(`${API_ENDPOINTS.CONTENTS.BASE}/${id}`);
  },

  /**
   * 发布内容（上线）
   * @param {string|number} id 内容ID
   */
  async publishContent(id) {
    return await httpClient.put(`${API_ENDPOINTS.CONTENTS.BASE}/${id}/publish`);
  },

  /**
   * 下架内容
   * @param {string|number} id 内容ID
   */
  async unpublishContent(id) {
    return await httpClient.put(
      `${API_ENDPOINTS.CONTENTS.BASE}/${id}/unpublish`
    );
  },

  /**
   * 记录内容观看
   * @param {string} id 内容ID
   * @param {number} viewDuration 观看时长（秒）
   */
  async recordView(id, viewDuration = 0) {
    return await httpClient.post(`${API_ENDPOINTS.CONTENTS.BASE}/${id}/views`, {
      viewDuration,
    });
  },
};
