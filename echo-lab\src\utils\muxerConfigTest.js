/**
 * MP4Muxer配置测试工具
 * 用于验证Muxer配置是否正确
 */

import { Muxer, ArrayBufferTarget } from "mp4-muxer";

/**
 * 测试Muxer配置
 */
export function testMuxerConfig() {
  console.log("开始测试MP4Muxer配置...");

  try {
    const target = new ArrayBufferTarget();

    // 测试基本配置
    const muxer = new Muxer({
      target,
      video: {
        codec: "avc",
        width: 1280,
        height: 720,
        frameRate: 30,
      },
      audio: {
        codec: "aac",
        numberOfChannels: 1,
        sampleRate: 48000,
      },
      fastStart: "in-memory",
      firstTimestampBehavior: "offset",
    });

    console.log("✅ Muxer配置测试成功");
    console.log("- 视频编解码器: avc (H.264)");
    console.log("- 音频编解码器: aac");
    console.log("- 分辨率: 1280x720");
    console.log("- 帧率: 30fps");
    console.log("- FastStart: in-memory");
    console.log("- 时间戳行为: offset");

    // 清理
    muxer.finalize();

    return {
      success: true,
      config: {
        video: { codec: "avc", width: 1280, height: 720, frameRate: 30 },
        audio: { codec: "aac", numberOfChannels: 1, sampleRate: 44100 },
        fastStart: "in-memory",
        firstTimestampBehavior: "offset",
      },
    };
  } catch (error) {
    console.error("❌ Muxer配置测试失败:", error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * 测试不同的fastStart选项
 */
export function testFastStartOptions() {
  console.log("测试不同的fastStart选项...");

  const options = [
    false,
    "in-memory",
    "fragmented",
    { expectedDuration: 10000 },
  ];

  const results = [];

  for (const fastStart of options) {
    try {
      const target = new ArrayBufferTarget();
      const muxer = new Muxer({
        target,
        video: {
          codec: "avc",
          width: 640,
          height: 480,
          frameRate: 30,
        },
        audio: {
          codec: "aac",
          numberOfChannels: 1,
          sampleRate: 48000,
        },
        fastStart,
        firstTimestampBehavior: "offset",
      });

      muxer.finalize();

      results.push({
        fastStart,
        success: true,
      });

      console.log(`✅ fastStart: ${JSON.stringify(fastStart)} - 成功`);
    } catch (error) {
      results.push({
        fastStart,
        success: false,
        error: error.message,
      });

      console.error(
        `❌ fastStart: ${JSON.stringify(fastStart)} - 失败:`,
        error.message
      );
    }
  }

  return results;
}

/**
 * 测试不同的视频质量配置
 */
export function testQualityConfigs() {
  console.log("测试不同的视频质量配置...");

  const qualityConfigs = [
    { name: "low", width: 854, height: 480, frameRate: 30 },
    { name: "medium", width: 1280, height: 720, frameRate: 30 },
    { name: "high", width: 1920, height: 1080, frameRate: 30 },
  ];

  const results = [];

  for (const config of qualityConfigs) {
    try {
      const target = new ArrayBufferTarget();
      const muxer = new Muxer({
        target,
        video: {
          codec: "avc",
          width: config.width,
          height: config.height,
          frameRate: config.frameRate,
        },
        audio: {
          codec: "aac",
          numberOfChannels: 1,
          sampleRate: 48000,
        },
        fastStart: "in-memory",
        firstTimestampBehavior: "offset",
      });

      muxer.finalize();

      results.push({
        ...config,
        success: true,
      });

      console.log(
        `✅ ${config.name} (${config.width}x${config.height}) - 成功`
      );
    } catch (error) {
      results.push({
        ...config,
        success: false,
        error: error.message,
      });

      console.error(
        `❌ ${config.name} (${config.width}x${config.height}) - 失败:`,
        error.message
      );
    }
  }

  return results;
}

/**
 * 运行完整的Muxer配置测试
 */
export function runFullMuxerTest() {
  console.log("🧪 开始完整的MP4Muxer配置测试");

  const results = {
    basicConfig: null,
    fastStartOptions: null,
    qualityConfigs: null,
    overall: false,
  };

  try {
    // 基本配置测试
    results.basicConfig = testMuxerConfig();

    // FastStart选项测试
    results.fastStartOptions = testFastStartOptions();

    // 质量配置测试
    results.qualityConfigs = testQualityConfigs();

    // 综合评估
    results.overall =
      results.basicConfig.success &&
      results.fastStartOptions.some((r) => r.success) &&
      results.qualityConfigs.every((r) => r.success);

    console.log("🎉 MP4Muxer配置测试完成");
    console.log("总体结果:", results.overall ? "✅ 成功" : "❌ 失败");
  } catch (error) {
    console.error("❌ 测试过程中发生错误:", error);
    results.error = error.message;
  }

  return results;
}

// 在开发环境下添加到全局
if (import.meta.env.DEV) {
  window.muxerConfigTest = {
    testBasicConfig: testMuxerConfig,
    testFastStartOptions: testFastStartOptions,
    testQualityConfigs: testQualityConfigs,
    runFullTest: runFullMuxerTest,
  };

  console.log("MP4Muxer配置测试工具已加载:");
  console.log("- 基本配置测试: muxerConfigTest.testBasicConfig()");
  console.log("- FastStart选项测试: muxerConfigTest.testFastStartOptions()");
  console.log("- 质量配置测试: muxerConfigTest.testQualityConfigs()");
  console.log("- 完整测试: muxerConfigTest.runFullTest()");
}
