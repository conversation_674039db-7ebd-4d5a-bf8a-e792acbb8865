<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>请求错误 - Echo Lab</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            text-align: center;
        }
        .container {
            max-width: 600px;
            padding: 40px 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #e74c3c;
            margin-bottom: 20px;
            font-size: 28px;
        }
        p {
            margin-bottom: 20px;
            line-height: 1.6;
            color: #555;
        }
        .btn {
            display: inline-block;
            background-color: #3498db;
            color: #fff;
            padding: 10px 20px;
            border-radius: 4px;
            text-decoration: none;
            transition: background-color 0.3s;
            font-weight: 500;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .error-code {
            font-size: 14px;
            color: #999;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>请求错误</h1>
        <p>很抱歉，您的请求无法处理。这可能是由于以下原因：</p>
        <ul style="text-align: left; margin-bottom: 20px;">
            <li>请求格式不正确</li>
            <li>您没有访问权限</li>
            <li>请求的资源不存在</li>
            <li>请求频率过高</li>
        </ul>
        <p>请稍后再试或返回首页。</p>
        <a href="/" class="btn">返回首页</a>
        <div class="error-code">错误代码：<span id="error-code">400</span></div>
    </div>
    <script>
        // 从URL获取错误代码
        const urlParams = new URLSearchParams(window.location.search);
        const errorCode = urlParams.get('code') || window.location.pathname.split('/').pop().replace('.html', '') || '400';
        document.getElementById('error-code').textContent = errorCode;
        
        // 根据错误代码设置不同的标题和消息
        const errorMessages = {
            '400': '请求格式不正确',
            '401': '未授权访问',
            '403': '禁止访问',
            '404': '请求的资源不存在',
            '405': '不允许的请求方法',
            '429': '请求频率过高'
        };
        
        if (errorMessages[errorCode]) {
            document.querySelector('h1').textContent = errorMessages[errorCode];
        }
    </script>
</body>
</html>
