/**
 * 播放策略模板模型
 * 用于存储用户的播放策略模板配置
 */
module.exports = (sequelize, DataTypes) => {
  const PlaybackTemplate = sequelize.define(
    "PlaybackTemplate",
    {
      // 主键ID，使用nanoid
      id: {
        type: DataTypes.STRING(21),
        primaryKey: true,
        allowNull: false,
      },

      // 模板名称
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: "模板名称",
      },

      // 模板描述
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "模板描述",
      },

      // 模板配置（JSON格式）
      config: {
        type: DataTypes.JSON,
        allowNull: false,
        comment: "模板配置，包含sections数组",
      },

      // 模板类型：system(系统预设), user(用户创建)
      type: {
        type: DataTypes.ENUM("system", "user"),
        allowNull: false,
        defaultValue: "user",
        comment: "模板类型",
      },

      // 创建者ID（系统模板为NULL）
      userId: {
        type: DataTypes.STRING(21),
        allowNull: true,
        field: "user_id",
        comment: "创建者ID，系统模板为NULL",
      },

      // 是否公开
      isPublic: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: "is_public",
        comment: "是否公开，公开后其他用户可以使用",
      },

      // 使用次数
      usageCount: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        field: "usage_count",
        comment: "使用次数统计",
      },

      // 模板状态：active(活跃), archived(归档)
      status: {
        type: DataTypes.ENUM("active", "archived"),
        allowNull: false,
        defaultValue: "active",
        comment: "模板状态",
      },

      // 创建时间
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        field: "created_at",
        defaultValue: DataTypes.NOW,
        comment: "创建时间",
      },

      // 更新时间
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        field: "updated_at",
        defaultValue: DataTypes.NOW,
        comment: "更新时间",
      },
    },
    {
      tableName: "playback_templates",
      underscored: true,
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      indexes: [
        {
          name: "idx_user_id",
          fields: ["user_id"],
        },
        {
          name: "idx_type",
          fields: ["type"],
        },
        {
          name: "idx_public",
          fields: ["is_public"],
        },
        {
          name: "idx_usage",
          fields: ["usage_count"],
        },
        {
          name: "idx_status",
          fields: ["status"],
        },
        {
          name: "idx_created_at",
          fields: ["created_at"],
        },
      ],
    }
  );

  // 定义关联关系
  PlaybackTemplate.associate = function (models) {
    // 与用户表关联
    PlaybackTemplate.belongsTo(models.User, {
      foreignKey: "userId",
      as: "creator",
    });
  };

  return PlaybackTemplate;
};
