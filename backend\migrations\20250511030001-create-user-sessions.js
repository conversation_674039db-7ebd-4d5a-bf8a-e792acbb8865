'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();
    
    // 如果表已存在，则跳过创建
    if (!tables.includes('user_sessions')) {
      // 创建用户会话表
      await queryInterface.createTable('user_sessions', {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER
        },
        user_id: {
          type: Sequelize.STRING(50),
          allowNull: false,
          comment: '用户ID'
        },
        token_id: {
          type: Sequelize.STRING(100),
          allowNull: false,
          unique: true,
          comment: 'JWT token的唯一标识符'
        },
        device_info: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: '设备信息（浏览器、操作系统等）'
        },
        ip_address: {
          type: Sequelize.STRING(45),
          allowNull: true,
          comment: 'IP地址'
        },
        last_active: {
          type: Sequelize.DATE,
          allowNull: false,
          comment: '最后活跃时间'
        },
        created_at: {
          allowNull: false,
          type: Sequelize.DATE
        },
        expires_at: {
          allowNull: false,
          type: Sequelize.DATE,
          comment: '会话过期时间'
        },
        updated_at: {
          allowNull: false,
          type: Sequelize.DATE
        }
      });

      // 添加索引
      await queryInterface.addIndex('user_sessions', ['user_id']);
      await queryInterface.addIndex('user_sessions', ['token_id']);
      await queryInterface.addIndex('user_sessions', ['expires_at']);
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('user_sessions');
  }
};
