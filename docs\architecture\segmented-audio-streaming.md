# 分段流式音频播放架构设计

## 📋 概述

本文档描述了 Echo Lab 分段流式音频播放的技术架构设计，旨在解决当前音频播放需要等待完整合成的用户体验问题。

## 🎯 设计目标

### 当前问题
- **首次播放时间长**：需要 10-30 秒等待所有音频片段下载和合成
- **内存占用大**：将所有音频合并为一个大的 AudioBuffer
- **用户体验差**：长时间等待，无法快速预览

### 优化目标
- **快速启动**：2-3 秒内开始播放
- **内存友好**：只保持必要的音频片段在内存中
- **功能完整**：保持所有现有功能正常工作（拖动、跳转、倍速等）

## 🏗 架构设计

### 核心策略：永不合成完整音频

```
┌─────────────────────────────────────────────────────────────┐
│                    分段流式播放架构                          │
├─────────────────────────────────────────────────────────────┤
│  虚拟时间轴管理器                                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ VirtualTimeline                                         │ │
│  │ - 维护全局时间映射                                      │ │
│  │ - 计算虚拟总时长                                        │ │
│  │ - 处理时间跳转请求                                      │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  分段音频管理器                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ SegmentedAudioManager                                   │ │
│  │ - 管理音频片段缓存                                      │ │
│  │ - 智能预加载策略                                        │ │
│  │ - 片段间无缝切换                                        │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  播放控制器                                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ StreamingAudioPlayer                                    │ │
│  │ - 统一播放接口                                          │ │
│  │ - 倍速控制                                              │ │
│  │ - 状态同步                                              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 核心组件设计

### 1. SegmentedAudioManager

```javascript
class SegmentedAudioManager {
  constructor(timeline) {
    this.timeline = timeline;
    this.segments = new Map();           // 音频片段缓存
    this.currentSegmentId = null;        // 当前播放片段
    this.preloadQueue = [];              // 预加载队列
    this.virtualDuration = 0;            // 虚拟总时长
    this.globalPlaybackRate = 1.0;       // 全局倍速
  }

  // 初始化：计算虚拟时长，预加载首批片段
  async initialize() {
    this.calculateVirtualDuration();
    await this.preloadInitialSegments();
  }

  // 加载指定片段
  async loadSegment(segmentInfo) {
    if (this.segments.has(segmentInfo.id)) {
      return this.segments.get(segmentInfo.id);
    }

    const audioElement = new Audio();
    audioElement.preload = 'auto';
    audioElement.playbackRate = this.globalPlaybackRate;

    // 处理音频URL和速度变化
    const processedUrl = await this.processSegmentAudio(segmentInfo);
    audioElement.src = processedUrl;

    const segment = {
      id: segmentInfo.id,
      audioElement,
      startTime: segmentInfo.startTime,
      duration: segmentInfo.duration,
      loaded: false
    };

    this.segments.set(segmentInfo.id, segment);
    return segment;
  }

  // 智能预加载策略
  updatePreloadQueue(currentTime) {
    // 基于当前播放位置预测需要的片段
    const currentIndex = this.findSegmentIndexByTime(currentTime);
    const preloadRange = 3; // 预加载前后3个片段

    for (let i = Math.max(0, currentIndex - 1);
         i < Math.min(this.timeline.length, currentIndex + preloadRange);
         i++) {
      const segment = this.timeline[i];
      if (!this.segments.has(segment.id)) {
        this.preloadQueue.push(segment);
      }
    }
  }
}
```

### 2. VirtualTimeline

```javascript
class VirtualTimeline {
  constructor(timeline) {
    this.timeline = timeline;
    this.virtualDuration = 0;
    this.segmentMap = new Map();
    this.buildTimelineMap();
  }

  // 构建时间映射表
  buildTimelineMap() {
    let currentTime = 0;

    this.timeline.forEach((item, index) => {
      const duration = item.duration || 2;

      this.segmentMap.set(item.id, {
        index,
        virtualStartTime: currentTime,
        virtualEndTime: currentTime + duration,
        duration
      });

      currentTime += duration;
    });

    this.virtualDuration = currentTime;
  }

  // 根据虚拟时间查找片段
  findSegmentByVirtualTime(virtualTime) {
    for (const [id, info] of this.segmentMap) {
      if (virtualTime >= info.virtualStartTime &&
          virtualTime < info.virtualEndTime) {
        return {
          id,
          ...info,
          localTime: virtualTime - info.virtualStartTime
        };
      }
    }
    return null;
  }

  // 虚拟时间转换为进度百分比
  timeToProgress(virtualTime) {
    return (virtualTime / this.virtualDuration) * 100;
  }

  // 进度百分比转换为虚拟时间
  progressToTime(progress) {
    return (progress / 100) * this.virtualDuration;
  }
}
```

### 3. StreamingAudioPlayer

```javascript
class StreamingAudioPlayer {
  constructor(timeline) {
    this.segmentManager = new SegmentedAudioManager(timeline);
    this.virtualTimeline = new VirtualTimeline(timeline);
    this.currentAudioElement = null;
    this.isPlaying = false;
    this.virtualCurrentTime = 0;
    this.updateTimer = null;
  }

  // 播放/暂停
  async togglePlay() {
    if (this.isPlaying) {
      await this.pause();
    } else {
      await this.play();
    }
  }

  // 跳转到指定时间
  async seekToTime(virtualTime) {
    const segmentInfo = this.virtualTimeline.findSegmentByVirtualTime(virtualTime);
    if (!segmentInfo) return;

    // 确保目标片段已加载
    await this.segmentManager.loadSegment(this.timeline[segmentInfo.index]);

    // 切换到目标片段
    await this.switchToSegment(segmentInfo.id);

    // 设置片段内的精确时间
    this.currentAudioElement.currentTime = segmentInfo.localTime;
    this.virtualCurrentTime = virtualTime;
  }

  // 设置倍速
  setPlaybackRate(rate) {
    this.segmentManager.globalPlaybackRate = rate;

    // 应用到当前播放的音频元素
    if (this.currentAudioElement) {
      this.currentAudioElement.playbackRate = rate;
    }

    // 应用到所有已加载的片段
    this.segmentManager.segments.forEach(segment => {
      if (segment.audioElement) {
        segment.audioElement.playbackRate = rate;
      }
    });
  }

  // 切换片段
  async switchToSegment(segmentId) {
    const segment = this.segmentManager.segments.get(segmentId);
    if (!segment) return;

    // 暂停当前播放
    if (this.currentAudioElement) {
      this.currentAudioElement.pause();
    }

    // 切换到新片段
    this.currentAudioElement = segment.audioElement;
    this.segmentManager.currentSegmentId = segmentId;

    // 设置事件监听
    this.setupAudioEventListeners();
  }

  // 处理片段播放结束
  onSegmentEnded() {
    const currentIndex = this.virtualTimeline.segmentMap.get(
      this.segmentManager.currentSegmentId
    ).index;

    // 如果不是最后一个片段，自动播放下一个
    if (currentIndex < this.timeline.length - 1) {
      const nextSegment = this.timeline[currentIndex + 1];
      this.switchToSegment(nextSegment.id);
      this.currentAudioElement.play();
    } else {
      // 播放结束
      this.isPlaying = false;
      this.onPlaybackEnded();
    }
  }
}
```

## 🎮 现有功能适配

### 1. 拖动滑动条

```javascript
// 在 VideoPlayerBase.vue 中的适配
const handleProgressChange = (value) => {
  if (!streamingPlayer) return;

  // 将进度百分比转换为虚拟时间
  const virtualTime = streamingPlayer.virtualTimeline.progressToTime(value);

  // 跳转到目标时间
  streamingPlayer.seekToTime(virtualTime);

  // 更新UI状态
  uiState.currentTime = virtualTime;
  updateCurrentIndex();
};
```

### 2. 点击跳转功能

```javascript
const jumpToIndex = async (index) => {
  if (!streamingPlayer || !props.timeline[index]) return;

  const targetItem = props.timeline[index];
  const virtualTime = targetItem.startTime;

  // 跳转到目标时间
  await streamingPlayer.seekToTime(virtualTime);

  // 更新UI状态
  uiState.currentIndex = index;
  uiState.currentTime = virtualTime;

  // 开始播放
  if (!streamingPlayer.isPlaying) {
    await streamingPlayer.play();
  }
};
```

### 3. 倍速功能

```javascript
const setPlaybackRate = (rate) => {
  if (!streamingPlayer) return;

  // 设置流式播放器的倍速
  streamingPlayer.setPlaybackRate(rate);

  // 更新UI状态
  uiState.playbackRate = rate;
};
```

## 📊 性能优化策略

### 1. 智能预加载

```javascript
class PreloadStrategy {
  static STRATEGIES = {
    CONSERVATIVE: { range: 2, maxConcurrent: 2 },
    BALANCED: { range: 3, maxConcurrent: 3 },
    AGGRESSIVE: { range: 5, maxConcurrent: 4 }
  };

  static selectStrategy(networkSpeed, memoryUsage) {
    if (networkSpeed < 1000 || memoryUsage > 0.8) {
      return this.STRATEGIES.CONSERVATIVE;
    } else if (networkSpeed < 5000 || memoryUsage > 0.6) {
      return this.STRATEGIES.BALANCED;
    } else {
      return this.STRATEGIES.AGGRESSIVE;
    }
  }
}
```

### 2. 内存管理

```javascript
class MemoryManager {
  constructor(maxSegments = 10) {
    this.maxSegments = maxSegments;
    this.segmentUsage = new Map(); // 记录片段使用情况
  }

  // 清理不需要的片段
  cleanup(currentSegmentId, keepRange = 3) {
    const currentIndex = this.findSegmentIndex(currentSegmentId);

    this.segmentManager.segments.forEach((segment, id) => {
      const segmentIndex = this.findSegmentIndex(id);
      const distance = Math.abs(segmentIndex - currentIndex);

      // 清理距离当前播放位置较远的片段
      if (distance > keepRange) {
        this.releaseSegment(id);
      }
    });
  }

  releaseSegment(segmentId) {
    const segment = this.segmentManager.segments.get(segmentId);
    if (segment && segment.audioElement) {
      segment.audioElement.src = '';
      segment.audioElement = null;
    }
    this.segmentManager.segments.delete(segmentId);
  }
}
```

## 🔄 迁移策略

### 阶段一：基础架构
1. 创建 `SegmentedAudioManager` 类
2. 实现基础的片段加载和播放功能
3. 保持现有接口兼容

### 阶段二：功能适配
1. 适配拖动滑动条功能
2. 适配点击跳转功能
3. 适配倍速功能

### 阶段三：优化完善
1. 实现智能预加载策略
2. 添加内存管理机制
3. 性能监控和调优

## 📈 预期效果

| 指标 | 当前方案 | 分段流式播放 | 改善幅度 |
|------|----------|--------------|----------|
| 首次播放时间 | 10-30秒 | 2-3秒 | 70-85% ⬇️ |
| 内存占用 | 完整音频大小 | 固定小量 | 80-90% ⬇️ |
| 网络流量 | 一次性下载 | 按需加载 | 30-50% ⬇️ |
| 用户体验 | 长时间等待 | 快速响应 | 显著提升 |

## 🚀 实施计划

### 第一周：核心架构
- [ ] 实现 `SegmentedAudioManager` 基础功能
- [ ] 实现 `VirtualTimeline` 时间映射
- [ ] 创建基础的片段播放逻辑

### 第二周：功能集成
- [ ] 集成到现有播放器组件
- [ ] 适配所有交互功能
- [ ] 确保功能完整性

### 第三周：优化测试
- [ ] 实现智能预加载策略
- [ ] 添加性能监控
- [ ] 全面测试和调优

## 🔍 风险评估

### 技术风险
- **复杂度增加**：需要维护虚拟时间轴和片段状态
- **同步问题**：确保UI状态与实际播放状态一致
- **边界处理**：片段切换时的无缝过渡

### 缓解措施
- 渐进式迁移，保持现有功能作为降级方案
- 充分的单元测试和集成测试
- 详细的错误处理和日志记录

## 📝 总结

分段流式音频播放架构将显著改善 Echo Lab 的用户体验，通过智能的片段管理和虚拟时间轴设计，在保持所有现有功能的同时，大幅提升播放启动速度和内存效率。

## 🔗 相关文档

- [音频处理架构](./audio-processing.md)
- [PWA 缓存策略](../deployment/pwa-cache-strategy.md)
- [播放器组件设计](../components/player-components.md)
- [性能优化指南](../optimization/performance-guide.md)

## 📞 联系方式

如有技术问题或建议，请联系开发团队或在项目 Issue 中讨论。
