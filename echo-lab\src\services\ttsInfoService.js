/**
 * TTS信息服务
 * 提供TTS服务信息的获取和缓存功能
 */
import { audioService } from "./api";

class TtsInfoService {
  constructor() {
    this.cache = null;
    this.currentRequest = null;
  }

  /**
   * 获取TTS服务信息
   * @returns {Promise<Object>} TTS服务信息
   */
  async getTtsInfo() {
    // 如果已有缓存，直接返回
    if (this.cache) {
      return this.cache;
    }

    // 如果已有请求进行中，返回该请求
    if (this.currentRequest) {
      return this.currentRequest;
    }

    // 创建新请求
    this.currentRequest = this._fetchTtsInfo();

    try {
      const result = await this.currentRequest;
      return result;
    } finally {
      this.currentRequest = null;
    }
  }

  /**
   * 从服务器获取TTS服务信息
   * @private
   */
  async _fetchTtsInfo() {
    try {
      const result = await audioService.getTtsServiceInfo();

      // 记录数据结构和大小，用于开发调试
      const dataSize = JSON.stringify(result).length;
      console.log(
        `TTS服务信息获取成功，数据大小: ${(dataSize / 1024).toFixed(2)} KB`
      );
      console.log(
        `语言数量: ${Object.keys(result.languages).length}, 声音数量: ${
          result.voices.length
        }`
      );

      this.cache = result;
      return result;
    } catch (error) {
      console.error("获取TTS服务信息失败:", error);
      throw error;
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache = null;
  }

  /**
   * 同步获取声音名称（从缓存中）
   * @param {number} voiceId 声音数据库ID
   * @returns {string} 声音名称，如果未找到返回fallback
   */
  getVoiceNameSync(voiceId) {

    if (!voiceId) return '';

    // 如果没有缓存，返回fallback
    if (!this.cache || !this.cache.voices) {
      return `声音ID: ${voiceId}`;
    }

    // 确保voiceId是数字类型
    const numericVoiceId = typeof voiceId === 'string' ? parseInt(voiceId, 10) : voiceId;

    // 从缓存中查找声音信息
    const voice = this.cache.voices.find(voice => voice.id === numericVoiceId);
    if (voice) {
      return voice.name || `声音ID: ${voiceId}`;
    }

    return `声音ID: ${voiceId}`;
  }

  /**
   * 获取声音信息
   * @param {number} voiceId 声音数据库ID
   * @returns {Promise<Object|null>} 声音信息对象，包含id、name、speaker_id等属性
   */
  async getVoiceById(voiceId) {
    if (!voiceId) return null;

    try {
      const ttsInfo = await this.getTtsInfo();

      // 确保voiceId是数字类型
      const numericVoiceId =
        typeof voiceId === "string" ? parseInt(voiceId, 10) : voiceId;

      // 查找声音信息
      const voice =
        ttsInfo.voices.find((voice) => voice.id === numericVoiceId) || null;

      if (voice) {
        // 声音对象中已经包含了service_name，直接使用
        const result = {
          ...voice,
          displayName: voice.name, // 人类可读的显示名称
        };

        return result;
      }

      return null;
    } catch (error) {
      console.error("获取声音信息失败:", error);
      return null;
    }
  }

  /**
   * 获取指定语言的所有声音
   * @param {string} language 语言代码
   * @returns {Promise<Array>} 声音信息数组
   */
  async getVoicesForLanguage(language) {
    if (!language) return [];

    try {
      const ttsInfo = await this.getTtsInfo();

      // 获取该语言的声音ID列表
      const languageInfo = ttsInfo.languages[language];
      if (
        !languageInfo ||
        !languageInfo.voice_ids ||
        !languageInfo.voice_ids.length
      ) {
        console.log(`没有找到语言 ${language} 的声音`);
        return [];
      }

      // 根据ID列表从voices数组中获取完整声音信息
      const voices = languageInfo.voice_ids
        .map((id) => ttsInfo.voices.find((voice) => voice.id === id))
        .filter((voice) => voice && !voice.disabled);

      // 声音对象中已经包含了service_name，只需添加displayName
      return voices.map((voice) => {
        return {
          ...voice,
          displayName: voice.name, // 人类可读的显示名称
        };
      });
    } catch (error) {
      console.error("获取语言声音失败:", error);
      return [];
    }
  }

  /**
   * 获取默认映射
   * @returns {Promise<Object>} 默认映射对象
   */
  async getDefaultMappings() {
    try {
      const ttsInfo = await this.getTtsInfo();
      return ttsInfo.defaultMappings || {};
    } catch (error) {
      console.error("获取默认映射失败:", error);
      return {};
    }
  }
}

// 创建单例
const ttsService = new TtsInfoService();

// 导出方法
export const getTtsInfo = () => ttsService.getTtsInfo();
export const getVoiceById = (voiceId) => ttsService.getVoiceById(voiceId);
export const getVoicesForLanguage = (language) =>
  ttsService.getVoicesForLanguage(language);
export const getDefaultMappings = () => ttsService.getDefaultMappings();
export const clearTtsCache = () => ttsService.clearCache();
export const getVoiceNameSync = (voiceId) => ttsService.getVoiceNameSync(voiceId);

/**
 * 获取人类可读的声音名称
 * @param {number} voiceId 声音数据库ID
 * @returns {Promise<string>} 人类可读的声音名称
 */
export const getHumanReadableVoiceName = async (voiceId) => {
  if (!voiceId) return "未知声音";

  try {
    // 确保voiceId是数字类型
    const numericVoiceId =
      typeof voiceId === "string" ? parseInt(voiceId, 10) : voiceId;

    // 获取声音信息
    const voiceInfo = await getVoiceById(numericVoiceId);

    if (voiceInfo) {
      // 使用声音的name属性作为显示名称
      const displayName = voiceInfo.name;

      // 如果需要显示带服务名称的完整名称，可以使用下面的代码
      // const fullDisplayName = `${voiceInfo.service_name} - ${displayName}`;

      return displayName;
    }

    return `声音ID: ${voiceId}`;
  } catch (error) {
    console.error(`获取声音名称失败: ${error.message}`);
    return `声音ID: ${voiceId}`;
  }
};

// 注意：TTS信息现在采用按需加载，只有在真正需要时才会调用API
// 如果需要预加载，请在具体的编辑器页面中调用 getTtsInfo()

// 默认导出
export default {
  getTtsInfo,
  getVoiceById,
  getVoicesForLanguage,
  getDefaultMappings,
  clearTtsCache,
  getHumanReadableVoiceName,
  getVoiceNameSync,
};
