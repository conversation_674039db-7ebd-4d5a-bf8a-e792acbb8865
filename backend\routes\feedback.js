/**
 * 用户反馈路由
 * 处理用户反馈相关的请求
 */
const express = require("express");
const router = express.Router();
const feedbackController = require("../controllers/feedbackController");
const {
  authenticate,
  optionalAuthenticate,
} = require("../middleware/authMiddleware");
const { adminAuth } = require("../middleware/adminMiddleware");

/**
 * 提交反馈
 * POST /api/feedback
 * @body {string} type - 反馈类型（suggestion, bug, feature, other）
 * @body {string} content - 反馈内容
 * @body {string} contact - 联系方式（可选）
 * @body {string} pageUrl - 页面URL（可选）
 */
router.post("/", optionalAuthenticate, feedbackController.submitFeedback);

/**
 * 获取当前用户的反馈列表
 * GET /api/feedback/user
 * @query {number} page - 页码
 * @query {number} pageSize - 每页数量
 * @query {string} status - 状态（可选）
 * @query {string} type - 类型（可选）
 */
router.get("/user", authenticate, feedbackController.getUserFeedbacks);

/**
 * 获取所有反馈（管理员）
 * GET /api/feedback/admin
 * @query {number} page - 页码
 * @query {number} pageSize - 每页数量
 * @query {string} status - 状态（可选）
 * @query {string} type - 类型（可选）
 * @query {string} userId - 用户ID（可选）
 */
router.get("/admin", adminAuth, feedbackController.getAllFeedbacks);

/**
 * 更新反馈状态（管理员）
 * PUT /api/feedback/admin/:id
 * @param {number} id - 反馈ID
 * @body {string} status - 状态
 * @body {string} adminReply - 管理员回复（可选）
 */
router.put("/admin/:id", adminAuth, feedbackController.updateFeedbackStatus);

module.exports = router;
