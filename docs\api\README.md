# Echo Lab API 文档

本文档提供了 Echo Lab 后端 API 的详细说明，包括接口定义、参数说明和返回值格式。

## 基本信息

- **基础URL**: `/api`
- **内容类型**: `application/json`
- **认证方式**: JWT (JSON Web Token)

## 认证相关 API

### 用户注册

创建新用户账号。

- **URL**: `/auth/register`
- **方法**: `POST`
- **认证要求**: 无

**请求参数**:

```json
{
  "username": "用户名",
  "email": "邮箱地址",
  "password": "密码"
}
```

**成功响应** (200):

```json
{
  "id": 1,
  "username": "用户名",
  "email": "邮箱地址",
  "token": "JWT令牌"
}
```

**错误响应** (400):

```json
{
  "message": "用户名已存在"
}
```

### 用户登录

用户登录并获取认证令牌。

- **URL**: `/auth/login`
- **方法**: `POST`
- **认证要求**: 无

**请求参数**:

```json
{
  "username": "用户名",
  "password": "密码"
}
```

**成功响应** (200):

```json
{
  "id": 1,
  "username": "用户名",
  "email": "邮箱地址",
  "token": "JWT令牌"
}
```

**错误响应** (401):

```json
{
  "message": "用户名或密码错误"
}
```

### 获取用户信息

获取当前登录用户的信息。

- **URL**: `/auth/profile`
- **方法**: `GET`
- **认证要求**: 是

**请求头**:

```
Authorization: Bearer JWT令牌
```

**成功响应** (200):

```json
{
  "id": 1,
  "username": "用户名",
  "email": "邮箱地址"
}
```

**错误响应** (401):

```json
{
  "message": "未授权"
}
```

## 内容相关 API

### 创建内容

创建新的内容。

- **URL**: `/content`
- **方法**: `POST`
- **认证要求**: 是

**请求头**:

```
Authorization: Bearer JWT令牌
```

**请求参数**:

```json
{
  "title": "内容标题",
  "description": "内容描述",
  "data": {
    "nodes": {},
    "nodeTypeCounters": {},
    "resources": {}
  }
}
```

**成功响应** (201):

```json
{
  "id": 1,
  "title": "内容标题",
  "description": "内容描述",
  "userId": 1,
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z"
}
```

**错误响应** (500):

```json
{
  "message": "服务器错误"
}
```

### 获取内容列表

获取当前用户的内容列表。

- **URL**: `/content`
- **方法**: `GET`
- **认证要求**: 是

**请求头**:

```
Authorization: Bearer JWT令牌
```

**成功响应** (200):

```json
[
  {
    "id": 1,
    "title": "内容标题1",
    "description": "内容描述1",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  },
  {
    "id": 2,
    "title": "内容标题2",
    "description": "内容描述2",
    "createdAt": "2023-01-02T00:00:00.000Z",
    "updatedAt": "2023-01-02T00:00:00.000Z"
  }
]
```

**错误响应** (500):

```json
{
  "message": "服务器错误"
}
```

### 获取内容详情

获取指定内容的详细信息。

- **URL**: `/content/:id`
- **方法**: `GET`
- **认证要求**: 否

**成功响应** (200):

```json
{
  "id": 1,
  "title": "内容标题",
  "description": "内容描述",
  "data": {
    "nodes": {},
    "nodeTypeCounters": {},
    "resources": {}
  },
  "userId": 1,
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z"
}
```

**错误响应** (404):

```json
{
  "message": "内容不存在"
}
```

### 更新内容

更新指定内容的信息。

- **URL**: `/content/:id`
- **方法**: `PUT`
- **认证要求**: 是

**请求头**:

```
Authorization: Bearer JWT令牌
```

**请求参数**:

```json
{
  "title": "更新的标题",
  "description": "更新的描述",
  "data": {
    "nodes": {},
    "nodeTypeCounters": {},
    "resources": {}
  }
}
```

**成功响应** (200):

```json
{
  "id": 1,
  "title": "更新的标题",
  "description": "更新的描述",
  "userId": 1,
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-02T00:00:00.000Z"
}
```

**错误响应** (404):

```json
{
  "message": "内容不存在"
}
```

### 删除内容

删除指定内容。

- **URL**: `/content/:id`
- **方法**: `DELETE`
- **认证要求**: 是

**请求头**:

```
Authorization: Bearer JWT令牌
```

**成功响应** (200):

```json
{
  "message": "内容已删除"
}
```

**错误响应** (404):

```json
{
  "message": "内容不存在"
}
```

## 资源相关 API

### 生成音频

生成文本对应的音频。

- **URL**: `/resource/audio`
- **方法**: `POST`
- **认证要求**: 否

**请求参数**:

```json
{
  "text": "要转换为语音的文本",
  "language": "ja",
  "ignoreCache": false
}
```

**成功响应** (200):

```json
{
  "url": "https://example.com/audio/abc123.mp3",
  "duration": 2.5
}
```

**错误响应** (500):

```json
{
  "message": "服务器错误"
}
```

### 生成翻译

生成文本的翻译。

- **URL**: `/resource/translation`
- **方法**: `POST`
- **认证要求**: 否

**请求参数**:

```json
{
  "text": "要翻译的文本",
  "sourceLanguage": "ja",
  "targetLanguage": "zh-CN",
  "ignoreCache": false
}
```

**成功响应** (200):

```json
{
  "translation": "翻译后的文本"
}
```

**错误响应** (500):

```json
{
  "message": "服务器错误"
}
```

### 生成标注

生成文本的标注（如振り仮名）。

- **URL**: `/resource/annotation`
- **方法**: `POST`
- **认证要求**: 否

**请求参数**:

```json
{
  "text": "要标注的文本",
  "language": "ja",
  "method": "furigana",
  "ignoreCache": false
}
```

**成功响应** (200):

```json
{
  "annotation": "标注后的文本"
}
```

**错误响应** (500):

```json
{
  "message": "服务器错误"
}
```

### 上传文件

上传文件（如图片）。

- **URL**: `/resource/upload`
- **方法**: `POST`
- **认证要求**: 是
- **内容类型**: `multipart/form-data`

**请求头**:

```
Authorization: Bearer JWT令牌
```

**请求参数**:

```
file: 文件数据
type: 文件类型（如 image）
```

**成功响应** (200):

```json
{
  "url": "https://example.com/images/abc123.jpg"
}
```

**错误响应** (500):

```json
{
  "message": "服务器错误"
}
```

## 反馈相关 API

### 创建反馈

创建新的用户反馈。

- **URL**: `/feedback`
- **方法**: `POST`
- **认证要求**: 否

**请求参数**:

```json
{
  "content": "反馈内容",
  "email": "联系邮箱（可选）"
}
```

**成功响应** (201):

```json
{
  "id": 1,
  "content": "反馈内容",
  "email": "联系邮箱",
  "status": "pending",
  "createdAt": "2023-01-01T00:00:00.000Z"
}
```

**错误响应** (500):

```json
{
  "message": "服务器错误"
}
```

### 获取用户反馈列表

获取当前用户的反馈列表。

- **URL**: `/feedback`
- **方法**: `GET`
- **认证要求**: 是

**请求头**:

```
Authorization: Bearer JWT令牌
```

**成功响应** (200):

```json
[
  {
    "id": 1,
    "content": "反馈内容1",
    "status": "pending",
    "createdAt": "2023-01-01T00:00:00.000Z"
  },
  {
    "id": 2,
    "content": "反馈内容2",
    "status": "completed",
    "createdAt": "2023-01-02T00:00:00.000Z"
  }
]
```

**错误响应** (500):

```json
{
  "message": "服务器错误"
}
```

### 获取所有反馈（管理员）

获取所有用户的反馈列表（仅管理员）。

- **URL**: `/feedback/admin`
- **方法**: `GET`
- **认证要求**: 是（管理员）

**请求头**:

```
Authorization: Bearer JWT令牌
```

**成功响应** (200):

```json
[
  {
    "id": 1,
    "content": "反馈内容1",
    "email": "<EMAIL>",
    "status": "pending",
    "userId": 1,
    "createdAt": "2023-01-01T00:00:00.000Z"
  },
  {
    "id": 2,
    "content": "反馈内容2",
    "email": "<EMAIL>",
    "status": "completed",
    "userId": 2,
    "createdAt": "2023-01-02T00:00:00.000Z"
  }
]
```

**错误响应** (403):

```json
{
  "message": "权限不足"
}
```

### 更新反馈状态（管理员）

更新指定反馈的状态（仅管理员）。

- **URL**: `/feedback/:id`
- **方法**: `PUT`
- **认证要求**: 是（管理员）

**请求头**:

```
Authorization: Bearer JWT令牌
```

**请求参数**:

```json
{
  "status": "completed"
}
```

**成功响应** (200):

```json
{
  "id": 1,
  "content": "反馈内容",
  "status": "completed",
  "updatedAt": "2023-01-02T00:00:00.000Z"
}
```

**错误响应** (403):

```json
{
  "message": "权限不足"
}
```

## 错误码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器错误 |

## 认证说明

大多数 API 需要认证才能访问。认证通过 JWT 令牌实现，令牌需要在请求头的 `Authorization` 字段中提供，格式为 `Bearer {token}`。

登录成功后，服务器会返回 JWT 令牌，客户端需要保存该令牌并在后续请求中使用。令牌有效期为一年。
