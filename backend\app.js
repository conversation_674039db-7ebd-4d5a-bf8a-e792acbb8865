/**
 * 后端应用入口文件
 * 集成MySQL数据库和阿里云OSS
 * 增强安全性，防止接口刷新和攻击
 */
const express = require("express");
const cors = require("cors");
const path = require("path");
const dotenv = require("dotenv");
const { Sequelize } = require("sequelize");
const fs = require("fs");

// 根据环境加载不同的配置文件
const env = process.env.NODE_ENV || "development";
const envFile = env === "development" ? ".env.development" : ".env";
const envPath = path.resolve(__dirname, envFile);

// 加载环境变量
dotenv.config({ path: envPath });
console.log(`加载环境配置: ${envFile}, NODE_ENV: ${env}`);

// 创建Express应用
const app = express();
const port = process.env.PORT || 3000;

// 设置超时时间 - 5分钟（普通API）
const timeout = require("connect-timeout");
app.use(timeout("300s")); // 设置请求超时时间为5分钟

// 导入安全中间件
const { ipFilter } = require("./middleware/ipFilterMiddleware");
const rateLimiters = require("./middleware/rateLimitMiddleware");
const {
  securityHeaders,
  apiSecurityHeaders,
} = require("./middleware/securityHeadersMiddleware");
const loggers = require("./middleware/requestLoggerMiddleware");

// 应用基本安全中间件
app.use(ipFilter); // IP过滤
app.use(securityHeaders); // 安全头部
app.use(rateLimiters.global); // 全局请求频率限制
app.use(...loggers.api); // 请求日志

// 基本中间件
app.use(
  cors({
    origin: process.env.CORS_ORIGIN || "*",
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: [
      "Content-Type",
      "Authorization",
      "X-Requested-With",
      "X-CSRF-Token",
      "X-Request-ID",
    ],
    credentials: true,
    maxAge: 86400, // 预检请求缓存24小时
  })
);
app.use(express.json({ limit: "100mb" }));
app.use(express.urlencoded({ extended: true, limit: "100mb" }));
app.use(haltOnTimedout); // 超时处理中间件

// 超时处理函数
function haltOnTimedout(req, res, next) {
  if (!req.timedout) next();
}

// 静态文件目录
app.use(express.static(path.join(__dirname, "public")));

// 创建必要的目录
const dirsToCreate = [
  path.join(__dirname, "temp"),
  path.join(__dirname, "logs"),
  path.join(__dirname, "config"),
];

dirsToCreate.forEach((dir) => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`创建目录: ${dir}`);
  }
});

// 数据库连接
const db = require("./models");
// 将数据库实例添加到应用中，以便在中间件中访问
app.set("db", db);

// 数据库连接测试
db.sequelize
  .authenticate()
  .then(() => {
    console.log("数据库连接成功");
  })
  .catch((err) => {
    console.error("数据库连接失败:", err);
  });

// 健康检查路由 - 不需要任何安全限制
app.get("/health", (_, res) => {
  res.json({ status: "ok", time: new Date().toISOString() });
});

// CSRF令牌路由 - 不需要认证
app.get("/api/csrf-token", (req, res) => {
  res.json({
    success: true,
    csrfToken: req.csrfToken ? req.csrfToken() : null,
  });
});

// API路由 - 应用特定的安全限制
// TTS相关路由 - 应用TTS频率限制
app.use("/api/tts", rateLimiters.tts, require("./routes/tts"));
app.use("/api/tts", rateLimiters.tts, require("./routes/ttsInfo").router);

// 内容处理路由
app.use("/api/annotate", require("./routes/annotate"));
app.use("/api/translate", require("./routes/translate"));
app.use("/api/contents", require("./routes/contents"));

// 合集路由
app.use("/api/collections", require("./routes/collections"));

// 用户空间路由
app.use("/api/users", require("./routes/users"));

// 公开内容路由 - 不需要认证，但需要基本的频率限制
app.use("/api/public/contents", require("./routes/public-contents"));

// 特殊词汇路由
app.use("/api/special-words", require("./routes/specialWords"));

// 图片上传路由
app.use("/api/images", require("./routes/imageRoutes"));

// 音频管理路由
app.use("/api/audio", require("./routes/audio"));

// 认证路由 - 应用认证频率限制
app.use("/api/auth", rateLimiters.auth, require("./routes/auth"));

// 会话管理路由
app.use("/api/sessions", require("./routes/sessions"));

// 用户等级和订阅路由
app.use("/api/user-levels", require("./routes/userLevels"));

// 权限管理路由
app.use("/api/permissions", require("./routes/permissions"));

// 管理员路由 - 应用管理员频率限制
app.use("/api/admin", rateLimiters.admin, require("./routes/admin"));

// SEO路由 - sitemap和robots.txt
app.use("/api/seo", require("./routes/seo"));

// 管理员功能权限路由
app.use(
  "/api/admin/feature-permissions",
  rateLimiters.admin,
  require("./routes/admin/featurePermissions")
);

// 安全管理路由
app.use(
  "/api/admin/security",
  rateLimiters.admin,
  require("./routes/admin/securityRoutes")
);

// 功能权限路由
app.use(
  "/api/feature-permissions",
  require("./routes/featurePermissionsRoutes")
);

// 用户反馈路由
app.use("/api/feedback", require("./routes/feedback"));

// 收藏路由
app.use("/api/favorites", require("./routes/favorites"));

// 播放策略模板路由
app.use("/api/templates", require("./routes/playbackTemplates"));

// 错误统计路由
app.use("/api/errors", require("./routes/errorStatistics"));

// 签到路由
app.use("/api/checkin", require("./routes/checkIn"));

// 404处理中间件 - 必须在所有路由之后
app.use((_, res) => {
  res.status(404).json({
    success: false,
    error: "请求的资源不存在",
    code: "RESOURCE_NOT_FOUND",
  });
});

// 错误日志中间件
app.use(loggers.error);

// 错误处理中间件
app.use((err, _, res) => {
  // 根据错误类型返回不同的状态码
  let statusCode = 500;
  let errorMessage = "服务器内部错误";
  let errorCode = "INTERNAL_SERVER_ERROR";

  // 处理常见错误类型
  if (err.name === "ValidationError") {
    statusCode = 400;
    errorMessage = "请求参数验证失败";
    errorCode = "VALIDATION_ERROR";
  } else if (
    err.name === "UnauthorizedError" ||
    err.name === "JsonWebTokenError"
  ) {
    statusCode = 401;
    errorMessage = "未授权，请先登录";
    errorCode = "UNAUTHORIZED";
  } else if (err.name === "ForbiddenError") {
    statusCode = 403;
    errorMessage = "权限不足，无法访问";
    errorCode = "FORBIDDEN";
  } else if (err.name === "NotFoundError") {
    statusCode = 404;
    errorMessage = "请求的资源不存在";
    errorCode = "NOT_FOUND";
  } else if (err.code === "LIMIT_FILE_SIZE") {
    statusCode = 413;
    errorMessage = "上传的文件过大";
    errorCode = "FILE_TOO_LARGE";
  }

  // 开发环境下返回详细错误信息
  const error =
    process.env.NODE_ENV === "development"
      ? { message: errorMessage, detail: err.message, stack: err.stack }
      : errorMessage;

  console.error(`[Error] ${statusCode} ${errorCode}: ${err.message}`);

  res.status(statusCode).json({
    success: false,
    error,
    code: errorCode,
  });
});

// 启动服务器
const server = app.listen(port, () => {
  console.log(`服务器运行在 http://localhost:${port}`);
  console.log(`环境: ${process.env.NODE_ENV}`);
  console.log(`安全防护: IP过滤、请求频率限制、安全头部、请求日志`);
});

// 优雅关闭
process.on("SIGTERM", gracefulShutdown);
process.on("SIGINT", gracefulShutdown);

function gracefulShutdown() {
  console.log("正在关闭服务器...");
  server.close(() => {
    console.log("服务器已关闭");
    process.exit(0);
  });

  // 如果10秒后仍未关闭，则强制退出
  setTimeout(() => {
    console.error("无法正常关闭服务器，强制退出");
    process.exit(1);
  }, 10000);
}

module.exports = app;
