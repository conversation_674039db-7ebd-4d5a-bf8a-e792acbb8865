<!--
  管理后台布局组件
  用于管理后台的整体布局
-->
<template>
  <div class="admin-layout">
    <!-- 侧边栏 -->
    <div class="admin-sidebar" :class="{ 'collapsed': sidebarCollapsed }">
      <div class="sidebar-header">
        <img src="@/assets/logo.jpg" alt="Echo Lab" class="sidebar-logo" />
        <h1 class="sidebar-title" v-if="!sidebarCollapsed">Echo Lab 管理后台</h1>
        <el-icon class="sidebar-toggle" @click="toggleSidebar">
          <i-ep-fold v-if="!sidebarCollapsed" />
          <i-ep-expand v-else />
        </el-icon>
      </div>

      <el-menu :default-active="activeMenu" class="sidebar-menu" :collapse="sidebarCollapsed"
        :collapse-transition="false" @select="handleMenuSelect">
        <el-menu-item index="/admin">
          <el-icon>
            <i-ep-data-board />
          </el-icon>
          <template #title>控制台</template>
        </el-menu-item>

        <el-sub-menu index="user-management">
          <template #title>
            <el-icon>
              <i-ep-user />
            </el-icon>
            <span>用户管理</span>
          </template>
          <el-menu-item index="/admin/users">
            <el-icon>
              <i-ep-user-filled />
            </el-icon>
            <span>用户列表</span>
          </el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="content-management">
          <template #title>
            <el-icon>
              <i-ep-document />
            </el-icon>
            <span>内容管理</span>
          </template>
          <el-menu-item index="/admin/contents">
            <el-icon>
              <i-ep-files />
            </el-icon>
            <span>内容列表</span>
          </el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="membership-management">
          <template #title>
            <el-icon>
              <i-ep-ticket />
            </el-icon>
            <span>会员管理</span>
          </template>
          <el-menu-item index="/admin/user-levels">
            <el-icon>
              <i-ep-user />
            </el-icon>
            <span>用户等级管理</span>
          </el-menu-item>
          <el-menu-item index="/admin/subscriptions">
            <el-icon>
              <i-ep-money />
            </el-icon>
            <span>用户订阅管理</span>
          </el-menu-item>
          <el-menu-item index="/admin/usage-statistics">
            <el-icon>
              <i-ep-data-line />
            </el-icon>
            <span>使用统计</span>
          </el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="system-management">
          <template #title>
            <el-icon>
              <i-ep-setting />
            </el-icon>
            <span>系统管理</span>
          </template>
          <el-menu-item index="/admin/feature-permissions">
            <el-icon>
              <i-ep-lock />
            </el-icon>
            <span>功能权限管理</span>
          </el-menu-item>
          <el-menu-item index="/admin/special-words">
            <el-icon>
              <i-ep-chat-dot-round />
            </el-icon>
            <span>特殊词管理</span>
          </el-menu-item>
          <el-menu-item index="/admin/feedback">
            <el-icon>
              <i-ep-chat-line-round />
            </el-icon>
            <span>用户反馈管理</span>
          </el-menu-item>
          <el-menu-item index="/admin/security">
            <el-icon>
              <i-ep-lock />
            </el-icon>
            <span>安全管理</span>
          </el-menu-item>
          <el-menu-item index="/admin/seo">
            <el-icon>
              <i-ep-search />
            </el-icon>
            <span>SEO管理</span>
          </el-menu-item>
          <el-menu-item index="/admin/error-statistics">
            <el-icon>
              <i-ep-warning-filled />
            </el-icon>
            <span>错误统计</span>
          </el-menu-item>
          <el-menu-item index="/admin/logs">
            <el-icon>
              <i-ep-list />
            </el-icon>
            <span>操作日志</span>
          </el-menu-item>
        </el-sub-menu>
      </el-menu>
    </div>

    <!-- 主内容区 -->
    <div class="admin-content" :class="{ 'expanded': sidebarCollapsed }">
      <!-- 顶部导航栏 -->
      <div class="admin-header">
        <div class="header-left">
          <h2 class="page-title">{{ pageTitle }}</h2>
        </div>

        <div class="header-right">
          <el-dropdown trigger="click" @command="handleCommand">
            <div class="admin-user">
              <el-avatar :size="32" :src="avatarUrl">
                {{ userInitials }}
              </el-avatar>
              <span class="admin-username">{{ displayName }}</span>
              <el-icon class="el-icon--right">
                <i-ep-arrow-down />
              </el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon>
                    <i-ep-user />
                  </el-icon>
                  个人信息
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon>
                    <i-ep-switch-button />
                  </el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 页面内容 -->
      <div class="admin-main">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/stores/userStore';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

// 侧边栏状态
const sidebarCollapsed = ref(localStorage.getItem('adminSidebarCollapsed') === 'true');

// 当前激活的菜单项
const activeMenu = computed(() => route.path);

// 页面标题
const pageTitle = computed(() => {
  const path = route.path;
  if (path === '/admin') return '控制台';
  if (path === '/admin/users') return '用户管理';
  if (path.startsWith('/admin/users/')) return '用户详情';
  if (path === '/admin/contents') return '内容管理';
  if (path === '/admin/feature-permissions') return '功能权限管理';
  if (path === '/admin/special-words') return '特殊词管理';
  if (path === '/admin/feedback') return '用户反馈管理';
  if (path === '/admin/security') return '安全管理';
  if (path === '/admin/logs') return '操作日志';
  if (path === '/admin/user-levels') return '用户等级管理';
  if (path === '/admin/subscriptions') return '用户订阅管理';
  if (path === '/admin/usage-statistics') return '使用统计';
  if (path === '/admin/seo') return 'SEO管理';
  if (path === '/admin/error-statistics') return '错误统计';
  return 'Echo Lab 管理后台';
});

// 用户信息
const avatarUrl = computed(() => userStore.user?.avatarUrl || '');
const displayName = computed(() => userStore.user?.username || userStore.user?.email || '管理员');
const userInitials = computed(() => {
  const user = userStore.user;
  if (!user) return 'A';
  if (user.username) return user.username.substring(0, 1).toUpperCase();
  return user.email.substring(0, 1).toUpperCase();
});

// 切换侧边栏
function toggleSidebar() {
  sidebarCollapsed.value = !sidebarCollapsed.value;
  localStorage.setItem('adminSidebarCollapsed', sidebarCollapsed.value);
}

// 处理菜单选择
function handleMenuSelect(index) {
  router.push(index);
}

// 处理下拉菜单命令
function handleCommand(command) {
  switch (command) {
    case 'profile':
      router.push('/profile');
      break;
    case 'logout':
      ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        userStore.logout();
        router.push('/login');
      }).catch(() => { });
      break;
  }
}

// 检查用户是否是管理员
onMounted(() => {
  if (!userStore.isAdmin) {
    ElMessageBox.alert('您没有管理员权限，无法访问管理后台', '权限不足', {
      confirmButtonText: '返回首页',
      callback: () => {
        router.push('/');
      }
    });
  }
});

// 监听路由变化，更新激活的菜单项
watch(() => route.path, () => {
  // 可以在这里添加额外的路由处理逻辑
});
</script>

<style scoped>
.admin-layout {
  display: flex;
  height: 100vh;
  width: 100%;
  background-color: #f5f7fa;
}

/* 侧边栏样式 */
.admin-sidebar {
  width: 16rem;
  height: 100%;
  background-color: #304156;
  transition: width 0.3s;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.admin-sidebar.collapsed {
  width: 4rem;
}

.sidebar-header {
  height: 4rem;
  display: flex;
  align-items: center;
  padding: 0 1rem;
  color: #fff;
  background-color: #263445;
}

.sidebar-logo {
  width: 2rem;
  height: 2rem;
  margin-right: 0.75rem;
}

.sidebar-title {
  font-size: 1.125rem;
  margin: 0;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-toggle {
  cursor: pointer;
  font-size: 1.25rem;
  color: #a7b1c2;
}

.sidebar-toggle:hover {
  color: #fff;
}

.sidebar-menu {
  flex: 1;
  border-right: none;
  background-color: #304156;
}

.sidebar-menu :deep(.el-menu-item),
.sidebar-menu :deep(.el-sub-menu__title) {
  color: #a7b1c2;
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  color: #409eff;
  background-color: #263445;
}

.sidebar-menu :deep(.el-menu-item:hover),
.sidebar-menu :deep(.el-sub-menu__title:hover) {
  background-color: #263445;
}

/* 主内容区样式 */
.admin-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: margin-left 0.3s;
}

.admin-content.expanded {
  margin-left: 0;
}

.admin-header {
  height: 4rem;
  background-color: #fff;
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1.5rem;
}

.header-left {
  display: flex;
  align-items: center;
}

.page-title {
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0;
  color: #303133;
}

.header-right {
  display: flex;
  align-items: center;
}

.admin-user {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.admin-username {
  margin: 0 0.5rem;
  font-size: 0.875rem;
  color: #606266;
}

.admin-main {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

/* 响应式布局 - 使用类名而非媒体查询 */
.mobile-device .admin-sidebar {
  position: fixed;
  z-index: 1000;
  box-shadow: 0.25rem 0 0.5rem rgba(0, 0, 0, 0.1);
}

.mobile-device .admin-sidebar.collapsed {
  transform: translateX(-100%);
}

.mobile-device .admin-content {
  margin-left: 0;
}

.mobile-device .admin-header {
  padding: 0 1rem;
}

.mobile-device .admin-main {
  padding: 1rem;
}
</style>
