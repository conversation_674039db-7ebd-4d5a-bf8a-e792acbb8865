/**
 * 全局播放策略组合式函数
 */
import { ref, computed } from 'vue';
import globalPlaybackService from '@/services/globalPlaybackService';
import { ElMessage } from 'element-plus';

export function useGlobalPlayback() {
  const globalTemplate = ref(null);
  const isGlobalTemplateActive = ref(false);

  // 加载全局模板
  const loadGlobalTemplate = async () => {
    try {
      globalTemplate.value = await globalPlaybackService.getGlobalTemplate();
      return globalTemplate.value;
    } catch (error) {
      console.error('加载全局播放策略失败:', error);
      return null;
    }
  };

  // 应用全局策略到配置
  const applyGlobalTemplateToConfig = (serverConfig, contentId) => {
    // 检查是否应该应用全局策略
    if (!globalPlaybackService.shouldApplyGlobalTemplate(contentId)) {
      isGlobalTemplateActive.value = false;
      return serverConfig;
    }

    if (!globalTemplate.value) {
      isGlobalTemplateActive.value = false;
      return serverConfig;
    }

    // 直接应用全局模板，不做任何匹配处理
    isGlobalTemplateActive.value = true;
    return globalPlaybackService.applyGlobalTemplate(serverConfig, globalTemplate.value);
  };

  // 禁用全局策略（用户手动修改时）
  const disableGlobalTemplate = (contentId) => {
    isGlobalTemplateActive.value = false;
    // 标记此内容有特定设置
    localStorage.setItem(`template_${contentId}`, 'custom');
  };

  // 重新启用全局策略
  const enableGlobalTemplate = (contentId) => {
    localStorage.removeItem(`template_${contentId}`);
    isGlobalTemplateActive.value = true;
  };

  // 全局策略状态信息
  const globalTemplateInfo = computed(() => {
    if (!globalTemplate.value) return null;
    
    return {
      name: globalTemplate.value.name,
      isActive: isGlobalTemplateActive.value,
      sectionCount: globalTemplate.value.config.sections?.length || 0
    };
  });

  return {
    globalTemplate,
    isGlobalTemplateActive,
    globalTemplateInfo,
    loadGlobalTemplate,
    applyGlobalTemplateToConfig,
    disableGlobalTemplate,
    enableGlobalTemplate
  };
}