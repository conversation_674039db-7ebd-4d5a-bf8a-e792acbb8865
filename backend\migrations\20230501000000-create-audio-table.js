'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('audios', {
      id: {
        type: Sequelize.BIGINT.UNSIGNED,
        autoIncrement: true,
        primaryKey: true
      },
      text: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      language: {
        type: Sequelize.STRING(10),
        allowNull: false
      },
      speed: {
        type: Sequelize.FLOAT,
        defaultValue: 1.0
      },
      speaker: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      oss_url: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      oss_key: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      duration: {
        type: Sequelize.FLOAT,
        allowNull: false
      },
      md5_hash: {
        type: Sequelize.STRING(32),
        allowNull: false
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 添加索引
    await queryInterface.addIndex('audios', ['md5_hash'], {
      name: 'idx_md5_hash'
    });
    await queryInterface.addIndex('audios', ['language'], {
      name: 'idx_language'
    });
    await queryInterface.addIndex('audios', ['created_at'], {
      name: 'idx_created_at'
    });
  },

  down: async (queryInterface) => {
    await queryInterface.dropTable('audios');
  }
};
