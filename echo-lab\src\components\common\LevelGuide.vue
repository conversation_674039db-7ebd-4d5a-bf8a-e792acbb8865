<!--
  等级选择引导组件
  引导用户选择学习等级（支持多语言）
-->
<template>
  <el-dialog v-model="visible" :title="`选择你的${currentLanguageLabel}水平`" :width="isMobile ? '90%' : '32rem'"
    :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false" class="level-guide-dialog">
    <div class="level-guide">
      <div class="guide-header">
        <div class="language-indicator">
          <span class="language-flag">{{ getLanguageFlag(currentLanguage) }}</span>
          <span class="language-name">{{ currentLanguageLabel }}</span>
        </div>
        <p class="guide-text">为了给你推荐合适的学习内容，请选择你的{{ currentLanguageLabel }}水平：</p>
      </div>

      <div class="level-options">
        <div v-for="level in currentLanguageLevels" :key="level.key" class="level-option"
          :class="{ 'selected': selectedLevel === level.key }" @click="handleLevelSelect(level.key)">
          <div class="level-name">{{ level.name }}</div>
          <div class="level-desc">{{ level.description }}</div>
          <div class="selection-indicator">
            <el-icon v-if="selectedLevel === level.key" class="check-icon">
              <i-ep-check />
            </el-icon>
          </div>
        </div>
      </div>

      <div class="guide-actions">
        <el-button @click="handleBack" size="large" v-if="showBackButton">
          <el-icon class="el-icon--left">
            <i-ep-arrow-left />
          </el-icon>
          上一步
        </el-button>
        <el-button @click="handleSkip" size="large">跳过</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="!selectedLevel" size="large">
          开始学习
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue';
import { isMobileDevice } from '@/utils/deviceDetector';
import { setUserLevel } from '@/utils/userPreferences';
import { getLanguageLabel } from '@/config/languages';
import { getLanguageLevels, getLanguageFlag } from '@/config/languageLevels';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  currentLanguage: {
    type: String,
    required: true
  },
  selectedLevel: {
    type: String,
    default: null
  },
  showBackButton: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'update:selectedLevel', 'completed', 'back']);

// 响应式状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const selectedLevel = computed({
  get: () => props.selectedLevel,
  set: (value) => emit('update:selectedLevel', value)
});

// 设备检测
const isMobile = computed(() => isMobileDevice());

// 当前语言相关计算属性
const currentLanguageLabel = computed(() => {
  return getLanguageLabel(props.currentLanguage);
});

const currentLanguageLevels = computed(() => {
  return getLanguageLevels(props.currentLanguage);
});

// 处理等级选择
function handleLevelSelect(levelKey) {
  selectedLevel.value = levelKey;
}

// 处理返回上一步
function handleBack() {
  emit('back');
}

// 处理跳过
function handleSkip() {
  selectedLevel.value = null;
  visible.value = false;
  emit('completed', null);
}

// 处理确认
function handleConfirm() {
  if (!selectedLevel.value) return;

  // 保存用户等级
  setUserLevel(selectedLevel.value);

  visible.value = false;
  emit('completed', selectedLevel.value);
}
</script>

<style scoped>
.level-guide {
  padding: 1rem 0;
}

.guide-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.language-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.language-flag {
  font-size: 1.5rem;
}

.language-name {
  font-size: 1rem;
  font-weight: 600;
  color: #409eff;
}

.guide-text {
  color: #606266;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

.level-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 2rem;
}

.level-option {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 2px solid #e4e7ed;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
}

.level-option:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.level-option.selected {
  border-color: #409eff;
  background: #ecf5ff;
  box-shadow: 0 0 0 1px #409eff;
}

.level-name {
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
  margin-bottom: 0.25rem;
  flex-shrink: 0;
  min-width: 3rem;
}

.level-desc {
  font-size: 0.75rem;
  color: #909399;
  flex: 1;
  margin-left: 1rem;
}

.selection-indicator {
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.check-icon {
  color: #409eff;
  font-size: 1.25rem;
}

.guide-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

/* 移动端优化 */
.mobile-layout .level-option {
  padding: 0.875rem;
}

.mobile-layout .level-name {
  font-size: 0.9rem;
}

.mobile-layout .level-desc {
  font-size: 0.8rem;
}
</style>