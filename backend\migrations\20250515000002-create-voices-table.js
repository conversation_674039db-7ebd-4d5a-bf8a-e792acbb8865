"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();

    // 如果表已存在，则跳过创建
    if (!tables.includes("voices")) {
      // 创建声音表
      await queryInterface.createTable("voices", {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER,
        },
        speaker_id: {
          type: Sequelize.STRING(50),
          allowNull: false,
          comment: "声音ID，如 ja-JP-Wavenet-A, 4100 等",
        },
        service_id: {
          type: Sequelize.STRING(20),
          allowNull: false,
          comment: "服务ID，如 google, baidu 等",
        },
        language_code: {
          type: Sequelize.STRING(10),
          allowNull: false,
          comment: "语言代码，如 ja, zh-CN 等",
        },
        name: {
          type: Sequelize.STRING(50),
          allowNull: false,
          comment: "声音名称",
        },
        gender: {
          type: Sequelize.ENUM("male", "female"),
          allowNull: false,
          defaultValue: "female",
          comment: "性别",
        },
        is_premium: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
          comment: "是否为高级声音",
        },
        disabled: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
          comment: "是否禁用",
        },
        api_params: {
          type: Sequelize.JSON,
          allowNull: true,
          comment: "API参数，如 per 等",
        },
        created_at: {
          allowNull: false,
          type: Sequelize.DATE,
          defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
        },
        updated_at: {
          allowNull: false,
          type: Sequelize.DATE,
          defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
        },
      });

      // 添加索引
      await queryInterface.addIndex("voices", ["speaker_id"]);
      await queryInterface.addIndex("voices", ["service_id"]);
      await queryInterface.addIndex("voices", ["language_code"]);
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("voices");
  },
};
