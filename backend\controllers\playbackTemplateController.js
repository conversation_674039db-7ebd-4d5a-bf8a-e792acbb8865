/**
 * 播放策略模板控制器
 * 处理模板相关的HTTP请求
 */
const playbackTemplateService = require("../services/playbackTemplateService");

class PlaybackTemplateController {
  /**
   * 获取模板列表
   * GET /api/templates
   */
  async getTemplates(req, res) {
    try {
      const { type, isPublic } = req.query;
      const userId = req.user?.id; // 可选的用户ID，未登录时为undefined

      const options = {
        type,
        isPublic: isPublic !== undefined ? isPublic === "true" : undefined,
      };

      const result = await playbackTemplateService.getTemplates(options);

      // 按类型分组返回，在控制器层进行用户权限筛选
      const groupedTemplates = {
        system: result.templates.filter((t) => t.type === "system"),
        user: userId
          ? result.templates.filter(
              (t) => t.type === "user" && t.userId === userId
            )
          : [], // 未登录用户没有个人模板
        public: result.templates.filter(
          (t) => t.type === "user" && t.isPublic && t.userId !== userId
        ),
      };

      res.json({
        success: true,
        data: groupedTemplates,
      });
    } catch (error) {
      console.error("获取模板列表失败:", error);
      res.status(500).json({
        success: false,
        error: `获取模板列表失败: ${error.message}`,
      });
    }
  }

  /**
   * 获取单个模板
   * GET /api/templates/:id
   */
  async getTemplateById(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      const template = await playbackTemplateService.getTemplateById(
        id,
        userId
      );

      if (!template) {
        return res.status(404).json({
          success: false,
          error: "模板不存在或无权限访问",
        });
      }

      res.json({
        success: true,
        data: template,
      });
    } catch (error) {
      console.error("获取模板失败:", error);
      res.status(500).json({
        success: false,
        error: `获取模板失败: ${error.message}`,
      });
    }
  }

  /**
   * 创建模板
   * POST /api/templates
   */
  async createTemplate(req, res) {
    try {
      const { name, description, config, isPublic } = req.body;
      const userId = req.user.id;

      // 验证必要参数
      if (!name) {
        return res.status(400).json({
          success: false,
          error: "缺少必要参数: name",
        });
      }

      if (!config) {
        return res.status(400).json({
          success: false,
          error: "缺少必要参数: config",
        });
      }

      const template = await playbackTemplateService.createTemplate(
        {
          name,
          description,
          config,
          isPublic,
        },
        userId
      );

      res.status(201).json({
        success: true,
        data: template,
      });
    } catch (error) {
      console.error("创建模板失败:", error);
      res.status(400).json({
        success: false,
        error: `创建模板失败: ${error.message}`,
      });
    }
  }

  /**
   * 更新模板
   * PUT /api/templates/:id
   */
  async updateTemplate(req, res) {
    try {
      const { id } = req.params;
      const updateData = req.body;
      const userId = req.user.id;

      const template = await playbackTemplateService.updateTemplate(
        id,
        updateData,
        userId
      );

      res.json({
        success: true,
        data: template,
      });
    } catch (error) {
      console.error("更新模板失败:", error);
      const statusCode = error.message.includes("权限不足")
        ? 403
        : error.message.includes("不存在")
        ? 404
        : 400;
      res.status(statusCode).json({
        success: false,
        error: `更新模板失败: ${error.message}`,
      });
    }
  }

  /**
   * 删除模板
   * DELETE /api/templates/:id
   */
  async deleteTemplate(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      await playbackTemplateService.deleteTemplate(id, userId);

      res.json({
        success: true,
        message: "模板删除成功",
      });
    } catch (error) {
      console.error("删除模板失败:", error);
      const statusCode = error.message.includes("权限不足")
        ? 403
        : error.message.includes("不存在")
        ? 404
        : 500;
      res.status(statusCode).json({
        success: false,
        error: `删除模板失败: ${error.message}`,
      });
    }
  }

  /**
   * 使用模板（增加使用次数）
   * POST /api/templates/:id/use
   */
  async useTemplate(req, res) {
    try {
      const { id } = req.params;

      const success = await playbackTemplateService.useTemplate(id);

      if (!success) {
        return res.status(404).json({
          success: false,
          error: "模板不存在",
        });
      }

      res.json({
        success: true,
        message: "使用记录已更新",
      });
    } catch (error) {
      console.error("更新使用记录失败:", error);
      res.status(500).json({
        success: false,
        error: `更新使用记录失败: ${error.message}`,
      });
    }
  }

  /**
   * 复制模板
   * POST /api/templates/:id/duplicate
   */
  async duplicateTemplate(req, res) {
    try {
      const { id } = req.params;
      const { name } = req.body;
      const userId = req.user.id;

      if (!name) {
        return res.status(400).json({
          success: false,
          error: "缺少必要参数: name",
        });
      }

      const newTemplate = await playbackTemplateService.duplicateTemplate(
        id,
        name,
        userId
      );

      res.status(201).json({
        success: true,
        data: newTemplate,
      });
    } catch (error) {
      console.error("复制模板失败:", error);
      const statusCode =
        error.message.includes("不存在") || error.message.includes("无权限")
          ? 404
          : 400;
      res.status(statusCode).json({
        success: false,
        error: `复制模板失败: ${error.message}`,
      });
    }
  }
}

module.exports = new PlaybackTemplateController();
