<!--
  用户等级设置页面
  允许用户选择和修改自己的语言水平等级
-->
<template>
  <div class="level-settings-view" :class="{ 'mobile-layout': isMobile }">
    <!-- 页面标题 -->
    <SmartPageHeader :title="`我的${currentLanguageLabel}水平`" @back="goBack">
      <template #actions>
        <el-button
          v-if="selectedLevel"
          type="primary"
          size="small"
          @click="saveLevel"
          :loading="saving"
        >
          保存
        </el-button>
      </template>
    </SmartPageHeader>

    <div class="settings-container">
      <!-- 设置内容 -->
      <div class="settings-content">
        <!-- 语言信息显示 -->
        <div class="language-info">
          <div class="current-language">
            <span class="language-flag">{{ getLanguageFlag(currentLanguage) }}</span>
            <span class="language-name">{{ currentLanguageLabel }}</span>
            <el-button link @click="changeLanguage" class="change-language-btn">
              切换语言
            </el-button>
          </div>
        </div>

        <div class="level-selection-section">
          <h2 class="section-title">选择你的{{ currentLanguageLabel }}水平
            <span class="section-subtitle">（点击选择，再次点击取消）</span>
          </h2>
          <div v-if="selectedLevel" class="selected-tip">
            当前选择：{{ getCurrentLevelInfo(selectedLevel)?.name }}
          </div>

          <div v-if="availableLevels.length > 0" class="level-options">
            <div
              v-for="level in availableLevels"
              :key="level.key"
              class="level-option"
              :class="{
                'selected': selectedLevel === level.key
              }"
              @click="toggleLevel(level.key)"
            >
              <div class="level-name">{{ level.name }}</div>
              <div class="level-desc">{{ level.description }}</div>
            </div>
          </div>

          <div v-else class="no-levels-tip">
            <el-empty description="当前语言暂无等级设置" :image-size="80">
              <el-button @click="changeLanguage">选择其他语言</el-button>
            </el-empty>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { isMobileDevice } from '@/utils/deviceDetector';
import { useLanguageStore } from '@/stores/languageStore';
import { getLanguageFlag } from '@/config/languageLevels';
import SmartPageHeader from '@/components/common/SmartPageHeader.vue';

const router = useRouter();
const languageStore = useLanguageStore();
const isMobile = computed(() => isMobileDevice());

// 当前语言和等级
const currentLanguage = computed(() => languageStore.currentLearningLanguage);
const currentLanguageLabel = computed(() => languageStore.currentLanguageLabel);
const currentLevel = computed(() => languageStore.currentUserLevel);

// 可用的等级选项
const availableLevels = computed(() => languageStore.currentLanguageLevels);

const selectedLevel = ref('');
const saving = ref(false);

const goBack = () => {
  router.back();
};

const changeLanguage = () => {
  router.push('/onboarding/language');
};

const toggleLevel = (levelKey) => {
  if (selectedLevel.value === levelKey) {
    // 重复点击取消选择
    selectedLevel.value = '';
  } else {
    // 选择新等级
    selectedLevel.value = levelKey;
  }
};

const saveLevel = async () => {
  if (!selectedLevel.value) return;

  saving.value = true;
  try {
    const success = languageStore.setUserLevel(selectedLevel.value);
    if (success) {
      const levelInfo = getCurrentLevelInfo(selectedLevel.value);
      ElMessage.success(`水平已设置为 ${levelInfo?.name}`);

      // 立即跳转到推荐页面
      router.replace('/?tab=recommended');
    } else {
      ElMessage.error('保存失败，请重试');
    }
  } catch (error) {
    console.error('保存等级失败:', error);
    ElMessage.error('保存失败，请重试');
  } finally {
    saving.value = false;
  }
};

// 获取当前语言的等级信息
const getCurrentLevelInfo = (levelKey) => {
  return availableLevels.value.find(level => level.key === levelKey);
};

onMounted(() => {
  // 检查是否设置了学习语言
  if (!currentLanguage.value) {
    ElMessage.warning('请先选择学习语言');
    router.push('/onboarding/language');
    return;
  }

  // 初始化时将当前等级设为选中状态
  if (currentLevel.value) {
    selectedLevel.value = currentLevel.value;
  }

  console.log('等级设置页面加载，当前语言:', currentLanguage.value, '当前等级:', currentLevel.value);
});
</script>

<style scoped>
.level-settings-view {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.settings-container {
  max-width: 48rem;
  margin: 0 auto;
  padding: 1rem 1rem 0 1rem;
}



.settings-content {
  background: white;
  border-radius: 0.75rem;
  padding: 2rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.06);
  border: 0.0625rem solid #f0f2f5;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 1rem 0;
}

.section-subtitle {
  font-size: 0.875rem;
  font-weight: normal;
  color: #909399;
}





.level-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 0.25rem;
}

.level-desc {
  font-size: 0.875rem;
  color: #606266;
}

.level-selection-section {
  margin-bottom: 2rem;
}

.level-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.level-option {
  padding: 1rem;
  border: 0.125rem solid #e4e7ed;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.level-option:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.level-option.selected {
  border-color: #409eff;
  background-color: #ecf5ff;
  position: relative;
}

.level-option.selected::after {
  content: '✓ 已选择';
  position: absolute;
  top: 0.5rem;
  right: 0.75rem;
  background: #409eff;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.selected-tip {
  background: #ecf5ff;
  border: 0.0625rem solid #b3d8ff;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  margin-bottom: 1rem;
  color: #409eff;
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
}





/* 移动端优化 */
.mobile-layout .settings-container {
  padding: 0.5rem 0.5rem 0 0.5rem;
}

.mobile-layout .settings-container {
  max-width: 100%;
}



.mobile-layout .settings-content {
  padding: 1rem;
  border-radius: 0.5rem;
}

.mobile-layout .section-title {
  font-size: 1rem;
}

.mobile-layout .level-option {
  padding: 0.75rem;
}

.mobile-layout .level-name {
  font-size: 0.9rem;
}

.mobile-layout .level-desc {
  font-size: 0.8rem;
}


</style>