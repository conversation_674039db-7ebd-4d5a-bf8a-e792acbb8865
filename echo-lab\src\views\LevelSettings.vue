<!--
  用户等级设置页面
  允许用户选择和修改自己的日语水平等级
-->
<template>
  <div class="level-settings-view" :class="{ 'mobile-layout': isMobile }">
    <!-- 页面标题 -->
    <SmartPageHeader title="我的日语水平" @back="goBack">
      <template #actions>
        <el-button 
          v-if="selectedLevel"
          type="primary" 
          size="small"
          @click="saveLevel"
          :loading="saving"
        >
          保存
        </el-button>
      </template>
    </SmartPageHeader>
    
    <div class="settings-container">
      <!-- 设置内容 -->
      <div class="settings-content">
        <div class="level-selection-section">
          <h2 class="section-title">选择你的日语水平
            <span class="section-subtitle">（点击选择，再次点击取消）</span>
          </h2>
          <div v-if="selectedLevel" class="selected-tip">
            当前选择：{{ getLevelInfo(selectedLevel)?.name }}
          </div>

          <div class="level-options">
            <div
              v-for="level in USER_LEVELS"
              :key="level.key"
              class="level-option"
              :class="{ 
                'selected': selectedLevel === level.key
              }"
              @click="toggleLevel(level.key)"
            >
              <div class="level-name">{{ level.name }}</div>
              <div class="level-desc">{{ getLevelDescription(level.key) }}</div>

            </div>
          </div>
        </div>


      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { isMobileDevice } from '@/utils/deviceDetector';
import { USER_LEVELS, getUserLevel, setUserLevel, getLevelInfo } from '@/utils/userPreferences';
import SmartPageHeader from '@/components/common/SmartPageHeader.vue';

const router = useRouter();
const isMobile = computed(() => isMobileDevice());

const currentLevel = ref(getUserLevel());
const selectedLevel = ref('');
const saving = ref(false);

const goBack = () => {
  router.back();
};

const toggleLevel = (levelKey) => {
  if (selectedLevel.value === levelKey) {
    // 重复点击取消选择
    selectedLevel.value = '';
  } else {
    // 选择新等级
    selectedLevel.value = levelKey;
  }
};

const saveLevel = async () => {
  if (!selectedLevel.value) return;
  
  saving.value = true;
  try {
    const success = setUserLevel(selectedLevel.value);
    if (success) {
      currentLevel.value = selectedLevel.value;
      ElMessage.success(`水平已设置为 ${getLevelInfo(currentLevel.value)?.name}`);
      
      // 立即跳转到推荐页面
      router.replace('/?tab=recommended');
    } else {
      ElMessage.error('保存失败，请重试');
    }
  } catch (error) {
    console.error('保存等级失败:', error);
    ElMessage.error('保存失败，请重试');
  } finally {
    saving.value = false;
  }
};

// 获取等级详细说明
const getLevelDescription = (levelKey) => {
  const descriptions = {
    'N5': '适合日语初学者，包含基础词汇和语法。能够理解和使用平假名、片假名及用平假名书写的基本汉字的典型句子和文章。',
    'N4': '适合有一定基础的学习者，日常会话水平。能够理解由基本词汇及汉字组成的有关日常生活话题的文章，能够大致听懂语速稍慢的日常会话。',
    'N3': '中级水平，能理解日常生活中的日语。能够阅读和理解日常话题中表达具体内容的文章，能够通过报纸的标题了解文章的大意。',
    'N2': '中高级水平，能理解各种场面的日语。能够阅读和理解各种话题的报纸及杂志报道、解说、简单的评论等论点清晰的文章。',
    'N1': '高级水平，能理解各种场面的日语。能够阅读具有逻辑性的复杂文章，理解文章的构成及内容，能够理解各种场面中自然语速的连贯会话。'
  };
  return descriptions[levelKey] || '未知等级';
};

onMounted(() => {
  // 初始化时将当前等级设为选中状态
  if (currentLevel.value) {
    selectedLevel.value = currentLevel.value;
  }
  console.log('组件挂载，当前等级:', currentLevel.value);
});
</script>

<style scoped>
.level-settings-view {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.settings-container {
  max-width: 48rem;
  margin: 0 auto;
  padding: 1rem 1rem 0 1rem;
}



.settings-content {
  background: white;
  border-radius: 0.75rem;
  padding: 2rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.06);
  border: 0.0625rem solid #f0f2f5;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 1rem 0;
}

.section-subtitle {
  font-size: 0.875rem;
  font-weight: normal;
  color: #909399;
}





.level-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 0.25rem;
}

.level-desc {
  font-size: 0.875rem;
  color: #606266;
}

.level-selection-section {
  margin-bottom: 2rem;
}

.level-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.level-option {
  padding: 1rem;
  border: 0.125rem solid #e4e7ed;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.level-option:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.level-option.selected {
  border-color: #409eff;
  background-color: #ecf5ff;
  position: relative;
}

.level-option.selected::after {
  content: '✓ 已选择';
  position: absolute;
  top: 0.5rem;
  right: 0.75rem;
  background: #409eff;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.selected-tip {
  background: #ecf5ff;
  border: 0.0625rem solid #b3d8ff;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  margin-bottom: 1rem;
  color: #409eff;
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
}





/* 移动端优化 */
.mobile-layout .settings-container {
  padding: 0.5rem 0.5rem 0 0.5rem;
}

.mobile-layout .settings-container {
  max-width: 100%;
}



.mobile-layout .settings-content {
  padding: 1rem;
  border-radius: 0.5rem;
}

.mobile-layout .section-title {
  font-size: 1rem;
}

.mobile-layout .level-option {
  padding: 0.75rem;
}

.mobile-layout .level-name {
  font-size: 0.9rem;
}

.mobile-layout .level-desc {
  font-size: 0.8rem;
}


</style>