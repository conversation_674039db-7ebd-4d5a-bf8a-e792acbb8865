<!--
  用户等级设置页面
  允许用户选择和修改自己的语言水平等级
-->
<template>
  <div class="level-settings-page">
    <div class="page-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <img src="@/assets/logo.jpg" alt="Echo Lab" class="logo" />
        <div class="language-indicator" @click="changeLanguage" title="点击更改语言">
          <span class="language-flag">{{ getLanguageFlag(currentLanguage) }}</span>
          <span class="language-name">{{ currentLanguageLabel }}</span>
          <el-icon class="change-icon">
            <i-ep-edit />
          </el-icon>
        </div>
        <h1 class="page-title">我的{{ currentLanguageLabel }}水平</h1>
        <p class="page-subtitle">选择你的当前水平，我们将为你推荐合适的学习内容</p>
      </div>

      <!-- 主要内容 -->
      <div class="page-content">
        <div v-if="availableLevels.length > 0" class="level-options">
          <div v-for="level in availableLevels" :key="level.key" class="level-card"
            :class="{ 'selected': selectedLevel === level.key }" @click="toggleLevel(level.key)">
            <div class="level-badge">{{ level.name }}</div>
            <div class="level-info">
              <h3 class="level-title">{{ level.name }}</h3>
              <p class="level-desc">{{ level.description }}</p>
            </div>
            <div class="selection-indicator">
              <el-icon v-if="selectedLevel === level.key" class="check-icon">
                <i-ep-check />
              </el-icon>
            </div>
          </div>
        </div>

        <div v-else class="no-levels-section">
          <el-empty description="当前语言暂无等级设置" :image-size="80">
            <el-button @click="changeLanguage">选择其他语言</el-button>
          </el-empty>
        </div>

        <div class="page-actions">
          <el-button @click="goBack" size="large" class="back-button">
            <el-icon class="el-icon--left">
              <i-ep-arrow-left />
            </el-icon>
            返回
          </el-button>

          <el-button
            v-if="currentLevel && selectedLevel !== currentLevel"
            @click="clearLevel"
            size="large"
            class="clear-button"
          >
            清除等级
          </el-button>

          <el-button
            type="primary"
            size="large"
            @click="saveLevel"
            :disabled="!selectedLevel || selectedLevel === currentLevel"
            :loading="saving"
            class="save-button"
          >
            保存设置
            <el-icon class="el-icon--right">
              <i-ep-check />
            </el-icon>
          </el-button>
        </div>
      </div>

      <!-- 页面底部 -->
      <div class="page-footer">
        <p class="footer-text">
          不确定自己的水平？没关系，你可以随时调整
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useLanguageStore } from '@/stores/languageStore';
import { getLanguageFlag } from '@/config/languageLevels';

const router = useRouter();
const languageStore = useLanguageStore();

// 当前语言和等级
const currentLanguage = computed(() => languageStore.currentLearningLanguage);
const currentLanguageLabel = computed(() => languageStore.currentLanguageLabel);
const currentLevel = computed(() => languageStore.currentUserLevel);

// 可用的等级选项
const availableLevels = computed(() => languageStore.currentLanguageLevels);

const selectedLevel = ref('');
const saving = ref(false);

const goBack = () => {
  router.back();
};

const changeLanguage = () => {
  router.push('/onboarding/language');
};

const toggleLevel = (levelKey) => {
  if (selectedLevel.value === levelKey) {
    // 重复点击取消选择
    selectedLevel.value = '';
  } else {
    // 选择新等级
    selectedLevel.value = levelKey;
  }
};

const clearLevel = async () => {
  saving.value = true;
  try {
    languageStore.clearUserLevel();
    selectedLevel.value = '';
    ElMessage.success('等级已清除');

    // 跳转到首页
    router.replace('/');
  } catch (error) {
    console.error('清除等级失败:', error);
    ElMessage.error('清除失败，请重试');
  } finally {
    saving.value = false;
  }
};

const saveLevel = async () => {
  if (!selectedLevel.value) return;

  saving.value = true;
  try {
    const success = languageStore.setUserLevel(selectedLevel.value);
    if (success) {
      const levelInfo = getCurrentLevelInfo(selectedLevel.value);
      ElMessage.success(`水平已设置为 ${levelInfo?.name}`);

      // 添加短暂延迟让用户看到成功消息
      await new Promise(resolve => setTimeout(resolve, 800));

      // 使用 replace 进行更优雅的跳转，并添加来源标记
      router.replace('/?tab=recommended&from=level-settings');
    } else {
      ElMessage.error('保存失败，请重试');
    }
  } catch (error) {
    console.error('保存等级失败:', error);
    ElMessage.error('保存失败，请重试');
  } finally {
    saving.value = false;
  }
};

// 获取当前语言的等级信息
const getCurrentLevelInfo = (levelKey) => {
  return availableLevels.value.find(level => level.key === levelKey);
};

onMounted(() => {
  // 检查是否设置了学习语言
  if (!currentLanguage.value) {
    ElMessage.warning('请先选择学习语言');
    router.push('/onboarding/language');
    return;
  }

  // 初始化时将当前等级设为选中状态
  if (currentLevel.value) {
    selectedLevel.value = currentLevel.value;
  }

  console.log('等级设置页面加载，当前语言:', currentLanguage.value, '当前等级:', currentLevel.value);
});
</script>

<style scoped>
.level-settings-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.page-container {
  max-width: 48rem;
  width: 100%;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.page-header {
  text-align: center;
  padding: 3rem 2rem 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.logo {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  margin-bottom: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.language-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  background: rgba(64, 158, 255, 0.1);
  transition: all 0.3s ease;
}

.language-indicator:hover {
  background: rgba(64, 158, 255, 0.2);
  transform: scale(1.05);
}

.change-icon {
  font-size: 0.875rem;
  color: #409eff;
  margin-left: 0.25rem;
}

.language-flag {
  font-size: 1.5rem;
}

.language-name {
  font-size: 1rem;
  font-weight: 600;
  color: #409eff;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.75rem 0;
}

.page-subtitle {
  font-size: 1rem;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.5;
}

.page-content {
  padding: 2rem;
}

.level-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 3rem;
}

.level-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  border: 2px solid #e9ecef;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
}

.level-card:hover {
  border-color: #409eff;
  background: #f8faff;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.1);
}

.level-card.selected {
  border-color: #409eff;
  background: #ecf5ff;
  box-shadow: 0 0 0 1px #409eff;
}

.level-badge {
  background: #409eff;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-weight: 600;
  font-size: 0.875rem;
  margin-right: 1rem;
  flex-shrink: 0;
  min-width: 4rem;
  text-align: center;
}

.level-card.selected .level-badge {
  background: #67c23a;
}

.level-info {
  flex: 1;
}

.level-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
}

.level-desc {
  font-size: 0.875rem;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.4;
}

.selection-indicator {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.check-icon {
  color: #67c23a;
  font-size: 1.5rem;
}

.no-levels-section {
  margin-bottom: 3rem;
}

.page-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.back-button,
.clear-button,
.save-button {
  min-width: 8rem;
  height: 2.75rem;
  font-weight: 600;
}

.page-footer {
  text-align: center;
  padding: 1.5rem 2rem;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.footer-text {
  font-size: 0.875rem;
  color: #6c757d;
  margin: 0;
}

.settings-container {
  max-width: 48rem;
  margin: 0 auto;
  padding: 1rem 1rem 0 1rem;
}



.settings-content {
  background: white;
  border-radius: 0.75rem;
  padding: 2rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.06);
  border: 0.0625rem solid #f0f2f5;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #303133;
  margin: 0 0 1rem 0;
}

.section-subtitle {
  font-size: 0.875rem;
  font-weight: normal;
  color: #909399;
}





.level-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 0.25rem;
}

.level-desc {
  font-size: 0.875rem;
  color: #606266;
}

.level-selection-section {
  margin-bottom: 2rem;
}

.level-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.level-option {
  padding: 1rem;
  border: 0.125rem solid #e4e7ed;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.level-option:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.level-option.selected {
  border-color: #409eff;
  background-color: #ecf5ff;
  position: relative;
}

.level-option.selected::after {
  content: '✓ 已选择';
  position: absolute;
  top: 0.5rem;
  right: 0.75rem;
  background: #409eff;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.selected-tip {
  background: #ecf5ff;
  border: 0.0625rem solid #b3d8ff;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  margin-bottom: 1rem;
  color: #409eff;
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
}





/* 移动端优化 */
.mobile-layout .settings-container {
  padding: 0.5rem 0.5rem 0 0.5rem;
}

.mobile-layout .settings-container {
  max-width: 100%;
}



.mobile-layout .settings-content {
  padding: 1rem;
  border-radius: 0.5rem;
}

.mobile-layout .section-title {
  font-size: 1rem;
}

.mobile-layout .level-option {
  padding: 0.75rem;
}

.mobile-layout .level-name {
  font-size: 0.9rem;
}

.mobile-layout .level-desc {
  font-size: 0.8rem;
}


</style>