<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { v4 as uuidv4 } from 'uuid';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import DraggableSectionList from './DraggableSectionList.vue';
import SectionDetailDrawer from './SectionDetailDrawer.vue';
import TemplateSelector from '../template/TemplateSelector.vue';

// 接收属性 - 新的模板驱动属性
const props = defineProps({
  settings: {
    type: Object,
    required: true
  },
  serverConfig: {
    type: Object,
    required: true
  },
  currentTemplate: {
    type: Object,
    default: null
  },
  configState: {
    type: Object,
    default: () => ({ type: 'server', source: null, name: '' })
  },
  content: {
    type: Object,
    required: true
  },
});

// 事件 - 新的模板驱动事件
const emit = defineEmits(['save', 'select-template', 'check-template-state', 'reset-to-saved']);

// 本地状态 - 与桌面版相同
const localSettings = ref(JSON.parse(JSON.stringify(props.settings)));

// 监听props.settings变化，同步更新localSettings
watch(() => props.settings, (newSettings) => {
  // 设置同步标记，避免触发模板状态检测
  isSyncingProps.value = true;

  localSettings.value = JSON.parse(JSON.stringify(newSettings));

  // 重置选中的环节索引
  if (localSettings.value.sections && localSettings.value.sections.length > 0) {
    activeSectionIndex.value = 0;
  }

  // 下一个tick后清除同步标记
  nextTick(() => {
    isSyncingProps.value = false;
  });
}, { deep: true });

// 重置为当前props.settings（用于同步）
const resetLocalSettings = () => {
  isSyncingProps.value = true;
  localSettings.value = JSON.parse(JSON.stringify(props.settings));
  if (localSettings.value.sections && localSettings.value.sections.length > 0) {
    activeSectionIndex.value = 0;
  }
  nextTick(() => {
    isSyncingProps.value = false;
  });
};

// 重置为真正保存到localStorage的配置（用于取消草稿）
const resetToSavedConfig = () => {
  // 通知父组件重新加载保存的配置和模板状态
  emit('reset-to-saved');
};

// 标记是否正在同步props变化，避免循环检测
const isSyncingProps = ref(false);

// 监听用户主动修改localSettings，检测模板状态
watch(() => localSettings.value, (newSettings) => {
  // 如果正在同步外部props变化，不触发模板状态检测
  if (isSyncingProps.value) {
    return;
  }

  // 发出事件让父组件检测模板状态
  emit('check-template-state', JSON.parse(JSON.stringify(newSettings)));
}, { deep: true });



// 当前选中的环节索引
const activeSectionIndex = ref(0);

// 抽屉可见性
const drawerVisible = ref(false);

// 模板选择器可见性
const showTemplateSelector = ref(false);

// 当前选中的环节
const activeSection = computed(() => {
  if (localSettings.value.sections && localSettings.value.sections.length > 0 && activeSectionIndex.value >= 0) {
    return localSettings.value.sections[activeSectionIndex.value];
  }
  return null;
});

// 移除未使用的计算属性

// 选择环节
const selectSection = (index) => {
  activeSectionIndex.value = index;
};

// 编辑环节
const editSection = (index) => {
  activeSectionIndex.value = index;
  drawerVisible.value = true;
};

// 移除未使用的计算属性

// 获取所有文本内容节点
const textContentNodes = computed(() => {
  if (!props.content || !props.content.configJson || !props.content.configJson.nodes) {
    return [];
  }
  return Object.values(props.content.configJson.nodes)
    .filter(node => node.type === 'textContent')
    .map(node => {
      const nodeNumber = node.number || '';
      let nodeName = '';
      if (node.customName) {
        nodeName = node.customName;
      } else {
        nodeName = `文本内容节点${nodeNumber ? ` #${nodeNumber}` : ''}`;
      }
      return {
        id: node.id,
        name: nodeName,
        type: 'textContent'
      };
    });
});

// 移除未使用的计算属性

// 添加环节 - 简化逻辑
const addSection = () => {
  // 始终使用sequence类型和基于序列的处理模式
  const sectionType = 'sequence';
  const processingMode = 'sequence';

  // 创建新环节，使用简单的标题
  const newSection = {
    id: `section_${uuidv4()}`,
    name: `环节 ${localSettings.value.sections.length + 1}`,
    type: sectionType,
    processingMode: processingMode,
    repeatCount: 4, // 默认重复4次
    pauseDuration: 3000,
    userEditable: true
  };

  {
    // 翻译设置
    newSection.enableTranslation = false; // 默认不启用翻译
    newSection.translationLanguage = '';
    newSection.translationPosition = Math.min(2, newSection.repeatCount || 4); // 默认在第2次重复后插入翻译

    // 关键词设置
    newSection.enableKeywords = false; // 默认不启用关键词
    newSection.keywordRepeatCount = 2; // 默认关键词重复2次
    newSection.keywordPosition = Math.min(2, newSection.repeatCount || 4); // 默认在第2次重复后插入关键词


    // 每次重复的参数
    const defaultSpeed = 1.0;
    const defaultPause = newSection.pauseDuration || 3000;

    newSection.repeatSpeeds = Array(newSection.repeatCount).fill(defaultSpeed);
    newSection.repeatPauses = Array(newSection.repeatCount).fill(defaultPause);
  }

  // 基于序列模式不需要设置源节点ID

  localSettings.value.sections.push(newSection);

  // 选择新添加的环节
  activeSectionIndex.value = localSettings.value.sections.length - 1;
};



// 删除环节 - 简化逻辑
const removeSection = (index) => {
  if (localSettings.value.sections.length <= 1) {
    ElMessage.warning('至少需要保留一个环节');
    return;
  }

  ElMessageBox.confirm('确定要删除此环节吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    localSettings.value.sections.splice(index, 1);
    // 如果删除的是当前选中的环节，选择第一个环节
    if (index === activeSectionIndex.value) {
      activeSectionIndex.value = 0;
    } else if (index < activeSectionIndex.value) {
      // 如果删除的环节在当前选中环节之前，调整索引
      activeSectionIndex.value--;
    }
    // watch会自动检测模板状态
  }).catch(() => { });
};

// 复制环节 - 与桌面版相同的逻辑
const duplicateSection = (index) => {
  const sectionToDuplicate = localSettings.value.sections[index];
  if (!sectionToDuplicate) return;

  // 创建深拷贝
  const newSection = JSON.parse(JSON.stringify(sectionToDuplicate));

  // 生成新的ID
  newSection.id = `section_${uuidv4()}`;

  // 修改名称，添加"副本"标识
  if (newSection.name) {
    // 如果名称已经包含"副本"，则增加编号
    if (newSection.name.includes('副本')) {
      const match = newSection.name.match(/副本(\d+)?/);
      if (match) {
        const num = match[1] ? parseInt(match[1]) + 1 : 2;
        newSection.name = newSection.name.replace(/副本(\d+)?/, `副本${num}`);
      } else {
        newSection.name = `${newSection.name}2`;
      }
    } else {
      // 保留完整名称，添加副本标识
      newSection.name = `${newSection.name}-副本`;
    }
  }

  // 插入到原环节后面
  localSettings.value.sections.splice(index + 1, 0, newSection);

  // 选择新复制的环节
  activeSectionIndex.value = index + 1;

  // watch会自动检测模板状态
  ElMessage.success('环节复制成功');
};

// 这些函数已由 DraggableSectionList 组件处理

// 保存设置 - 简化逻辑
const saveSettings = () => {
  // 保存环节设置
  const settingsToSave = JSON.parse(JSON.stringify(localSettings.value));
  emit('save', settingsToSave);
};

// 重置设置 - 使用服务器端环节信息的正确逻辑
const resetSettings = () => {
  const resetMessage = '确定要重置所有设置吗？这将恢复到服务器端的原始环节配置';

  ElMessageBox.confirm(resetMessage, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 使用服务器端配置重置，而不是当前的props.settings
    if (props.serverConfig && props.serverConfig.sections) {
      isSyncingProps.value = true;
      
      // 基于服务器端环节信息创建默认配置
      const resetConfig = JSON.parse(JSON.stringify(props.serverConfig));
      
      localSettings.value = JSON.parse(JSON.stringify(props.serverConfig));
      
      if (localSettings.value.sections && localSettings.value.sections.length > 0) {
        activeSectionIndex.value = 0;
      }
      
      nextTick(() => {
        isSyncingProps.value = false;
      });
      
      ElMessage.success('设置已重置为服务器端原始配置');
    } else {
      ElMessage.error('无法获取服务器端配置信息');
    }
  }).catch(() => { });
};

// 选择模板
const selectTemplate = (template) => {
  emit('select-template', template);
  showTemplateSelector.value = false;
};

// 模板选择器key，用于强制重新渲染
const templateSelectorKey = ref(0);

// 打开模板选择器时重置状态
const openTemplateSelector = () => {
  templateSelectorKey.value++; // 强制重新渲染组件
  showTemplateSelector.value = true;
};

// 处理环节更新（环节编辑保存后）
const handleSectionUpdate = (newSection) => {
  if (activeSection.value) {
    // 更新本地环节数据（watch会自动检测模板状态）
    Object.assign(activeSection.value, newSection);
  }
};



// 确保处理方式有效
const ensureProcessingMode = (section) => {
  // 确保环节类型为sequence
  section.type = 'sequence';

  // 只有当处理方式完全未设置时才设置默认值
  if (section.processingMode === undefined || section.processingMode === null) {
    if (section.sourceNodeIds && section.sourceNodeIds.length > 0) {
      section.processingMode = 'source';
    } else {
      section.processingMode = 'sequence';
    }
  }

  // 根据处理模式设置正确的属性
  if (section.processingMode === 'sequence') {
    // 基于序列模式，移除源节点ID
    if (section.sourceNodeIds) {
      delete section.sourceNodeIds; // 删除不需要的属性
    }
    // 移除sourceIndex属性，不再需要选择特定序列
    if (section.sourceIndex !== undefined) {
      delete section.sourceIndex;
    }
  } else if (section.processingMode === 'source') {
    // 基于源节点模式使用源节点ID
    if (section.sourceIndex !== undefined) {
      delete section.sourceIndex; // 删除不需要的属性
    }

    // 如果没有源节点ID，但有可用的文本内容节点，设置默认源节点
    if ((!section.sourceNodeIds || section.sourceNodeIds.length === 0) && textContentNodes.value.length > 0) {
      section.sourceNodeIds = [textContentNodes.value[0].id]; // 默认使用第一个源节点
    }
  }
};

// 移除未使用的函数

// 监听环节设置变化，仅在必要时设置默认值
watch(() => localSettings.value.sections, (sections) => {
  sections.forEach(section => {
    // 确保环节类型为sequence
    section.type = 'sequence';

    ensureProcessingMode(section);

    // 只在字段缺失时设置默认值，不修改已有值
    if (section.enableTranslation === undefined) {
      section.enableTranslation = false;
    }
    if (section.translationLanguage === undefined) {
      section.translationLanguage = '';
    }
    if (section.translationPosition === undefined) {
      section.translationPosition = Math.min(2, section.repeatCount || 4);
    }
    if (section.enableKeywords === undefined) {
      section.enableKeywords = false;
    }
    if (section.keywordRepeatCount === undefined) {
      section.keywordRepeatCount = 2;
    }
    if (section.keywordPosition === undefined) {
      section.keywordPosition = Math.min(2, section.repeatCount || 4);
    }

    // 确保重复参数数组存在且长度正确
    const repeatCount = section.repeatCount || 4;
    const defaultSpeed = 1.0;
    const defaultPause = section.pauseDuration || 3000;

    if (!section.repeatSpeeds || !Array.isArray(section.repeatSpeeds)) {
      section.repeatSpeeds = Array(repeatCount).fill(defaultSpeed);
    } else {
      // 调整数组长度以匹配repeatCount
      while (section.repeatSpeeds.length < repeatCount) {
        section.repeatSpeeds.push(defaultSpeed);
      }
      if (section.repeatSpeeds.length > repeatCount) {
        section.repeatSpeeds = section.repeatSpeeds.slice(0, repeatCount);
      }
    }

    if (!section.repeatPauses || !Array.isArray(section.repeatPauses)) {
      section.repeatPauses = Array(repeatCount).fill(defaultPause);
    } else {
      // 调整数组长度以匹配repeatCount
      while (section.repeatPauses.length < repeatCount) {
        section.repeatPauses.push(defaultPause);
      }
      if (section.repeatPauses.length > repeatCount) {
        section.repeatPauses = section.repeatPauses.slice(0, repeatCount);
      }
    }
  });
}, { deep: true });

// 暴露方法给父组件
defineExpose({
  saveSettings,
  resetSettings,
  resetLocalSettings,
  resetToSavedConfig
});
</script>

<template>
  <div class="playback-settings-panel">
    <!-- 播放策略模板选择 -->
    <div class="template-section">
      <div class="template-header">
        <h3>播放策略</h3>
        <el-button size="small" @click="openTemplateSelector">
          <el-icon class="el-icon--left">
            <i-ep-setting />
          </el-icon>
          选择策略
        </el-button>
      </div>
      <!-- 配置状态显示 -->
      <div class="config-status">
        <el-tag v-if="props.configState.type === 'custom'" type="primary" effect="light">
          <el-icon><i-ep-user /></el-icon>
          自定义设置
        </el-tag>
        
        <el-tag v-else-if="props.configState.type === 'template'" type="success" effect="light">
          <el-icon><i-ep-collection-tag /></el-icon>
          使用模板：{{ props.configState.name }}
        </el-tag>
        
        <el-tag v-else-if="props.configState.type === 'global'" type="warning" effect="light">
          <el-icon><i-ep-star /></el-icon>
          全局默认：{{ props.configState.name }}
        </el-tag>
        
        <el-tag v-else type="info" effect="light">
          <el-icon><i-ep-document /></el-icon>
          内容默认
        </el-tag>
      </div>
    </div>

    <!-- 环节列表 -->
    <div class="section-list">
      <div class="section-list-header">
        <h3>环节列表</h3>
        <el-button type="primary" size="small" @click="addSection" class="add-section-btn">
          <el-icon class="el-icon--left">
            <i-ep-plus />
          </el-icon>
          添加环节
        </el-button>
      </div>

      <!-- 使用可拖拽环节列表组件 -->
      <DraggableSectionList :sections="localSettings.sections" :active-section-index="activeSectionIndex"
        @update:sections="(newSections) => localSettings.sections = newSections" @select="selectSection"
        @duplicate="duplicateSection" @remove="removeSection" @edit="editSection" />
    </div>

    <!-- 使用抽屉组件显示详细设置 -->
    <SectionDetailDrawer :visible="drawerVisible" @update:visible="(val) => drawerVisible = val"
      :section="activeSection" :content="props.content" @update:section="handleSectionUpdate" />

    <!-- 模板选择器 -->
    <el-drawer v-model="showTemplateSelector" title="选择播放策略" direction="btt" size="80%">
      <TemplateSelector :key="templateSelectorKey" :current-template="props.currentTemplate" @select="selectTemplate" @close="showTemplateSelector = false" />
    </el-drawer>
  </div>
</template>

<style scoped>
.playback-settings-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background-color: #ffffff;
}

/* 模板选择区域样式 */
.template-section {
  padding: 1rem;
  border-bottom: 0.0625rem solid #f0f0f0;
  background-color: #fafafa;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.template-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: #303133;
}

.config-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.config-status .el-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
}

/* 环节列表样式 */
.section-list {
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.section-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding: 0 0.25rem 0.5rem;
}

.section-list-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: #303133;
}

.add-section-btn {
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
}

/* 环节卡片样式已移至 DraggableSectionList 组件 */

/* 详细设置样式已移至 SectionDetailDrawer 组件 */
.full-width {
  width: 100%;
}

/* 移除不再需要的样式 */
</style>
