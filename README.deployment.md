# JapRepeater 部署指南

本文档提供了在阿里云ECS服务器上部署JapRepeater应用的简易指南。

## 部署前准备

1. 一台阿里云ECS服务器（推荐配置：2核4G，CentOS 7或Ubuntu 18.04+）
2. 域名（用于HTTPS配置）
3. 阿里云OSS存储空间（用于存储音频文件）
4. Node.js 16+（推荐使用nvm管理Node.js版本）
5. MySQL 8.0+（用于存储应用数据）
6. Nginx（用于提供前端静态文件和API代理）
7. PM2（用于管理Node.js进程）

## 一键部署步骤

我们提供了一键部署脚本，只需几个简单的步骤即可完成部署：

### 1. 获取代码

```bash
# 克隆代码库
git clone https://your-repository-url.git
cd JapRepeater
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp backend/.env.example backend/.env

# 编辑环境变量
nano backend/.env
```

确保填写正确的数据库配置、阿里云OSS配置和其他必要参数。

### 3. 运行部署脚本

```bash
# 添加执行权限
chmod +x deploy.sh

# 运行部署脚本
./deploy.sh
```

脚本会自动完成以下工作：
- 更新代码库
- 构建前端项目
- 复制前端文件到Nginx目录
- 更新后端依赖
- 使用PM2启动后端服务

### 4. 访问应用

部署完成后，您可以通过以下地址访问应用：

- 前端界面：https://your-domain.com
- 后端API：https://your-domain.com/api

## 配置HTTPS

项目使用阿里云免费SSL证书和Nginx配置HTTPS。以下是配置步骤：

```bash
# 1. 在阿里云控制台申请免费SSL证书

# 2. 下载证书文件（Nginx格式）
# 将证书文件上传到服务器的/etc/nginx/ssl目录
sudo mkdir -p /etc/nginx/ssl
sudo chmod 700 /etc/nginx/ssl

# 3. 配置Nginx
sudo nano /etc/nginx/conf.d/echolab.conf
```

Nginx配置示例：

```nginx
server {
    listen 80;
    server_name echolab.club www.echolab.club;

    # 将HTTP请求重定向到HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name echolab.club www.echolab.club;

    # SSL证书配置
    ssl_certificate /etc/nginx/ssl/echolab.club.pem;
    ssl_certificate_key /etc/nginx/ssl/echolab.club.key;
    ssl_session_timeout 5m;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;

    # 前端静态文件
    location / {
        root /var/www/echo-lab;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # 后端API代理
    location /api/ {
        proxy_pass http://localhost:3000/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 维护操作

### 查看日志

```bash
# 查看后端日志
pm2 logs echo-lab-backend

# 查看Nginx访问日志
sudo tail -f /var/log/nginx/access.log

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log
```

### 重启服务

```bash
# 重启后端服务
pm2 restart echo-lab-backend

# 重启Nginx
sudo systemctl restart nginx
```

### 更新应用

当有新版本时，只需拉取最新代码并重新运行部署脚本：

```bash
cd ~/JapRepeater
git pull
./deploy.sh
```

### 备份数据

```bash
# 备份MySQL数据
cd ~/JapRepeater/backend
bash scripts/backup-db.sh
```

## 故障排除

### 常见问题

1. **后端服务无法启动**
   - 检查PM2状态：`pm2 status`
   - 查看PM2日志：`pm2 logs echo-lab-backend`
   - 检查环境变量配置：`cat backend/.env`

2. **数据库连接失败**
   - 确保MySQL服务正在运行：`sudo systemctl status mysql`
   - 检查数据库配置是否正确：`cat backend/.env | grep DB_`
   - 尝试手动连接数据库：`mysql -u username -p -h localhost echo-lab`

3. **OSS上传失败**
   - 验证OSS配置参数：`cat backend/.env | grep OSS_`
   - 检查OSS存储桶权限
   - 检查网络连接：`ping oss-cn-hongkong.aliyuncs.com`

4. **SSL证书问题**
   - 检查证书文件权限：`ls -la /etc/nginx/ssl/`
   - 检查Nginx配置：`sudo nginx -t`
   - 检查证书有效期：`openssl x509 -in /etc/nginx/ssl/echolab.club.pem -noout -dates`

如有其他问题，请联系技术支持。
