<template>
  <div class="error-statistics">
    <div class="page-header">
      <h1>错误统计管理</h1>
      <p>监控和管理前端错误，提升用户体验</p>
    </div>

    <!-- 概览卡片 -->
    <div class="overview-cards">
      <div class="card">
        <div class="card-header">
          <h3>总错误数</h3>
        </div>
        <div class="card-content">
          <div class="number">{{ overview.total_errors || 0 }}</div>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <h3>新错误</h3>
        </div>
        <div class="card-content">
          <div class="number error">{{ overview.new_errors || 0 }}</div>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <h3>已解决</h3>
        </div>
        <div class="card-content">
          <div class="number success">{{ overview.resolved_errors || 0 }}</div>
        </div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters">
      <el-form :model="filters" inline>
        <el-form-item label="错误类型">
          <el-select v-model="filters.error_type" placeholder="全部类型" clearable>
            <el-option label="JavaScript错误" value="javascript" />
            <el-option label="Vue组件错误" value="vue" />
            <el-option label="网络错误" value="network" />
            <el-option label="资源错误" value="resource" />
            <el-option label="Promise错误" value="promise" />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="全部状态" clearable>
            <el-option label="新错误" value="new" />
            <el-option label="已确认" value="acknowledged" />
            <el-option label="已解决" value="resolved" />
            <el-option label="已忽略" value="ignored" />
          </el-select>
        </el-form-item>

        <el-form-item label="优先级">
          <el-select v-model="filters.priority" placeholder="全部优先级" clearable>
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="critical" />
          </el-select>
        </el-form-item>

        <el-form-item label="严重程度">
          <el-select v-model="filters.severity_level" placeholder="全部严重程度" clearable>
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="critical" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="loadErrorSummaries">查询</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 错误列表 -->
    <div class="error-list">
      <el-table :data="errorList" v-loading="loading">
        <el-table-column prop="message" label="错误消息" min-width="300">
          <template #default="{ row }">
            <div class="error-message">
              <div class="message-text">{{ row.message }}</div>
              <div class="error-meta">
                <el-tag :type="getErrorTypeColor(row.error_type)" size="small">
                  {{ getErrorTypeLabel(row.error_type) }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="occurrence_count" label="发生次数" width="100" sortable />
        <el-table-column prop="affected_users" label="影响用户" width="100" sortable />

        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)" size="small">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityColor(row.priority)" size="small">
              {{ getPriorityLabel(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="严重程度" width="120">
          <template #default="{ row }">
            <el-tag v-if="row.severity_level" :type="getSeverityColor(row.severity_level)" size="small">
              {{ getSeverityLabel(row.severity_level) }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column prop="last_seen" label="最后发生" width="150">
          <template #default="{ row }">
            {{ formatDate(row.last_seen) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewDetails(row)">详情</el-button>
            <el-dropdown @command="(command) => handleAction(command, row)">
              <el-button size="small" link>
                更多<el-icon>
                  <i-ep-arrow-down />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="acknowledge">标记已确认</el-dropdown-item>
                  <el-dropdown-item command="resolve">标记已解决</el-dropdown-item>
                  <el-dropdown-item command="ignore">忽略</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination :current-page="pagination.page" :page-size="pagination.limit" :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 错误详情对话框 -->
    <el-dialog v-model="detailDialog.visible" title="错误详情" width="80%">
      <div v-if="detailDialog.data" class="error-details">
        <div class="detail-section">
          <h4>基本信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="错误类型">
              {{ getErrorTypeLabel(detailDialog.data.summary.error_type) }}
            </el-descriptions-item>
            <el-descriptions-item label="发生次数">
              {{ detailDialog.data.summary.occurrence_count }}
            </el-descriptions-item>
            <el-descriptions-item label="影响用户">
              {{ detailDialog.data.summary.affected_users }}
            </el-descriptions-item>
            <el-descriptions-item label="首次发现">
              {{ formatDate(detailDialog.data.summary.first_seen) }}
            </el-descriptions-item>
            <el-descriptions-item label="最后发生">
              {{ formatDate(detailDialog.data.summary.last_seen) }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusColor(detailDialog.data.summary.status)">
                {{ getStatusLabel(detailDialog.data.summary.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="严重程度" v-if="detailDialog.data.recent_logs?.[0]?.severity_level">
              <el-tag :type="getSeverityColor(detailDialog.data.recent_logs[0].severity_level)">
                {{ getSeverityLabel(detailDialog.data.recent_logs[0].severity_level) }}
                <span v-if="detailDialog.data.recent_logs[0].severity_score">
                  ({{ detailDialog.data.recent_logs[0].severity_score }}分)
                </span>
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <h4>错误消息</h4>
          <pre class="error-message-detail">{{ detailDialog.data.summary.message }}</pre>
        </div>

        <div v-if="detailDialog.data.summary.stack_sample" class="detail-section">
          <h4>错误堆栈</h4>
          <pre class="error-stack">{{ detailDialog.data.summary.stack_sample }}</pre>
        </div>

        <!-- 增强上下文信息 -->
        <div v-if="detailDialog.data.recent_logs?.[0]?.enhanced_context" class="detail-section">
          <h4>增强上下文信息</h4>
          <el-collapse>
            <el-collapse-item title="性能信息" name="performance"
              v-if="formatEnhancedContext(detailDialog.data.recent_logs[0].enhanced_context)?.performance">
              <div class="context-content">
                <pre>{{ JSON.stringify(formatEnhancedContext(detailDialog.data.recent_logs[0].enhanced_context).performance,
                  null, 2) }}</pre>
              </div>
            </el-collapse-item>

            <el-collapse-item title="环境信息" name="environment"
              v-if="formatEnhancedContext(detailDialog.data.recent_logs[0].enhanced_context)?.environment">
              <div class="context-content">
                <pre>{{ JSON.stringify(formatEnhancedContext(detailDialog.data.recent_logs[0].enhanced_context).environment,
                  null, 2) }}</pre>
              </div>
            </el-collapse-item>

            <el-collapse-item title="用户行为" name="user_behavior"
              v-if="formatEnhancedContext(detailDialog.data.recent_logs[0].enhanced_context)?.user_behavior">
              <div class="context-content">
                <pre>{{
                  JSON.stringify(formatEnhancedContext(detailDialog.data.recent_logs[0].enhanced_context).user_behavior,
                    null, 2) }}</pre>
              </div>
            </el-collapse-item>

            <el-collapse-item title="应用状态" name="app_state"
              v-if="formatEnhancedContext(detailDialog.data.recent_logs[0].enhanced_context)?.app_state">
              <div class="context-content">
                <pre>{{ JSON.stringify(formatEnhancedContext(detailDialog.data.recent_logs[0].enhanced_context).app_state,
                  null, 2) }}</pre>
              </div>
            </el-collapse-item>

            <el-collapse-item title="严重程度分析" name="severity"
              v-if="formatEnhancedContext(detailDialog.data.recent_logs[0].enhanced_context)?.severity">
              <div class="context-content">
                <div class="severity-analysis">
                  <p><strong>等级:</strong> {{
                    getSeverityLabel(formatEnhancedContext(detailDialog.data.recent_logs[0].enhanced_context).severity.level)
                  }}</p>
                  <p><strong>分数:</strong> {{
                    formatEnhancedContext(detailDialog.data.recent_logs[0].enhanced_context).severity.score }}/100</p>
                  <p><strong>影响因素:</strong></p>
                  <ul>
                    <li
                      v-for="factor in formatEnhancedContext(detailDialog.data.recent_logs[0].enhanced_context).severity.factors"
                      :key="factor">
                      {{ factor }}
                    </li>
                  </ul>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>

        <div class="detail-section">
          <h4>最近发生记录</h4>
          <el-table :data="detailDialog.data.recent_logs" size="small">
            <el-table-column prop="url" label="页面URL" min-width="200" />
            <el-table-column prop="user_agent" label="浏览器" min-width="150">
              <template #default="{ row }">
                {{ getBrowserInfo(row.browser_info) }}
              </template>
            </el-table-column>
            <el-table-column label="严重程度" width="120">
              <template #default="{ row }">
                <el-tag v-if="row.severity_level" :type="getSeverityColor(row.severity_level)" size="small">
                  {{ getSeverityLabel(row.severity_level) }}
                </el-tag>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="发生时间" width="150">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { errorManagementService } from '@/services/errorStatisticsService';

// 响应式数据
const loading = ref(false);
const overview = ref({});
const errorList = ref([]);

const filters = reactive({
  error_type: '',
  status: '',
  priority: '',
  severity_level: ''
});

const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
});

const detailDialog = reactive({
  visible: false,
  data: null
});

// 方法
const loadOverview = async () => {
  try {
    const response = await errorManagementService.getOverview();
    overview.value = response.data.overview;
  } catch (error) {
    console.error('加载概览数据失败:', error);
  }
};

const loadErrorSummaries = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.page,
      limit: pagination.limit,
      ...filters
    };

    const response = await errorManagementService.getErrorSummaries(params);
    errorList.value = response.data.data;
    pagination.total = response.data.total;
  } catch (error) {
    console.error('加载错误列表失败:', error);
    ElMessage.error('加载错误列表失败');
  } finally {
    loading.value = false;
  }
};

const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = '';
  });
  pagination.page = 1;
  loadErrorSummaries();
};

const viewDetails = async (row) => {
  try {
    const response = await errorManagementService.getErrorDetails(row.error_hash);
    detailDialog.data = response.data;
    detailDialog.visible = true;
  } catch (error) {
    console.error('加载错误详情失败:', error);
    ElMessage.error('加载错误详情失败');
  }
};

const handleAction = async (command, row) => {
  try {
    let status = '';
    switch (command) {
      case 'acknowledge':
        status = 'acknowledged';
        break;
      case 'resolve':
        status = 'resolved';
        break;
      case 'ignore':
        status = 'ignored';
        break;
    }

    await errorManagementService.updateErrorStatus(row.error_hash, status);
    ElMessage.success('状态更新成功');
    loadErrorSummaries();
    loadOverview();
  } catch (error) {
    console.error('更新状态失败:', error);
    ElMessage.error('更新状态失败');
  }
};

const handleSizeChange = (size) => {
  pagination.limit = size;
  pagination.page = 1;
  loadErrorSummaries();
};

const handleCurrentChange = (page) => {
  pagination.page = page;
  loadErrorSummaries();
};

// 辅助方法
const getErrorTypeLabel = (type) => {
  const labels = {
    javascript: 'JavaScript',
    vue: 'Vue组件',
    network: '网络',
    resource: '资源',
    promise: 'Promise'
  };
  return labels[type] || type;
};

const getErrorTypeColor = (type) => {
  const colors = {
    javascript: 'danger',
    vue: 'warning',
    network: 'info',
    resource: 'primary',
    promise: 'danger'
  };
  return colors[type] || '';
};

const getStatusLabel = (status) => {
  const labels = {
    new: '新错误',
    acknowledged: '已确认',
    resolved: '已解决',
    ignored: '已忽略'
  };
  return labels[status] || status;
};

const getStatusColor = (status) => {
  const colors = {
    new: 'danger',
    acknowledged: 'warning',
    resolved: 'success',
    ignored: 'info'
  };
  return colors[status] || '';
};

const getPriorityLabel = (priority) => {
  const labels = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '紧急'
  };
  return labels[priority] || priority;
};

const getPriorityColor = (priority) => {
  const colors = {
    low: 'info',
    medium: 'warning',
    high: 'danger',
    critical: 'danger'
  };
  return colors[priority] || '';
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleString('zh-CN');
};

const getBrowserInfo = (browserInfo) => {
  if (!browserInfo || !browserInfo.name) return '未知';
  return `${browserInfo.name} ${browserInfo.version || ''}`;
};

const getSeverityLabel = (level) => {
  const labels = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '紧急'
  };
  return labels[level] || level || '未知';
};

const getSeverityColor = (level) => {
  const colors = {
    low: 'info',
    medium: 'warning',
    high: 'danger',
    critical: 'danger'
  };
  return colors[level] || '';
};

const formatEnhancedContext = (enhancedContext) => {
  if (!enhancedContext) return null;

  try {
    const context = typeof enhancedContext === 'string'
      ? JSON.parse(enhancedContext)
      : enhancedContext;
    return context;
  } catch (err) {
    console.warn('解析增强上下文失败:', err);
    return null;
  }
};

// 生命周期
onMounted(() => {
  loadOverview();
  loadErrorSummaries();
});
</script>

<style scoped>
.error-statistics {
  padding: 1.5rem;
}

.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.card {
  background: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header h3 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  color: #606266;
}

.card-content .number {
  font-size: 2rem;
  font-weight: bold;
  color: #303133;
}

.card-content .number.error {
  color: #f56c6c;
}

.card-content .number.success {
  color: #67c23a;
}

.filters {
  background: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.error-list {
  background: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.error-message {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.message-text {
  font-weight: 500;
  color: #303133;
}

.error-meta {
  display: flex;
  gap: 0.5rem;
}

.pagination {
  margin-top: 1rem;
  display: flex;
  justify-content: center;
}

.error-details {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 2rem;
}

.detail-section h4 {
  margin: 0 0 1rem 0;
  color: #303133;
}

.error-message-detail,
.error-stack {
  background: #f5f7fa;
  padding: 1rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;
}

.context-content {
  max-height: 400px;
  overflow-y: auto;
}

.context-content pre {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
}

.severity-analysis {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 0.25rem;
}

.severity-analysis p {
  margin: 0.5rem 0;
}

.severity-analysis ul {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.severity-analysis li {
  margin: 0.25rem 0;
}
</style>
