"use strict";

/**
 * 创建IP黑白名单合并表
 * 使用单一表格存储黑名单和白名单IP
 */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable("ip_list", {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      ip: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: "IP地址或CIDR格式",
      },
      type: {
        type: Sequelize.ENUM("blacklist", "whitelist"),
        allowNull: false,
        comment: "IP类型：黑名单或白名单",
      },
      reason: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: "原因或备注",
      },
      expires_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "过期时间，null表示永久",
      },
      created_by: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: "创建者",
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
        comment: "创建时间",
      },
    });

    // 添加唯一索引，确保同一个IP不会同时出现在黑名单和白名单中
    await queryInterface.addIndex("ip_list", ["ip", "type"], {
      name: "idx_ip_list_ip_type",
      unique: true,
    });

    // 添加其他索引
    await queryInterface.addIndex("ip_list", ["type"], {
      name: "idx_ip_list_type",
    });
    await queryInterface.addIndex("ip_list", ["expires_at"], {
      name: "idx_ip_list_expires_at",
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable("ip_list");
  },
};
