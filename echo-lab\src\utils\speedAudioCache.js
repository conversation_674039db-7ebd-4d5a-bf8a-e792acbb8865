/**
 * 倍速音频缓存管理器
 * 管理不同倍速的音频 Blob URL
 */

import lamejs from "@breezystack/lamejs";

// 倍速音频缓存
const speedAdjustedCache = new Map();

// 最大缓存数量
const MAX_CACHE_SIZE = 100;

// 音频采样率
const VOICE_SAMPLE_RATE = 24000;

/**
 * 使用 OfflineAudioContext 解码音频数据
 * @param {ArrayBuffer} arrayBuffer - 音频数据
 * @returns {Promise<AudioBuffer>} 解码后的音频缓冲区
 */
async function decodeAudioDataOffline(arrayBuffer) {
  // 创建一个临时的 OfflineAudioContext 用于解码
  // 使用最小的参数，因为我们只是用来解码，不是渲染
  const tempContext = new OfflineAudioContext(1, 1, VOICE_SAMPLE_RATE);

  try {
    const audioBuffer = await tempContext.decodeAudioData(arrayBuffer);
    console.log("OfflineAudioContext 音频解码成功");
    return audioBuffer;
  } catch (error) {
    console.error("OfflineAudioContext 音频解码失败:", error);
    throw error;
  }
}

/**
 * 获取缓存的倍速音频URL
 * @param {string} audioUrl - 原始音频URL
 * @param {number} speed - 播放速度
 * @returns {Promise<string>} 倍速音频 Blob URL
 */
export async function getCachedSpeedAdjustedUrl(audioUrl, speed) {
  // 如果是标准速度，直接返回原始URL
  if (speed === 1.0) {
    return audioUrl;
  }

  // 生成缓存键
  const cacheKey = `${audioUrl}_${speed}`;

  // 检查缓存
  if (speedAdjustedCache.has(cacheKey)) {
    const cachedUrl = speedAdjustedCache.get(cacheKey);
    const isBlob = cachedUrl.startsWith('blob:');
    console.log(
      `使用缓存的倍速音频: ${speed}x, 原始URL: ${audioUrl.substring(0, 30)}..., 缓存URL: ${cachedUrl.substring(0, 30)}..., 是否为Blob: ${isBlob}`
    );
    return cachedUrl;
  }

  try {
    console.log(`开始生成倍速音频: ${speed}x, 原始URL: ${audioUrl}`);

    // 生成新的倍速音频
    const speedAdjustedUrl = await generateSpeedAdjustedUrl(audioUrl, speed);

    // 添加到缓存
    setCachedSpeedAdjustedUrl(cacheKey, speedAdjustedUrl);

    console.log(
      `✅ 倍速音频生成成功: ${speed}x, 原始URL: ${audioUrl.substring(
        0,
        50
      )}..., 生成URL: ${speedAdjustedUrl.substring(0, 50)}...`
    );
    return speedAdjustedUrl;
  } catch (error) {
    console.error(
      `❌ 生成倍速音频失败: ${speed}x, 原始URL: ${audioUrl}`,
      error
    );
    // 生成失败时返回原始URL，但不缓存这个结果
    console.warn(`⚠️ 使用原始音频作为降级方案: ${audioUrl}`);
    return audioUrl;
  }
}

/**
 * 获取缓存的倍速音频URL和实际时长
 * @param {string} audioUrl - 原始音频URL
 * @param {number} speed - 播放速度
 * @param {number} expectedDuration - 预期时长
 * @returns {Promise<{url: string, duration: number}>} 倍速音频 Blob URL和实际时长
 */
export async function getCachedSpeedAdjustedUrlWithDuration(
  audioUrl,
  speed,
  expectedDuration
) {
  // 如果是标准速度，直接返回原始URL和预期时长
  if (speed === 1.0) {
    return {
      url: audioUrl,
      duration: expectedDuration,
    };
  }

  try {
    // 获取倍速音频URL
    const speedAdjustedUrl = await getCachedSpeedAdjustedUrl(audioUrl, speed);

    // 关键修复：使用预期时长而不是实际音频时长来计算倍速后的时长
    // 这样可以确保时间轴中声明的时长与实际播放时长一致
    const speedAdjustedDuration = expectedDuration / speed;

    console.log(
      `倍速音频时长计算: 预期原始=${expectedDuration.toFixed(
        3
      )}s, 速度=${speed}x, 计算时长=${speedAdjustedDuration.toFixed(3)}s`
    );

    return {
      url: speedAdjustedUrl,
      duration: speedAdjustedDuration,
    };
  } catch (error) {
    console.error(
      `❌ 获取倍速音频时长失败: ${speed}x, 原始URL: ${audioUrl}`,
      error
    );
    // 返回原始URL和预期时长作为降级方案
    return {
      url: audioUrl,
      duration: expectedDuration,
    };
  }
}

/**
 * 生成倍速音频URL
 * @param {string} audioUrl - 原始音频URL
 * @param {number} speed - 播放速度
 * @returns {Promise<string>} 倍速音频 Blob URL
 */
async function generateSpeedAdjustedUrl(audioUrl, speed) {
  try {
    // 获取原始音频数据
    const audioBuffer = await fetchAndDecodeAudio(audioUrl);

    // 处理倍速
    const speedAdjustedBuffer = await changePlaybackRate(audioBuffer, speed);

    // 验证处理后的音频数据
    if (!speedAdjustedBuffer || speedAdjustedBuffer.length === 0) {
      throw new Error("倍速处理后的音频数据无效");
    }

    // 直接用lamejs生成MP3
    const mp3Data = audioBufferToMp3(speedAdjustedBuffer);
    const blob = new Blob([mp3Data], { type: "audio/mpeg" });
    const blobUrl = URL.createObjectURL(blob);

    console.log(
      `✅ 倍速音频生成成功: ${speed}x, 数据大小: ${mp3Data.length} bytes`
    );
    return blobUrl;
  } catch (error) {
    console.error("生成倍速音频URL失败:", error);
    throw error;
  }
}

/**
 * 获取并解码音频数据
 * @param {string} url - 音频URL
 * @returns {Promise<AudioBuffer>} 音频缓冲区
 */
async function fetchAndDecodeAudio(url) {
  try {
    // 获取音频数据
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Accept: "audio/*",
      },
      credentials: "omit",
    });

    if (!response.ok) {
      throw new Error(`获取音频失败，状态码: ${response.status}`);
    }

    const arrayBuffer = await response.arrayBuffer();

    if (arrayBuffer.byteLength === 0) {
      throw new Error("音频数据为空");
    }

    // 使用 OfflineAudioContext 解码音频数据
    const decodedBuffer = await decodeAudioDataOffline(arrayBuffer);

    // 如果采样率不匹配，进行重采样
    if (decodedBuffer.sampleRate !== VOICE_SAMPLE_RATE) {
      return resampleAudio(decodedBuffer, VOICE_SAMPLE_RATE);
    }

    return decodedBuffer;
  } catch (error) {
    console.error("获取并解码音频失败:", error);
    throw error;
  }
}

/**
 * 改变音频播放速度 - 使用重采样算法，保持音调不变
 * @param {AudioBuffer} buffer - 原始音频缓冲区
 * @param {number} rate - 播放速度
 * @returns {Promise<AudioBuffer>} 处理后的音频缓冲区
 */
async function changePlaybackRate(buffer, rate) {
  try {
    // 安全检查
    if (!buffer || buffer.length <= 0 || buffer.numberOfChannels <= 0) {
      throw new Error("无效的音频缓冲区");
    }

    // 确保rate是有效的数字
    const validRate = rate && !isNaN(rate) ? rate : 1.0;

    console.log(
      `🔄 处理倍速音频: ${validRate}x, 原始长度: ${buffer.length}, 声道数: ${buffer.numberOfChannels}, 采样率: ${buffer.sampleRate}`
    );

    // 如果速度是1.0，直接返回原始缓冲区
    if (validRate === 1.0) {
      return buffer;
    }

    // 使用重采样算法：保持音调不变，只改变播放速度
    const newLength = Math.max(1, Math.floor(buffer.length / validRate));
    console.log(
      `📏 新音频长度: ${newLength} (压缩比: ${(
        (newLength / buffer.length) *
        100
      ).toFixed(1)}%)`
    );

    // 创建新的AudioBuffer - 使用24kHz采样率
    const tempContext = new AudioContext({ sampleRate: VOICE_SAMPLE_RATE });
    const newBuffer = tempContext.createBuffer(
      buffer.numberOfChannels,
      newLength,
      VOICE_SAMPLE_RATE
    );

    // 对每个声道进行重采样
    for (let channel = 0; channel < buffer.numberOfChannels; channel++) {
      const inputData = buffer.getChannelData(channel);
      const outputData = newBuffer.getChannelData(channel);

      // 简单的线性插值重采样
      for (let i = 0; i < newLength; i++) {
        const sourceIndex = i * validRate;
        const index = Math.floor(sourceIndex);
        const fraction = sourceIndex - index;

        if (index + 1 < inputData.length) {
          // 线性插值
          outputData[i] = inputData[index] * (1 - fraction) + inputData[index + 1] * fraction;
        } else if (index < inputData.length) {
          outputData[i] = inputData[index];
        } else {
          outputData[i] = 0;
        }
      }
    }

    console.log(
      `✅ 倍速处理完成: ${validRate}x, 新长度: ${newLength}, 时长: ${(newLength / buffer.sampleRate).toFixed(2)}s`
    );
    return newBuffer;
  } catch (error) {
    console.error("改变音频播放速度失败:", error);
    throw error;
  }
}

/**
 * 重采样音频
 * @param {AudioBuffer} audioBuffer - 原始音频缓冲区
 * @param {number} targetSampleRate - 目标采样率
 * @returns {Promise<AudioBuffer>} 重采样后的音频缓冲区
 */
async function resampleAudio(audioBuffer, targetSampleRate) {
  if (targetSampleRate === audioBuffer.sampleRate) {
    return audioBuffer;
  }

  const numChannels = audioBuffer.numberOfChannels;
  const duration = audioBuffer.duration;
  const newLength = Math.round(duration * targetSampleRate);

  const offlineContext = new OfflineAudioContext(
    numChannels,
    newLength,
    targetSampleRate
  );

  const bufferSource = offlineContext.createBufferSource();
  bufferSource.buffer = audioBuffer;
  bufferSource.connect(offlineContext.destination);
  bufferSource.start();

  return offlineContext.startRendering();
}

/**
 * 设置缓存的倍速音频URL
 * @param {string} cacheKey - 缓存键
 * @param {string} url - 倍速音频URL
 */
function setCachedSpeedAdjustedUrl(cacheKey, url) {
  // 检查缓存大小，如果超过限制则清理最旧的
  if (speedAdjustedCache.size >= MAX_CACHE_SIZE) {
    const firstKey = speedAdjustedCache.keys().next().value;
    const oldUrl = speedAdjustedCache.get(firstKey);

    // 清理旧的 Blob URL
    if (oldUrl && oldUrl.startsWith("blob:")) {
      URL.revokeObjectURL(oldUrl);
      console.log(`清理旧的倍速音频缓存: ${firstKey}`);
    }

    speedAdjustedCache.delete(firstKey);
  }

  speedAdjustedCache.set(cacheKey, url);
}

/**
 * 预缓存常用的倍速音频
 * @param {Array} timeline - 时间线数组
 */
export async function precacheSpeedAdjustedAudio(timeline) {
  if (!Array.isArray(timeline)) {
    console.warn("时间线不是数组，跳过倍速音频预缓存");
    return;
  }

  // 收集所有需要倍速处理的音频
  const speedAdjustments = new Map();

  timeline.forEach((item) => {
    if (item.type === "audio" && item.audioUrl && item.speed !== 1.0) {
      const key = `${item.audioUrl}_${item.speed}`;
      speedAdjustments.set(key, {
        audioUrl: item.audioUrl,
        speed: item.speed,
      });
    }
  });

  console.log("开始预缓存倍速音频，数量:", speedAdjustments.size);

  // 预缓存这些倍速音频（并行处理，但限制并发数）
  const concurrency = 3; // 限制并发数
  const entries = Array.from(speedAdjustments.values());

  for (let i = 0; i < entries.length; i += concurrency) {
    const batch = entries.slice(i, i + concurrency);

    await Promise.allSettled(
      batch.map(({ audioUrl, speed }) =>
        getCachedSpeedAdjustedUrl(audioUrl, speed)
      )
    );
  }

  console.log(`倍速音频预缓存完成，共处理 ${speedAdjustments.size} 个音频`);
}

/**
 * 清理所有倍速音频缓存
 */
export function clearSpeedAdjustedCache() {
  console.log("开始清理倍速音频缓存");

  speedAdjustedCache.forEach((url, cacheKey) => {
    if (url && url.startsWith("blob:")) {
      URL.revokeObjectURL(url);
    }
  });

  speedAdjustedCache.clear();
  console.log("倍速音频缓存已清理");
}

/**
 * 获取缓存统计信息
 * @returns {Object} 缓存统计
 */
export function getSpeedAdjustedCacheStats() {
  return {
    size: speedAdjustedCache.size,
    maxSize: MAX_CACHE_SIZE,
    keys: Array.from(speedAdjustedCache.keys()),
  };
}

/**
 * 将AudioBuffer转换为MP3格式（使用lamejs）
 * @param {AudioBuffer} audioBuffer - 音频缓冲区
 * @returns {Uint8Array} MP3数据
 */
function audioBufferToMp3(audioBuffer) {
  try {
    const sampleRate = audioBuffer.sampleRate;
    const numChannels = audioBuffer.numberOfChannels;
    const length = audioBuffer.length;

    console.log(
      `🎵 使用lamejs编码MP3: 采样率=${sampleRate}, 声道=${numChannels}, 长度=${length}`
    );

    // 创建MP3编码器，先检查lamejs是否正确加载
    if (!lamejs || !lamejs.Mp3Encoder) {
      throw new Error("lamejs未正确加载");
    }

    const mp3encoder = new lamejs.Mp3Encoder(1, sampleRate, 192); // 强制单声道，提高码率

    const mp3Data = [];
    const sampleBlockSize = 1152; // MP3编码块大小

    // 获取第一个声道的数据并转换为16位整数，改善转换算法
    const channelData = audioBuffer.getChannelData(0);
    const int16Array = new Int16Array(channelData.length);
    for (let i = 0; i < channelData.length; i++) {
      // 改善转换算法，减少失真
      const sample = Math.max(-1, Math.min(1, channelData[i]));
      int16Array[i] = Math.round(sample * 32767);
    }

    // 分块编码
    for (let i = 0; i < length; i += sampleBlockSize) {
      const leftChunk = int16Array.subarray(i, i + sampleBlockSize);
      const rightChunk = leftChunk; // 单声道，左右声道相同

      const mp3buf = mp3encoder.encodeBuffer(leftChunk, rightChunk);
      if (mp3buf.length > 0) {
        mp3Data.push(mp3buf);
      }
    }

    // 完成编码
    const mp3buf = mp3encoder.flush();
    if (mp3buf.length > 0) {
      mp3Data.push(mp3buf);
    }

    // 合并所有MP3数据
    const totalLength = mp3Data.reduce((sum, chunk) => sum + chunk.length, 0);
    const result = new Uint8Array(totalLength);
    let offset = 0;
    for (const chunk of mp3Data) {
      result.set(chunk, offset);
      offset += chunk.length;
    }

    console.log(`✅ lamejs MP3编码完成: ${result.length} bytes`);
    return result;
  } catch (error) {
    console.error("lamejs MP3编码失败:", error);
    throw error;
  }
}




