# 音频切割功能需求文档

## 📋 功能概述

为Echo Lab编辑器的资源管理节点增加音频切割功能，允许用户上传完整音频文件，手动切割成片段，并与文本分句建立对应关系，作为TTS生成的替代方案。

## 🎯 核心需求

### 1. 基本功能需求

#### 1.1 音频切割
- **上传完整音频文件**：支持MP3、WAV、M4A格式
- **音频波形显示**：使用Canvas绘制音频波形图
- **手动切割标记**：用户可在波形图上点击添加切割点
- **自动静音检测**：可选的自动切割点检测功能
- **实时预览**：支持播放完整音频和切割片段

#### 1.2 文本关联
- **获取文本分句**：从文本内容节点获取已处理的分句数据
- **自动顺序对应**：默认按顺序将音频片段与文本分句对应
- **手动调整对应**：支持拖拽或点击调整对应关系
- **说话人信息继承**：从文本分句继承说话人、语言等信息

#### 1.3 音频片段处理
- **前端切割**：使用Web Audio API在前端切割音频
- **片段上传**：将切割后的音频片段上传到服务器（OSS）
- **URL获取**：获得独立的音频片段访问URL
- **数据保存**：保存到现有的audioItems数据结构

### 2. 数据结构需求

#### 2.1 数据库字段扩展
**只添加一个必要字段**：在Audio表中添加 `audio_source` 字段

```sql
-- 数据库字段
ALTER TABLE audios ADD COLUMN audio_source ENUM('tts', 'manual', 'upload')
NOT NULL DEFAULT 'tts'
COMMENT '音频来源：tts-TTS生成，manual-手动切割，upload-直接上传';

-- 添加索引
CREATE INDEX idx_audio_source ON audios (audio_source);
```

#### 2.2 前端数据结构
在现有audioItems结构基础上添加音频来源标记：

```javascript
audioItems: [
  {
    id: "seg_001",
    text: "こんにちは",
    language: "ja",
    type: "日语内容",
    speaker: "A",
    audioSource: "manual",  // 新增：音频来源标记
    url: "/oss-resources/audio_001.mp3",
    duration: 2.1,
    status: "success",

    // 保留TTS相关字段（便于切换）
    speed: 1.0,
    voice: "ja-JP-Standard-A",
    service: "google"
  }
]
```

#### 2.3 音频来源类型
- `"tts"`：TTS生成的音频
- `"manual"`：手动切割的音频
- `"upload"`：直接上传的音频（预留）

### 3. 界面交互需求

#### 3.1 资源管理节点标签页结构
保持现有的三个标签页结构：
- **"全部"标签页**：总览 + TTS批量操作
- **"源语言"标签页**：支持TTS生成和音频切割
- **"翻译语言"标签页**：仅支持TTS生成

#### 3.2 源语言标签页增强
```
┌─────────────────────────────────────────────────────────┐
│ 源语言音频 (日语)                                        │
├─────────────────────────────────────────────────────────┤
│ 音频生成策略：                                           │
│ ● 混合模式  ○ 全部TTS  ○ 全部手动切割                  │
├─────────────────────────────────────────────────────────┤
│ 批量操作：                                               │
│ [重新切割音频] [批量改为TTS] [生成缺失音频]              │
├─────────────────────────────────────────────────────────┤
│ 音频列表：                                               │
│ 1. こんにちは (A) [手动切割] ♪ [播放] [改为TTS]         │
│ 2. 元気ですか (B) [TTS生成] ♪ [播放] [改为手动]         │
└─────────────────────────────────────────────────────────┘
```

#### 3.3 音频切割界面
```
┌─────────────────────────────────────────────────────────┐
│ 音频切割工具                                             │
├─────────────────────────────────────────────────────────┤
│ 音频文件：conversation.mp3 (30.5秒)                     │
│ ████████████████████████████████████████████████████     │
│ 0:00    |    0:05    |    0:10    |    0:15    0:20     │
│       切割点1      切割点2      切割点3                  │
├─────────────────────────────────────────────────────────┤
│ 左侧：文本分句列表          右侧：音频片段列表            │
│ ┌─────────────────────┐   ┌─────────────────────────┐   │
│ │ 1. こんにちは        │   │ 片段1: 0:00-0:05 ♪     │   │
│ │ 2. 元気ですか        │   │ 片段2: 0:05-0:10 ♪     │   │
│ │ 3. はい、元気です    │   │ 片段3: 0:10-0:15 ♪     │   │
│ └─────────────────────┘   └─────────────────────────┘   │
├─────────────────────────────────────────────────────────┤
│ [播放] [暂停] [添加切割点] [自动检测静音] [确认并上传]   │
└─────────────────────────────────────────────────────────┘
```

### 4. 混合模式需求

#### 4.1 支持场景
- **纯TTS模式**：所有分句使用TTS生成
- **纯手动模式**：所有分句使用手动切割
- **混合模式**：部分分句手动切割，部分TTS生成

#### 4.2 灵活切换
- 每个分句可独立选择音频来源
- 支持从手动切割改为TTS生成
- 支持从TTS生成改为手动切割
- 支持重新生成任何音频项

#### 4.3 批量操作智能化
- **生成缺失音频**：询问用户选择TTS或手动切割
- **批量改变来源**：支持批量切换音频生成方式
- **增量处理**：只处理未生成或失败的音频项

### 5. 技术实现需求

#### 5.1 前端技术栈
- **音频处理**：Web Audio API
- **波形显示**：Canvas绘制或WaveSurfer.js
- **文件上传**：复用现有的OSS上传接口
- **状态管理**：集成到现有的Pinia stores

#### 5.2 后端接口需求
- **音频上传接口**：复用现有的文件上传API
- **音频元数据存储**：扩展现有的audio模型
- **批量处理支持**：支持批量音频片段上传

#### 5.3 数据流向
```
完整音频文件 → 前端切割 → 音频片段Blob → 上传OSS → 获得URL → 立即保存数据库 → 更新audioItems
```

#### 5.4 数据持久化策略

##### 5.4.1 保存时机
**采用立即保存策略**：音频切割完成后立即保存到数据库，与TTS生成保持一致的保存时机。

```javascript
// 音频切割完成后的保存流程
async function confirmAudioCutting() {
  try {
    // 1. 批量上传音频片段到OSS
    const uploadResults = await uploadAudioSegments(audioSegments);

    // 2. 构造音频数据（只包含必要字段）
    const audioData = audioSegments.map((segment, index) => ({
      text: segment.text,
      language: segment.language,
      speaker: segment.speaker || 'default',
      audioUrl: uploadResults[index].url,
      duration: segment.duration,
      speed: 1.0,
      audioSource: "manual"
    }));

    // 3. 批量保存到数据库
    await saveAudioToDatabase(audioData);

    // 4. 更新前端状态
    updateAudioItems(audioData);

  } catch (error) {
    // 错误处理和重试机制
    handleSaveError(error);
  }
}
```

##### 5.4.2 数据库模型扩展
```javascript
// Audio模型只需要添加audioSource字段
const Audio = sequelize.define('Audio', {
  // 现有字段...
  text: { type: DataTypes.TEXT, allowNull: false },
  language: { type: DataTypes.STRING(10), allowNull: false },
  speed: { type: DataTypes.FLOAT, defaultValue: 1.0 },
  speaker: { type: DataTypes.STRING(50), allowNull: false },
  ossUrl: { type: DataTypes.STRING(255), allowNull: false },
  ossKey: { type: DataTypes.STRING(255), allowNull: false },
  duration: { type: DataTypes.FLOAT, allowNull: false },
  md5Hash: { type: DataTypes.STRING(32), allowNull: false },

  // 新增字段：音频来源
  audioSource: {
    type: DataTypes.ENUM('tts', 'manual', 'upload'),
    defaultValue: 'tts',
    allowNull: false,
    comment: '音频来源：tts-TTS生成，manual-手动切割，upload-直接上传'
  }
});
```

##### 5.4.3 音频保存API
```javascript
// POST /api/audio - 保存音频记录（简化版本）
router.post('/audio', async (req, res) => {
  try {
    const {
      text,
      language,
      speed = 1.0,
      speaker,
      audioUrl,
      duration,
      audioSource = "tts",
      md5Hash
    } = req.body;

    // 验证必需字段
    if (!text || !language || !speaker || !audioUrl || duration === undefined) {
      return res.status(400).json({
        success: false,
        error: "缺少必需字段: text, language, speaker, audioUrl, duration"
      });
    }

    // 检查是否已存在相同的音频记录（基于MD5）
    let existingAudio = null;
    if (md5Hash) {
      existingAudio = await Audio.findOne({ where: { md5Hash } });
    }

    let audioRecord;
    if (existingAudio) {
      // 更新现有记录
      await existingAudio.update({
        text, language, speed, speaker,
        ossUrl: audioUrl,
        ossKey: extractOssKey(audioUrl),
        duration, audioSource, md5Hash
      });
      audioRecord = existingAudio;
    } else {
      // 创建新记录
      audioRecord = await Audio.create({
        text, language, speed, speaker,
        ossUrl: audioUrl,
        ossKey: extractOssKey(audioUrl),
        duration, audioSource,
        md5Hash: md5Hash || generateMd5Hash(text, language, speed, speaker)
      });
    }

    res.json({ success: true, audio: audioRecord });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// POST /api/audio/upload - 音频文件上传接口
router.post('/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, error: "未提供音频文件" });
    }

    const { language = "auto", type = "manual" } = req.body;
    const audioId = `audio_${nanoid()}`;
    const format = getFileExtension(req.file.mimetype);
    const ossPath = `audio/${type}/${audioId}.${format}`;

    // 上传到OSS
    const ossResult = await ossService.put(ossPath, req.file.buffer, {
      mime: req.file.mimetype
    });

    res.json({
      success: true,
      data: {
        audioId, url: ossResult.url, ossKey: ossPath,
        format, size: req.file.size, originalName: req.file.originalname
      }
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});
```

##### 5.4.4 错误处理和重试机制
- **保存失败重试**：最多重试3次，间隔递增
- **部分保存失败**：支持单个音频项重新保存
- **网络异常处理**：提供用户友好的错误提示
- **数据一致性**：确保前端状态与数据库同步

### 6. 用户体验需求

#### 6.1 操作流程
1. 在源语言标签页选择"音频切割"模式
2. 上传完整音频文件，显示波形图
3. 手动添加切割点或使用自动检测
4. 预览音频片段和文本对应关系
5. 确认后批量上传音频片段
6. 完成后可在各标签页查看和管理

#### 6.2 错误处理
- **上传失败重试**：支持单个片段重新上传
- **切割错误恢复**：可重新调整切割点
- **格式兼容性**：提示不支持的音频格式
- **网络异常处理**：上传进度显示和断点续传
- **保存失败处理**：数据库保存失败时的重试和恢复机制

#### 6.3 性能优化
- **大文件处理**：支持较大音频文件的波形显示
- **切割效率**：前端切割避免服务器压力
- **缓存机制**：避免重复上传相同的音频片段

### 7. 与现有系统集成

#### 7.1 数据兼容性
- 保持现有Audio数据库表结构
- 只添加audioSource字段，不破坏现有功能
- 向后兼容：没有audioSource字段的记录默认为"tts"
- 前端audioItems只是临时状态，不存储额外字段

#### 7.2 功能集成
- 与现有TTS功能并存
- 与翻译、标注功能正常协作
- 播放器正常播放手动切割的音频

#### 7.3 权限控制
- 复用现有的用户权限系统
- 音频切割功能可能需要特定用户等级
- 文件上传大小限制

## 🎯 开发优先级

### Phase 1: 基础功能（MVP）
- [ ] 音频文件上传和波形显示
- [ ] 手动切割点添加
- [ ] 基础的文本-音频对应
- [ ] 音频片段上传到OSS
- [ ] 数据库保存功能（立即保存策略）
- [ ] 前端状态更新

### Phase 2: 交互优化
- [ ] 自动静音检测
- [ ] 拖拽调整对应关系
- [ ] 实时预览和播放
- [ ] 错误处理和重试机制
- [ ] 保存进度提示和状态反馈

### Phase 3: 高级功能
- [ ] 混合模式完整支持
- [ ] 批量操作优化
- [ ] 性能优化和大文件支持
- [ ] 用户体验细节完善
- [ ] 数据一致性检查和修复工具

## 📝 注意事项

1. **音频质量**：手动切割的音频质量取决于原始录音
2. **文件大小**：需要考虑音频文件上传的大小限制
3. **浏览器兼容性**：Web Audio API的浏览器支持情况
4. **存储成本**：音频片段会增加OSS存储使用量
5. **用户引导**：需要提供清晰的操作指引和帮助文档
6. **数据简洁性**：数据库只存储必要字段，避免冗余信息
7. **保存时机统一**：手动切割和TTS生成都采用立即保存策略
8. **错误恢复**：提供完善的保存失败重试和数据恢复机制

---

*本文档版本：v1.0*
*最后更新：2024年12月*
*负责人：开发团队*
