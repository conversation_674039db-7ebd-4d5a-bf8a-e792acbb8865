<!--
  推荐内容组件
  基于用户等级显示个性化推荐内容
-->
<template>
  <div class="recommended-content">
    <!-- 推荐标题 -->
    <div class="recommend-header">
      <h2 class="recommend-title">
        <el-icon class="title-icon">
          <i-ep-star />
        </el-icon>
        {{ headerTitle }}
      </h2>
      <el-button 
        v-if="userLevel" 
        link 
        @click="goToLevelSettings"
        class="change-level-btn"
      >
        切换等级
      </el-button>
    </div>

    <!-- 无等级提示 -->
    <div v-if="!userLevel" class="no-level-tip">
      <el-empty description="请先选择你的日语水平" :image-size="80">
        <el-button type="primary" @click="goToLevelSettings">
          选择等级
        </el-button>
      </el-empty>
    </div>

    <!-- 推荐内容 -->
    <div v-else class="content-grid" :class="{ 'mobile-layout': isMobile }">
      <!-- 加载状态 -->
      <div v-if="loading" class="grid-loading">
        <el-skeleton :rows="2" animated />
        <el-skeleton :rows="2" animated />
      </div>

      <!-- 内容为空 -->
      <div v-else-if="contents.length === 0" class="grid-empty">
        <el-empty description="暂无适合你等级的内容" :image-size="100">
          <el-button @click="$emit('view-all')">查看全部内容</el-button>
        </el-empty>
      </div>

      <!-- 内容网格 -->
      <div v-else class="grid-container">
        <PublicContentCard 
          v-for="content in contents" 
          :key="content.id" 
          :content="content"
          class="grid-item"
        />
      </div>

      <!-- 加载更多 -->
      <div v-if="hasMore" class="load-more-section">
        <el-button 
          type="primary" 
          size="large" 
          :loading="loading" 
          @click="$emit('load-more')"
          class="load-more-btn"
        >
          {{ loading ? '加载中...' : '加载更多' }}
        </el-button>
      </div>

      <!-- 统计信息 -->
      <div v-if="total > 0" class="content-stats">
        <span class="stats-text">
          已显示 {{ contents.length }} / {{ total }} 个内容
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { isMobileDevice } from '@/utils/deviceDetector';
import { useLanguageStore } from '@/stores/languageStore';
import PublicContentCard from '@/components/content/PublicContentCard.vue';

const props = defineProps({
  userLevel: String,
  contents: { type: Array, default: () => [] },
  loading: Boolean,
  hasMore: Boolean,
  total: { type: Number, default: 0 }
});

defineEmits(['view-all', 'load-more']);

const router = useRouter();
const languageStore = useLanguageStore();

const goToLevelSettings = () => {
  router.push('/level-settings');
};

const isMobile = computed(() => isMobileDevice());

const headerTitle = computed(() => {
  if (!props.userLevel) return '个性化推荐';
  // 使用新的 languageStore 获取等级信息
  const levelInfo = languageStore.currentLevelInfo;
  return `为${levelInfo?.name || props.userLevel}推荐`;
});
</script>

<style scoped>
.recommended-content {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.06);
  border: 0.0625rem solid #f0f2f5;
}

.recommend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 0.125rem solid #f0f2f5;
}

.recommend-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.title-icon {
  font-size: 1.125rem;
  color: #409eff;
}

.change-level-btn {
  font-size: 0.875rem;
  padding: 0.25rem 0.5rem;
}

.no-level-tip {
  padding: 2rem 0;
  text-align: center;
}

.content-grid {
  width: 100%;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(18rem, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.grid-item {
  height: 100%;
}

.grid-loading {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(18rem, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.grid-empty {
  padding: 3rem 0;
  text-align: center;
}

.load-more-section {
  margin-top: 2rem;
  display: flex;
  justify-content: center;
}

.load-more-btn {
  min-width: 8rem;
  border-radius: 2rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.load-more-btn:hover {
  transform: translateY(-0.125rem);
  box-shadow: 0 0.5rem 1rem rgba(64, 158, 255, 0.3);
}

.content-stats {
  text-align: center;
  padding: 1rem 0;
  color: #909399;
  font-size: 0.875rem;
  border-top: 0.0625rem solid #f0f2f5;
  margin-top: 1rem;
}

.stats-text {
  background-color: #f8f9fa;
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  display: inline-block;
}

/* 移动端优化 */
.mobile-layout .grid-container {
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.mobile-layout .grid-loading {
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.mobile-layout .grid-empty {
  padding: 2rem 0;
}

.mobile-layout .load-more-section {
  margin-top: 1.5rem;
}

.mobile-layout .load-more-btn {
  min-width: 7rem;
  font-size: 0.875rem;
}

.mobile-layout .recommended-content {
  padding: 0.75rem;
  border-radius: 0.5rem;
}

.mobile-layout .recommend-header {
  margin-bottom: 0.75rem;
  padding-bottom: 0.375rem;
}

.mobile-layout .recommend-title {
  font-size: 1.125rem;
}

.mobile-layout .content-stats {
  font-size: 0.75rem;
  padding: 0.75rem 0;
}
</style>