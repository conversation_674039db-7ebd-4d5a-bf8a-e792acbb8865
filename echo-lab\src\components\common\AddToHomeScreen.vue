<template>
  <div class="add-to-home-screen">
    <el-popover :visible="popoverVisible" @update:visible="popoverVisible = $event" placement="bottom"
      :width="isMobile ? 300 : 400" trigger="manual">
      <template #reference>
        <div></div>
      </template>

      <div class="popover-content">
        <div class="popover-header">
          <div class="header-title">
            <el-icon class="header-icon">
              <i-ep-download />
            </el-icon>
            <h3>{{ isMobile ? "添加到主屏幕" : "添加到桌面" }}</h3>
          </div>
          <el-button type="primary" link @click="closePopover">
            <el-icon>
              <i-ep-close />
            </el-icon>
          </el-button>
        </div>

        <div class="popover-body">
          <div v-if="isIOS" class="instruction-container">
            <h4>iOS Safari 浏览器</h4>
            <div class="steps">
              <div class="step">
                <div class="step-number">1</div>
                <div class="step-text">点击底部的<strong>分享按钮</strong> <span class="icon">⎅</span></div>
              </div>
              <div class="step">
                <div class="step-number">2</div>
                <div class="step-text">在弹出的菜单中，滚动并点击<strong>添加到主屏幕</strong></div>
              </div>
              <div class="step">
                <div class="step-number">3</div>
                <div class="step-text">点击右上角的<strong>添加</strong>按钮完成</div>
              </div>
            </div>
          </div>

          <div v-else-if="isAndroid" class="instruction-container">
            <h4>Android Chrome 浏览器</h4>
            <div class="steps">
              <div class="step">
                <div class="step-number">1</div>
                <div class="step-text">点击右上角的<strong>菜单按钮</strong> <span class="icon">⋮</span></div>
              </div>
              <div class="step">
                <div class="step-number">2</div>
                <div class="step-text">选择<strong>添加到主屏幕</strong>选项</div>
              </div>
              <div class="step">
                <div class="step-number">3</div>
                <div class="step-text">点击<strong>添加</strong>按钮完成</div>
              </div>
            </div>
          </div>

          <div v-else-if="isChrome" class="instruction-container">
            <h4>Chrome 浏览器</h4>
            <div class="steps">
              <div class="step">
                <div class="step-number">1</div>
                <div class="step-text">点击右上角的<strong>菜单按钮</strong> <span class="icon">⋮</span></div>
              </div>
              <div class="step">
                <div class="step-number">2</div>
                <div class="step-text">选择<strong>更多工具</strong> > <strong>创建快捷方式</strong></div>
              </div>
              <div class="step">
                <div class="step-number">3</div>
                <div class="step-text">勾选<strong>在窗口中打开</strong>选项</div>
              </div>
              <div class="step">
                <div class="step-number">4</div>
                <div class="step-text">点击<strong>创建</strong>按钮完成</div>
              </div>
            </div>
          </div>

          <div v-else-if="isEdge" class="instruction-container">
            <h4>Edge 浏览器</h4>
            <div class="steps">
              <div class="step">
                <div class="step-number">1</div>
                <div class="step-text">点击右上角的<strong>菜单按钮</strong> <span class="icon">⋯</span></div>
              </div>
              <div class="step">
                <div class="step-number">2</div>
                <div class="step-text">选择<strong>应用</strong> > <strong>安装网站为应用</strong></div>
              </div>
              <div class="step">
                <div class="step-number">3</div>
                <div class="step-text">点击<strong>安装</strong>按钮完成</div>
              </div>
            </div>
          </div>

          <div v-else-if="isFirefox" class="instruction-container">
            <h4>Firefox 浏览器</h4>
            <div class="steps">
              <div class="step">
                <div class="step-number">1</div>
                <div class="step-text">右键点击页面空白处</div>
              </div>
              <div class="step">
                <div class="step-number">2</div>
                <div class="step-text">选择<strong>创建新的快捷方式</strong></div>
              </div>
              <div class="step">
                <div class="step-number">3</div>
                <div class="step-text">点击<strong>创建</strong>按钮完成</div>
              </div>
            </div>
          </div>

          <div v-else class="instruction-container">
            <h4>其他浏览器</h4>
            <div class="steps">
              <div class="step">
                <div class="step-number">1</div>
                <div class="step-text">在浏览器菜单中查找<strong>添加到主屏幕</strong>或<strong>创建快捷方式</strong>选项</div>
              </div>
              <div class="step">
                <div class="step-number">2</div>
                <div class="step-text">按照浏览器提示完成添加</div>
              </div>
            </div>
          </div>
        </div>

        <div class="popover-footer">
          <el-button type="success" @click="installPWA" :disabled="!installable">
            一键安装
            <el-tooltip content="您的浏览器支持一键安装功能" placement="top" v-if="installable">
              <el-icon class="support-icon">
                <i-ep-check />
              </el-icon>
            </el-tooltip>
            <el-tooltip content="您的浏览器不支持一键安装，请按照上方步骤手动添加" placement="top" v-else>
              <el-icon class="unsupport-icon">
                <i-ep-info-filled />
              </el-icon>
            </el-tooltip>
          </el-button>
          <el-button @click="closePopover">关闭</el-button>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { isMobileDevice, getBrowserInfo, isPWAInstallable } from '@/utils/deviceDetector';

// 状态
const popoverVisible = ref(false);
const installable = ref(false);

// 计算属性
const isMobile = computed(() => isMobileDevice());

// 获取浏览器信息
const browserInfo = computed(() => getBrowserInfo());

// 检测浏览器类型
const isIOS = computed(() => browserInfo.value.isIOS);
const isAndroid = computed(() => browserInfo.value.isAndroid);
const isChrome = computed(() => browserInfo.value.name === 'Chrome');
const isEdge = computed(() => browserInfo.value.name === 'Edge');
const isFirefox = computed(() => browserInfo.value.name === 'Firefox');

// 显示弹窗
const showPopover = () => {
  // 始终显示弹窗，不自动触发安装
  popoverVisible.value = true;
};

// 关闭弹窗
const closePopover = () => {
  popoverVisible.value = false;
};

// 检测是否已安装
let deferredPrompt = null;

// 安装PWA
const installPWA = async () => {
  // 确保这个函数只在用户点击按钮时调用
  if (!deferredPrompt) {
    // 提供更详细的信息说明为什么不能自动安装
    if (window.matchMedia('(display-mode: standalone)').matches) {
      ElMessage.info('应用已经安装到您的设备上');
    } else if (!isPWAInstallable()) {
      ElMessage.info('您的浏览器不支持PWA功能，请按照指引手动添加到主屏幕');
    } else {
      ElMessage.info('浏览器暂时不允许安装，可能是因为您需要先使用网站一段时间，请按照指引手动添加到主屏幕');
    }
    return;
  }

  try {
    console.log('尝试显示安装提示...');

    // 显示安装提示 - 这必须由用户手势触发
    // 这里不会自动调用，只有在用户点击按钮时才会调用
    deferredPrompt.prompt();

    // 等待用户响应
    const { outcome } = await deferredPrompt.userChoice;
    console.log('用户选择结果:', outcome);

    // 根据用户选择显示消息
    if (outcome === 'accepted') {
      ElMessage.success('安装成功！');
      installable.value = false;
      // 安装成功后关闭弹窗
      popoverVisible.value = false;
    } else {
      ElMessage.info('您取消了安装');
    }

    // 清除提示，因为它只能使用一次
    deferredPrompt = null;
  } catch (error) {
    console.error('安装PWA时出错:', error);
    ElMessage.error('安装过程中出现错误，请尝试手动安装');
  }
};

// 导出方法供外部调用
defineExpose({
  showPopover,
  closePopover,
  installPWA
});

onMounted(() => {
  // 检测是否支持PWA
  const isPwaSupported = isPWAInstallable();
  installable.value = isPwaSupported;
  console.log('PWA 支持状态:', isPwaSupported);
  console.log('Service Worker 支持:', 'serviceWorker' in navigator);
  console.log('是否已安装为独立应用:', window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone === true);

  // 监听 beforeinstallprompt 事件
  window.addEventListener('beforeinstallprompt', (e) => {
    // 阻止 Chrome 67 及更早版本自动显示安装提示
    e.preventDefault();
    console.log('beforeinstallprompt 事件已触发!');
    // 保存事件，以便稍后触发
    deferredPrompt = e;
    installable.value = true;
  });

  // 监听 appinstalled 事件
  window.addEventListener('appinstalled', () => {
    // 应用安装完成
    console.log('appinstalled 事件已触发!');
    ElMessage.success('应用已成功安装到您的设备！');
    installable.value = false;
    deferredPrompt = null;
  });

  // 延迟检查 manifest 是否正确加载，确保 DOM 已完全加载
  setTimeout(() => {
    // 获取所有 manifest 链接
    const manifestLinks = document.querySelectorAll('link[rel="manifest"]');
    console.log('Manifest 链接数量:', manifestLinks.length);

    // 如果有多个相同的 manifest 链接，只保留一个
    if (manifestLinks.length > 1) {
      // 创建一个 Set 来存储唯一的 href 值
      const uniqueHrefs = new Set();

      // 遍历所有 manifest 链接
      manifestLinks.forEach((link, index) => {
        console.log(`Manifest ${index + 1}:`, link.href);

        // 如果这个 href 已经存在，移除这个链接
        if (uniqueHrefs.has(link.href)) {
          link.parentNode.removeChild(link);
        } else {
          uniqueHrefs.add(link.href);
        }
      });

      console.log('清理后的 Manifest 链接数量:', document.querySelectorAll('link[rel="manifest"]').length);
    }

    // 如果没有找到 manifest 链接，记录日志但不手动添加
    if (manifestLinks.length === 0) {
      console.log('未找到 manifest 链接，PWA 功能可能无法正常工作');
    }
  }, 1000);
});
</script>

<style scoped>
.add-to-home-screen {
  display: inline-block;
}

.popover-content {
  padding: 0;
}

.popover-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #ebeef5;
  background-color: #f0f9eb;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-icon {
  color: #67c23a;
  font-size: 1.25rem;
}

.popover-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: #303133;
}

.popover-body {
  padding: 1rem;
  max-height: 70vh;
  overflow-y: auto;
}

.instruction-container {
  margin-bottom: 1rem;
}

.instruction-container h4 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-size: 0.9375rem;
  color: #303133;
}

.steps {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.step-number {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 1.5rem;
  height: 1.5rem;
  background-color: #67c23a;
  color: white;
  border-radius: 50%;
  font-size: 0.875rem;
  font-weight: 500;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(103, 194, 58, 0.3);
}

.step-text {
  font-size: 0.875rem;
  line-height: 1.5;
  color: #606266;
}

.icon {
  display: inline-block;
  font-size: 1.125rem;
  vertical-align: middle;
  margin-left: 0.25rem;
}

.popover-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-top: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

.support-icon {
  color: #67c23a;
  margin-left: 0.25rem;
  font-size: 0.875rem;
}

.unsupport-icon {
  color: #e6a23c;
  margin-left: 0.25rem;
  font-size: 0.875rem;
}
</style>
