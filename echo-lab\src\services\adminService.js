/**
 * 管理员服务
 * 提供管理后台相关的API调用
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

/**
 * 获取所有用户等级
 * @returns {Promise<Array>} 用户等级列表
 */
export async function getAllLevels() {
  try {
    const response = await httpClient.get(API_ENDPOINTS.ADMIN.LEVELS);

    if (response && response.success) {
      return response.levels;
    }

    return [];
  } catch (error) {
    console.error("获取用户等级失败:", error);
    throw error;
  }
}

/**
 * 创建用户等级
 * @param {Object} levelData 等级数据
 * @returns {Promise<Object>} 创建结果
 */
export async function createLevel(levelData) {
  try {
    const response = await httpClient.post(
      API_ENDPOINTS.ADMIN.LEVELS,
      levelData
    );

    if (response && response.success) {
      return response.level;
    }

    throw new Error(response?.error || "创建等级失败");
  } catch (error) {
    console.error("创建等级失败:", error);
    throw error;
  }
}

/**
 * 更新用户等级
 * @param {number} id 等级ID
 * @param {Object} levelData 等级数据
 * @returns {Promise<Object>} 更新结果
 */
export async function updateLevel(id, levelData) {
  try {
    const response = await httpClient.put(
      `${API_ENDPOINTS.ADMIN.LEVELS}/${id}`,
      levelData
    );

    if (response && response.success) {
      return response.level;
    }

    throw new Error(response?.error || "更新等级失败");
  } catch (error) {
    console.error("更新等级失败:", error);
    throw error;
  }
}

/**
 * 获取等级权限
 * @param {number} level 等级
 * @returns {Promise<Array>} 权限列表
 */
export async function getLevelPermissions(level) {
  try {
    const response = await httpClient.get(
      `/api/admin/permissions/levels/${level}`
    );

    if (response && response.success) {
      return response.permissions;
    }

    return [];
  } catch (error) {
    console.error("获取等级权限失败:", error);
    throw error;
  }
}

/**
 * 更新等级权限
 * @param {number} level 等级
 * @param {Array<string>} features 功能标识符列表
 * @returns {Promise<Object>} 更新结果
 */
export async function updateLevelPermissions(level, features) {
  try {
    const response = await httpClient.put(
      `/api/admin/permissions/levels/${level}`,
      { features }
    );

    if (response && response.success) {
      return response;
    }

    throw new Error(response?.error || "更新等级权限失败");
  } catch (error) {
    console.error("更新等级权限失败:", error);
    throw error;
  }
}

/**
 * 获取等级使用限制
 * @param {number} level 等级
 * @returns {Promise<Array>} 使用限制列表
 */
export async function getLevelLimits(level) {
  try {
    const response = await httpClient.get(
      `/api/admin/permissions/limits/${level}`
    );

    if (response && response.success) {
      return response.limits;
    }

    return [];
  } catch (error) {
    console.error("获取使用限制失败:", error);
    throw error;
  }
}

/**
 * 更新等级使用限制
 * @param {number} level 等级
 * @param {Array<Object>} limits 使用限制列表
 * @returns {Promise<Object>} 更新结果
 */
export async function updateLevelLimits(level, limits) {
  try {
    const response = await httpClient.put(
      `/api/admin/permissions/limits/${level}`,
      { limits }
    );

    if (response && response.success) {
      return response;
    }

    throw new Error(response?.error || "更新使用限制失败");
  } catch (error) {
    console.error("更新使用限制失败:", error);
    throw error;
  }
}

/**
 * 获取订阅列表
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 订阅列表和分页信息
 */
export async function getSubscriptions(params) {
  try {
    const response = await httpClient.get(
      API_ENDPOINTS.ADMIN.SUBSCRIPTIONS,
      params
    );

    if (response && response.success) {
      return {
        subscriptions: response.subscriptions,
        pagination: response.pagination,
      };
    }

    return { subscriptions: [], pagination: { total: 0 } };
  } catch (error) {
    console.error("获取订阅列表失败:", error);
    throw error;
  }
}

/**
 * 创建订阅
 * @param {Object} subscriptionData 订阅数据
 * @returns {Promise<Object>} 创建结果
 */
export async function createSubscription(subscriptionData) {
  try {
    const response = await httpClient.post(
      API_ENDPOINTS.ADMIN.SUBSCRIPTIONS,
      subscriptionData
    );

    if (response && response.success) {
      return response.subscription;
    }

    throw new Error(response?.error || "创建订阅失败");
  } catch (error) {
    console.error("创建订阅失败:", error);
    throw error;
  }
}

/**
 * 更新订阅状态
 * @param {number} id 订阅ID
 * @param {string} status 状态
 * @returns {Promise<Object>} 更新结果
 */
export async function updateSubscriptionStatus(id, status) {
  try {
    const response = await httpClient.put(
      `${API_ENDPOINTS.ADMIN.SUBSCRIPTIONS}/${id}/status`,
      { status }
    );

    if (response && response.success) {
      return response.subscription;
    }

    throw new Error(response?.error || "更新订阅状态失败");
  } catch (error) {
    console.error("更新订阅状态失败:", error);
    throw error;
  }
}

/**
 * 删除订阅
 * @param {number} id 订阅ID
 * @returns {Promise<boolean>} 是否成功
 */
export async function deleteSubscriptionById(id) {
  try {
    const response = await httpClient.delete(
      `${API_ENDPOINTS.ADMIN.SUBSCRIPTIONS}/${id}`
    );

    return response && response.success;
  } catch (error) {
    console.error("删除订阅失败:", error);
    throw error;
  }
}

/**
 * 搜索用户
 * @param {string} query 搜索关键词
 * @returns {Promise<Array>} 用户列表
 */
export async function searchUsersByQuery(query) {
  try {
    const response = await httpClient.get(
      `${API_ENDPOINTS.ADMIN.USERS}/search`,
      { query }
    );

    if (response && response.success) {
      return response.users;
    }

    return [];
  } catch (error) {
    console.error("搜索用户失败:", error);
    throw error;
  }
}

/**
 * 获取功能使用统计
 * @param {string} startDate 开始日期
 * @param {string} endDate 结束日期
 * @returns {Promise<Array>} 统计数据
 */
export async function getUsageStatistics(startDate, endDate) {
  try {
    const response = await httpClient.get(
      `${API_ENDPOINTS.ADMIN.STATISTICS}/features`,
      { startDate, endDate }
    );

    if (response && response.success) {
      return response.statistics;
    }

    return [];
  } catch (error) {
    console.error("获取功能使用统计失败:", error);
    throw error;
  }
}

/**
 * 获取用户使用排行
 * @param {string} startDate 开始日期
 * @param {string} endDate 结束日期
 * @param {number} limit 限制数量
 * @returns {Promise<Array>} 排行数据
 */
export async function getUserRankings(startDate, endDate, limit = 10) {
  try {
    const response = await httpClient.get(
      `${API_ENDPOINTS.ADMIN.STATISTICS}/users`,
      { startDate, endDate, limit }
    );

    if (response && response.success) {
      return response.rankings;
    }

    return [];
  } catch (error) {
    console.error("获取用户使用排行失败:", error);
    throw error;
  }
}

export default {
  getAllLevels,
  createLevel,
  updateLevel,
  getLevelPermissions,
  updateLevelPermissions,
  getLevelLimits,
  updateLevelLimits,
  getSubscriptions,
  createSubscription,
  updateSubscriptionStatus,
  deleteSubscriptionById,
  searchUsersByQuery,
  getUsageStatistics,
  getUserRankings,
};
