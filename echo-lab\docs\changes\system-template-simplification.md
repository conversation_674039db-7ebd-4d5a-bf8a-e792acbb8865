# 系统播放模板简化

## 📋 变更概述

根据用户需求，将系统预设的播放模板从 4 个简化为 2 个，并重新配置模板内容。

## 🔄 变更内容

### 变更前（4个模板）
1. **初学者模式** (`system_beginner`) - 慢速多重复，带翻译辅助
2. **进阶模式** (`system_intermediate`) - 正常语速，适度重复
3. **高级模式** (`system_advanced`) - 快速播放，少重复
4. **强化练习** (`system_intensive`) - 两环节强化练习

### 变更后（2个模板）

#### 1. 轻松模式 (`system_easy`)
- **描述**: 两个环节，第一个环节重复4遍（第2遍后插入关键词，第3遍后插入翻译），第二个环节重复1遍
- **配置**:
  - **环节1 - 重复练习**:
    - 重复次数: 4次
    - 语速: 1.0x
    - 停顿: 3000ms
    - 关键词: 启用，第2遍后插入
    - 翻译: 启用，第3遍后插入（中文）
  - **环节2 - 通读验证**:
    - 重复次数: 1次
    - 语速: 1.0x
    - 停顿: 3000ms
    - 关键词: 禁用
    - 翻译: 禁用

#### 2. 学习模式 (`system_learning`)
- **描述**: 三个环节，第一个环节重复1遍，第二个环节重复4遍（第2遍后插入关键词，第3遍后插入翻译），第三个环节重复1遍
- **配置**:
  - **环节1 - 初步了解**:
    - 重复次数: 1次
    - 语速: 1.0x
    - 停顿: 3000ms
    - 关键词: 禁用
    - 翻译: 禁用
  - **环节2 - 重点练习**:
    - 重复次数: 4次
    - 语速: 1.0x
    - 停顿: 3000ms
    - 关键词: 启用，第2遍后插入
    - 翻译: 启用，第3遍后插入（中文）
  - **环节3 - 巩固复习**:
    - 重复次数: 1次
    - 语速: 1.0x
    - 停顿: 3000ms
    - 关键词: 禁用
    - 翻译: 禁用

## 📁 修改的文件

### 后端文件
- **`backend/services/playbackTemplateService.js`**
  - 第 17-126 行：重写 `SYSTEM_TEMPLATES` 常量
  - 移除了 `beginner`、`intermediate`、`advanced`、`intensive` 模板
  - 新增了 `easy` 和 `learning` 模板

### 前端文件
- **`echo-lab/src/composables/usePlayerConfig.js`**
  - 第 178-180 行：更新默认模板引用从 `system_beginner` 改为 `system_easy`

## 🎯 设计理念

### 统一的配置原则
1. **语速统一**: 所有环节都使用 1.0x 语速，简化用户理解
2. **停顿统一**: 所有环节都使用 3000ms 停顿时间，保持一致性
3. **插入位置固定**: 关键词固定在第2遍后，翻译固定在第3遍后
4. **翻译语言统一**: 统一使用中文翻译（zh-CN）

### 模板差异化
- **轻松模式**: 2个环节，适合快速学习
- **学习模式**: 3个环节，适合深度学习

## 🔧 技术细节

### 模板变更
- 完全移除旧模板：`system_beginner`、`system_intermediate`、`system_advanced`、`system_intensive`
- 新增模板：`system_easy`、`system_learning`
- 默认模板：`system_easy`（轻松模式）

### 无兼容性处理
- 旧的模板ID将返回 404 错误
- 不提供任何向后兼容
- 用户需要重新选择模板

## 🚀 部署说明

1. **后端部署**: 重启后端服务即可生效，无需数据库迁移
2. **前端部署**: 重新构建前端应用
3. **用户影响**:
   - **开发阶段**: 直接替换，无需考虑兼容性
   - **默认行为**: 使用 configjson 自带的播放策略信息
   - **模板作用**: 仅在用户主动选择时应用

## ✅ 验证方法

### API 测试
```bash
# 获取所有系统模板
curl -X GET "http://localhost:3000/api/templates?type=system"

# 获取轻松模式模板
curl -X GET "http://localhost:3000/api/templates/system_easy"

# 获取学习模式模板
curl -X GET "http://localhost:3000/api/templates/system_learning"

# 验证旧模板已删除（应返回404）
curl -X GET "http://localhost:3000/api/templates/system_beginner"
```

### 前端测试
1. 访问播放器页面
2. 检查模板选择器中只显示2个系统模板
3. 验证默认使用 configjson 自带的播放策略
4. 测试手动应用模板的效果

## 📊 预期效果

1. **简化用户选择**: 从4个模板减少到2个，降低选择困难
2. **统一用户体验**: 所有模板使用相同的语速和停顿时间
3. **明确功能定位**: 轻松模式适合快速学习，学习模式适合深度学习
4. **减少维护成本**: 更少的模板意味着更少的测试和维护工作

## 🔄 回滚方案

如需回滚到原来的4个模板：
1. 通过 Git 历史恢复原始模板配置
2. 重启后端服务
3. 重新构建前端应用

注意：回滚后用户需要重新配置模板，因为不提供兼容性处理。
