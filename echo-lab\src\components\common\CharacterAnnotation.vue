<!--
  字符级标注组件
  用于显示汉字上方的假名标注
-->
<template>
  <div class="character-annotation-wrapper">
    <div class="annotated-word">
      <span v-for="(char, index) in characters" :key="index" class="annotated-char" @click="editCharacter(index)">
        <span class="furigana-container">
          <span v-if="needsFurigana(char)" class="furigana">{{ char.reading }}</span>
          <span :class="getCharClass(char)" class="base-char">{{ char.char }}</span>
        </span>
      </span>
    </div>

    <!-- 编辑假名标注对话框 -->
    <standard-dialog v-model="editDialogVisible" title="编辑假名标注" width="50%" :show-confirm="true" confirm-text="保存"
      cancel-text="取消" @confirm="saveEdit" @cancel="cancelEdit">
      <div v-if="editingIndex !== null" class="edit-dialog-content">
        <div class="selected-text-preview">
          <div class="selected-text-label">选中文本：</div>
          <div class="character-preview">
            <span class="character-large">{{ characters[editingIndex].char }}</span>
          </div>
        </div>

        <div class="annotation-mode-selector">
          <div class="mode-label">标注模式：</div>
          <el-radio-group v-model="annotationMode" size="large">
            <el-radio-button value="whole">整体标注</el-radio-button>
            <el-radio-button value="character">逐字标注</el-radio-button>
          </el-radio-group>
          <div class="mode-description" v-if="annotationMode === 'whole'">
            为整个文本输入一个整体的读音
          </div>
          <div class="mode-description" v-else>
            为每个字符单独输入读音
          </div>
        </div>

        <!-- 整体标注模式 -->
        <div v-if="annotationMode === 'whole'" class="whole-annotation-mode">
          <el-form label-position="top">
            <el-form-item label="整体假名标注">
              <el-input v-model="editingReading" placeholder="请输入整体假名标注"></el-input>
            </el-form-item>
          </el-form>
        </div>

        <!-- 逐字标注模式 -->
        <div v-else class="character-annotation-mode">
          <div class="character-by-character">
            <!-- 拆分字符，为每个字符创建一个输入框 -->
            <div v-for="(char, charIndex) in characters[editingIndex].char.split('')" :key="charIndex"
              class="character-input-item">
              <div class="character-label">{{ char }}</div>
              <el-input v-model="characterReadings[charIndex]" placeholder="假名"
                @input="updateCharacterReadings"></el-input>
            </div>
          </div>
        </div>
      </div>
    </standard-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import StandardDialog from './StandardDialog.vue';

const props = defineProps({
  characters: {
    type: Array,
    required: true,
    default: () => []
  },
  editable: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:characters']);

// 编辑状态
const editDialogVisible = ref(false);
const editingIndex = ref(null);
const editingReading = ref('');
const annotationMode = ref('whole'); // 'whole' 或 'character'
const characterReadings = ref({}); // 逐字标注模式下的字符读音

// 判断字符是否需要显示假名
function needsFurigana(char) {
  // 如果有读音且读音与字符不同，则显示假名
  return char.reading && char.reading !== char.char;
}

// 获取字符的CSS类
function getCharClass(char) {
  // 根据字符类型返回不同的CSS类
  if (isKanji(char.char)) {
    return 'kanji';
  } else if (isKana(char.char)) {
    return 'kana';
  } else {
    return 'other';
  }
}

// 判断是否为汉字
function isKanji(char) {
  // 日语汉字Unicode范围
  return /[\u4E00-\u9FAF]/.test(char);
}

// 判断是否为假名
function isKana(char) {
  // 平假名和片假名的Unicode范围
  return /[\u3040-\u309F\u30A0-\u30FF]/.test(char);
}

// 编辑字符
function editCharacter(index) {
  if (!props.editable) return;

  // 编辑模式
  editingIndex.value = index;
  editingReading.value = props.characters[index].reading || '';

  // 初始化逐字标注数据
  initCharacterReadings(index);

  // 显示对话框
  editDialogVisible.value = true;
}

// 初始化逐字标注数据
function initCharacterReadings(index) {
  const char = props.characters[index].char;
  const reading = props.characters[index].reading || '';

  // 清空之前的数据
  characterReadings.value = {};

  // 如果有现有的读音，尝试分配给每个字符
  if (reading) {
    // 简单情况：读音长度与字符长度相同，一一对应
    if (reading.length === char.length) {
      for (let i = 0; i < char.length; i++) {
        characterReadings.value[i] = reading[i];
      }
    } else {
      // 复杂情况：读音长度与字符长度不同，全部分配给第一个字符
      characterReadings.value[0] = reading;
    }
  }
}

// 更新字符读音
function updateCharacterReadings() {
  // 将所有字符读音合并为一个字符串
  const combinedReading = Object.values(characterReadings.value).join('');
  editingReading.value = combinedReading;
}

// 取消编辑
function cancelEdit() {
  editDialogVisible.value = false;
  editingIndex.value = null;
  editingReading.value = '';
  characterReadings.value = {};
}

// 保存编辑
function saveEdit() {
  if (editingIndex.value === null) return;

  // 创建新的字符数组，避免直接修改props
  const updatedCharacters = [...props.characters];

  // 根据标注模式更新字符标注
  if (annotationMode.value === 'whole') {
    // 整体标注模式：直接使用整体读音
    updatedCharacters[editingIndex.value] = {
      ...updatedCharacters[editingIndex.value],
      reading: editingReading.value,
      isEdited: true
    };
  } else {
    // 逐字标注模式：为每个字符创建单独的对象
    // 确保更新了最新的合并读音
    updateCharacterReadings();

    // 获取当前字符和它的拆分字符
    const currentChar = updatedCharacters[editingIndex.value];
    const charArray = currentChar.char.split('');

    // 创建新的字符数组，每个字符作为单独的对象
    const newCharacters = [];

    // 处理每个拆分的字符
    for (let i = 0; i < charArray.length; i++) {
      const char = charArray[i];
      const reading = characterReadings.value[i] || char;

      // 添加到新的字符数组
      newCharacters.push({
        char: char,
        reading: reading,
        isEdited: true
      });
    }

    // 替换原来的字符对象
    updatedCharacters.splice(editingIndex.value, 1, ...newCharacters);
  }

  // 发送更新事件
  emit('update:characters', updatedCharacters);

  // 关闭对话框
  editDialogVisible.value = false;
  editingIndex.value = null;
  editingReading.value = '';
  characterReadings.value = {};
}
</script>

<style scoped>
.character-annotation-wrapper {
  display: inline-block;
  width: 100%;
}

.annotated-word {
  display: inline-flex;
  flex-wrap: wrap;
  gap: 0.5em;
  margin-bottom: 0.5em;
  padding: 1em 0.5em 0.5em;
  border-radius: 0.25rem;
  background-color: #f8f9fa;
  border: 1px solid #ebeef5;
  transition: all 0.3s;
  line-height: 1.5;
  font-size: 1em;
}

.annotated-word:hover {
  background-color: #ecf5ff;
  border-color: #c6e2ff;
}

.annotated-char {
  display: inline-block;
  text-align: center;
  cursor: pointer;
  margin: 0 0.1em;
  padding: 0.1em 0.2em;
  transition: all 0.2s;
  border-radius: 0.25rem;
}

.annotated-char:hover {
  background-color: rgba(64, 158, 255, 0.1);
}



.furigana-container {
  display: inline-block;
  position: relative;
  text-align: center;
  vertical-align: bottom;
  min-width: 1em;
  padding-top: 0.5em;
}

.base-char {
  display: inline-block;
  position: relative;
}

.furigana {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.6em;
  line-height: 1;
  text-align: center;
  color: #409EFF;
  font-weight: normal;
  white-space: nowrap;
}

.character-preview {
  text-align: center;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.character-large {
  font-size: 2.5rem;
  line-height: 1;
  margin-bottom: 0.5rem;
  color: #303133;
}

.edit-dialog-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin: 0 auto;
}



/* 多字标注对话框样式 */
.multi-edit-dialog-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.selected-text-preview {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 0.25rem;
  border: 1px solid #ebeef5;
}

.selected-text-label {
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: #606266;
}

.selected-text {
  font-size: 1.5rem;
  line-height: 1.5;
  word-break: break-all;
}

.annotation-mode-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.mode-label {
  font-weight: bold;
  color: #606266;
}

.mode-description {
  font-size: 0.875rem;
  color: #909399;
  margin-top: 0.5rem;
}

.whole-annotation-mode,
.character-annotation-mode {
  margin-top: 1rem;
}

.character-by-character {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.character-input-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
}

.character-label {
  font-size: 1.5rem;
  min-width: 3rem;
  text-align: center;
  padding: 0.5rem;
  background-color: #fff;
  border-radius: 0.25rem;
  border: 1px solid #dcdfe6;
}
</style>
