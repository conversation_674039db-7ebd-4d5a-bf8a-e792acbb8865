# H.264 AVC Levels 与分辨率对应关系

本文档说明了Echo Lab前端视频导出功能中H.264编码级别与支持分辨率的对应关系。

## 🎯 问题背景

WebCodecs API在进行H.264视频编码时，需要确保指定的分辨率与AVC Level相匹配。如果分辨率超出了指定Level的最大编码区域，会导致编码失败。

### 错误示例
```
NotSupportedError: The provided resolution (1280x720) has a coded area (1280*720=921600) 
which exceeds the maximum coded area (414720) supported by the AVC level (3.0) 
indicated by the codec string (0x1E).
```

## 📊 AVC Level 与分辨率对应表

| AVC Level | Codec String | 最大编码区域 | 推荐分辨率 | 最大分辨率 | 用途 |
|-----------|--------------|-------------|------------|------------|------|
| 3.0 | `avc1.42E01E` | 414,720 | 640×480 | 720×576 | 标清视频 |
| 3.1 | `avc1.42E01F` | 921,600 | 1280×720 | 1280×720 | 高清视频 |
| 4.0 | `avc1.42E028` | 2,073,600 | 1920×1080 | 2048×1024 | 全高清视频 |
| 5.0 | `avc1.42E032` | 8,294,400 | 2560×1440 | 4096×2048 | 超高清视频 |

## 🔧 Echo Lab 实现方案

### 质量预设配置

```javascript
const qualityPresets = {
  low: { 
    width: 640, 
    height: 480,
    codec: 'avc1.42E01E',  // Level 3.0
    bitrate: 1000000       // 1Mbps
  },
  medium: { 
    width: 1280, 
    height: 720,
    codec: 'avc1.42E01F',  // Level 3.1
    bitrate: 2500000       // 2.5Mbps
  },
  high: { 
    width: 1920, 
    height: 1080,
    codec: 'avc1.42E028',  // Level 4.0
    bitrate: 5000000       // 5Mbps
  }
};
```

### 动态编解码器选择

```javascript
function selectCodecString(width, height) {
  if (width <= 640 && height <= 480) {
    return 'avc1.42E01E'; // Level 3.0
  } else if (width <= 1280 && height <= 720) {
    return 'avc1.42E01F'; // Level 3.1
  } else if (width <= 1920 && height <= 1080) {
    return 'avc1.42E028'; // Level 4.0
  } else {
    return 'avc1.42E032'; // Level 5.0
  }
}
```

## 🎨 用户界面更新

### 质量选项显示

在导出对话框中，每个质量选项现在显示详细的技术参数：

```
低质量 (480p)
640×480 · H.264 Level 3.0 · 1Mbps

中等质量 (720p)  
1280×720 · H.264 Level 3.1 · 2.5Mbps

高质量 (1080p)
1920×1080 · H.264 Level 4.0 · 5Mbps
```

### 样式实现

```css
.quality-option {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.quality-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 0.125rem;
}

.quality-desc {
  font-size: 0.75rem;
  color: #666;
  line-height: 1.2;
}
```

## 🧪 测试验证

### 兼容性测试

```javascript
// 测试不同Level的编码器支持
const testConfigs = [
  { codec: 'avc1.42E01E', width: 640, height: 480 },   // Level 3.0
  { codec: 'avc1.42E01F', width: 1280, height: 720 }, // Level 3.1
  { codec: 'avc1.42E028', width: 1920, height: 1080 } // Level 4.0
];

for (const config of testConfigs) {
  const support = await VideoEncoder.isConfigSupported(config);
  console.log(`${config.codec} (${config.width}x${config.height}):`, 
              support.supported ? '✅' : '❌');
}
```

### 快速测试

```javascript
// 运行快速兼容性测试
webCodecsQuickTest.runCompatibilityCheck();

// 运行完整导出测试（使用480p以确保兼容性）
webCodecsQuickTest.runQuickTest();
```

## 📈 性能对比

### 编码性能

| 质量 | 分辨率 | 编码时间* | 文件大小* | 内存占用* |
|------|--------|-----------|-----------|-----------|
| 低质量 | 640×480 | ~10秒 | ~2MB | ~100MB |
| 中等质量 | 1280×720 | ~25秒 | ~8MB | ~300MB |
| 高质量 | 1920×1080 | ~60秒 | ~20MB | ~800MB |

*基于30秒视频的估算值

### 兼容性评分

| Level | 硬件支持 | 软件支持 | 推荐度 |
|-------|----------|----------|--------|
| 3.0 | 🟢 优秀 | 🟢 优秀 | ⭐⭐⭐⭐⭐ |
| 3.1 | 🟢 优秀 | 🟢 优秀 | ⭐⭐⭐⭐⭐ |
| 4.0 | 🟡 良好 | 🟢 优秀 | ⭐⭐⭐⭐ |
| 5.0 | 🟡 良好 | 🟡 良好 | ⭐⭐⭐ |

## 🔍 故障排除

### 常见问题

1. **编码区域超限**
   ```
   错误: coded area exceeds the maximum coded area supported
   解决: 降低分辨率或提高AVC Level
   ```

2. **编解码器不支持**
   ```
   错误: VideoEncoder configuration not supported
   解决: 检查硬件加速设置，尝试软件编码
   ```

3. **内存不足**
   ```
   症状: 浏览器卡顿或崩溃
   解决: 使用低质量设置，减少视频时长
   ```

### 调试命令

```javascript
// 检查特定配置的支持情况
VideoEncoder.isConfigSupported({
  codec: 'avc1.42E01F',
  width: 1280,
  height: 720,
  bitrate: 2500000,
  framerate: 30
}).then(result => console.log('支持情况:', result));

// 运行完整的编解码器测试
muxerConfigTest.runFullTest();
```

## 📚 参考资料

- [H.264 Level Specification](https://en.wikipedia.org/wiki/Advanced_Video_Coding#Levels)
- [WebCodecs API Documentation](https://developer.mozilla.org/en-US/docs/Web/API/WebCodecs_API)
- [MP4 Container Format](https://developer.mozilla.org/en-US/docs/Web/Media/Formats/Containers#MP4)

## 🎯 最佳实践

1. **默认使用Level 3.1**：支持720p，兼容性最佳
2. **提供多种选择**：让用户根据需求选择质量
3. **显示技术参数**：帮助用户理解不同选项的差异
4. **渐进式降级**：不支持高级别时自动降级
5. **充分测试**：在不同设备上验证编码性能
