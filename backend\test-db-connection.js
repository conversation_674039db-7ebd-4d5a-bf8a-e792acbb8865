/**
 * 测试数据库连接
 */
const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.development' });

async function testConnection() {
  console.log('测试数据库连接...');
  console.log('配置信息:');
  console.log('Host:', process.env.DB_HOST);
  console.log('Port:', process.env.DB_PORT);
  console.log('Username:', process.env.DB_USERNAME);
  console.log('Database:', process.env.DB_NAME);
  console.log('Password:', process.env.DB_PASSWORD ? '***' : 'empty');

  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || '127.0.0.1',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USERNAME || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'echolab'
    });

    console.log('✅ 数据库连接成功!');
    
    // 测试查询
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('✅ 测试查询成功:', rows);
    
    // 查看数据库
    const [databases] = await connection.execute('SHOW DATABASES');
    console.log('✅ 可用数据库:', databases.map(db => db.Database));
    
    await connection.end();
    console.log('✅ 连接已关闭');
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    console.error('错误代码:', error.code);
    console.error('错误详情:', error);
  }
}

testConnection();
