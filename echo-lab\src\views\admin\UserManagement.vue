<!--
  用户管理页面
  用于管理员查看和管理用户
-->
<template>
  <div class="user-management">
    <!-- 搜索和筛选 -->
    <div class="filter-container">
      <el-input v-model="searchQuery" placeholder="搜索用户名或邮箱" class="search-input" clearable @clear="handleSearch">
        <template #prefix>
          <el-icon>
            <i-ep-search />
          </el-icon>
        </template>
        <template #append>
          <el-button @click="handleSearch">搜索</el-button>
        </template>
      </el-input>

      <div class="filter-options">
        <el-select v-model="statusFilter" placeholder="状态" clearable @change="handleSearch">
          <el-option label="全部" value="" />
          <el-option label="活跃" value="active" />
          <el-option label="未激活" value="inactive" />
          <el-option label="已禁用" value="banned" />
        </el-select>

        <el-select v-model="roleFilter" placeholder="角色" clearable @change="handleSearch">
          <el-option label="全部" value="" />
          <el-option label="用户" value="user" />
          <el-option label="管理员" value="admin" />
        </el-select>
      </div>
    </div>

    <!-- 用户列表 -->
    <el-card class="user-list-card">
      <template #header>
        <div class="card-header">
          <span>用户列表</span>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="refreshUsers">
              <el-icon>
                <i-ep-refresh />
              </el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="users" style="width: 100%" v-loading="loading" @row-click="handleRowClick" row-key="id">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名">
          <template #default="scope">
            {{ scope.row.username || '未设置' }}
          </template>
        </el-table-column>
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="role" label="角色" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.role === 'admin' ? 'danger' : 'info'" size="small">
              {{ scope.row.role === 'admin' ? '管理员' : '用户' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="注册时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button size="small" @click.stop="viewUser(scope.row)">
              查看
            </el-button>
            <el-dropdown trigger="click" @command="(command) => handleCommand(command, scope.row)" @click.stop>
              <el-button size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="scope.row.status === 'active' ? 'disable' : 'enable'"
                    :disabled="scope.row.id === currentUserId">
                    {{ scope.row.status === 'active' ? '禁用账户' : '启用账户' }}
                  </el-dropdown-item>
                  <el-dropdown-item :command="scope.row.role === 'admin' ? 'removeAdmin' : 'makeAdmin'"
                    :disabled="scope.row.id === currentUserId">
                    {{ scope.row.role === 'admin' ? '移除管理员' : '设为管理员' }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import { ElMessage, ElMessageBox } from 'element-plus';
import httpClient from '@/utils/httpClient';
import { API_ENDPOINTS } from '@/config/api';
import { useUserStore } from '@/stores/userStore';

const router = useRouter();
const userStore = useUserStore();

// 当前用户ID
const currentUserId = computed(() => userStore.user?.id);

// 加载状态
const loading = ref(false);

// 用户列表数据
const users = ref([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(20);

// 搜索和筛选
const searchQuery = ref('');
const statusFilter = ref('');
const roleFilter = ref('');

// 获取用户列表
async function fetchUsers() {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      status: statusFilter.value,
      role: roleFilter.value,
      sortBy: 'created_at', // 使用下划线命名法
      sortOrder: 'DESC'
    };

    const response = await httpClient.get(API_ENDPOINTS.ADMIN.USERS, params);
    if (response.success) {
      users.value = response.data.users;
      total.value = response.data.total;
    } else {
      ElMessage.error(response.error || '获取用户列表失败');
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    ElMessage.error('获取用户列表失败: ' + error.message);
  } finally {
    loading.value = false;
  }
}

// 刷新用户列表
function refreshUsers() {
  fetchUsers();
}

// 处理搜索
function handleSearch() {
  currentPage.value = 1;
  fetchUsers();
}

// 处理页码变化
function handleCurrentChange(page) {
  currentPage.value = page;
  fetchUsers();
}

// 处理每页数量变化
function handleSizeChange(size) {
  pageSize.value = size;
  currentPage.value = 1;
  fetchUsers();
}

// 查看用户详情
function viewUser(user) {
  router.push(`/admin/users/${user.id}`);
}

// 处理行点击
function handleRowClick(row) {
  viewUser(row);
}

// 处理下拉菜单命令
async function handleCommand(command, user) {
  switch (command) {
    case 'enable':
      await updateUserStatus(user.id, 'active');
      break;
    case 'disable':
      await updateUserStatus(user.id, 'banned');
      break;
    case 'makeAdmin':
      await updateUserRole(user.id, 'admin');
      break;
    case 'removeAdmin':
      await updateUserRole(user.id, 'user');
      break;
  }
}

// 更新用户状态
async function updateUserStatus(userId, status) {
  try {
    const confirmMessage = status === 'active'
      ? '确定要启用该用户账户吗？'
      : '确定要禁用该用户账户吗？禁用后该用户将无法登录系统。';

    await ElMessageBox.confirm(confirmMessage, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const response = await httpClient.put(`${API_ENDPOINTS.ADMIN.USERS}/${userId}/status`, { status });

    if (response.success) {
      ElMessage.success(response.message || '用户状态更新成功');
      // 更新本地用户列表
      const index = users.value.findIndex(u => u.id === userId);
      if (index !== -1) {
        users.value[index].status = status;
      }
    } else {
      ElMessage.error(response.error || '用户状态更新失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新用户状态失败:', error);
      ElMessage.error('更新用户状态失败: ' + error.message);
    }
  }
}

// 更新用户角色
async function updateUserRole(userId, role) {
  try {
    const confirmMessage = role === 'admin'
      ? '确定要将该用户设为管理员吗？管理员将拥有系统的管理权限。'
      : '确定要移除该用户的管理员权限吗？';

    await ElMessageBox.confirm(confirmMessage, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const response = await httpClient.put(`${API_ENDPOINTS.ADMIN.USERS}/${userId}/role`, { role });

    if (response.success) {
      ElMessage.success(response.message || '用户角色更新成功');
      // 更新本地用户列表
      const index = users.value.findIndex(u => u.id === userId);
      if (index !== -1) {
        users.value[index].role = role;
      }
    } else {
      ElMessage.error(response.error || '用户角色更新失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新用户角色失败:', error);
      ElMessage.error('更新用户角色失败: ' + error.message);
    }
  }
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// 获取用户状态类型
function getStatusType(status) {
  switch (status) {
    case 'active': return 'success';
    case 'inactive': return 'warning';
    case 'banned': return 'danger';
    default: return 'info';
  }
}

// 获取用户状态文本
function getStatusText(status) {
  switch (status) {
    case 'active': return '活跃';
    case 'inactive': return '未激活';
    case 'banned': return '已禁用';
    default: return '未知';
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchUsers();
});
</script>

<style scoped>
.user-management {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.filter-container {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-input {
  width: 20rem;
}

.filter-options {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.pagination-container {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
}

/* 移动端样式通过条件类名处理 */
</style>
