/**
 * 签到状态存储
 * 管理用户签到记录和统计
 */
import { defineStore } from 'pinia';
import api from '@/services/api';

export const useCheckInStore = defineStore('checkIn', {
  state: () => ({
    checkInRecords: [], // 签到记录
    consecutiveDays: 0, // 连续签到天数
    totalDays: 0, // 累计签到天数
    monthlyCount: 0, // 本月签到天数
    maxConsecutiveDays: 0, // 最长连续签到天数
    todayChecked: false, // 今日是否已签到
    loading: false,
    error: null,
    // 预留奖励字段
    rewards: []
  }),

  getters: {
    // 获取指定日期的签到状态
    isCheckedIn: (state) => (date) => {
      const dateStr = typeof date === 'string' ? date : date.toISOString().split('T')[0];
      return state.checkInRecords.some(record => record.date === dateStr);
    },

    // 获取签到统计
    stats: (state) => ({
      consecutive: state.consecutiveDays,
      total: state.totalDays,
      monthly: state.monthlyCount,
      maxConsecutive: state.maxConsecutiveDays
    })
  },

  actions: {
    /**
     * 初始化签到数据
     */
    async initCheckIn() {
      this.loading = true;
      try {
        const response = await api.get('/api/checkin/stats');
        if (response.success) {
          const { totalDays, monthlyCount, consecutiveDays, maxConsecutiveDays, todayChecked } = response.data;
          this.totalDays = totalDays;
          this.monthlyCount = monthlyCount;
          this.consecutiveDays = consecutiveDays;
          this.maxConsecutiveDays = maxConsecutiveDays;
          this.todayChecked = todayChecked;
        }
      } catch (error) {
        this.error = '获取签到数据失败';
        console.error('获取签到数据失败:', error);
      } finally {
        this.loading = false;
      }
    },

    /**
     * 执行签到
     */
    async checkIn() {
      if (this.todayChecked) {
        return { success: false, message: '今日已签到' };
      }

      this.loading = true;
      try {
        const response = await api.post('/api/checkin');
        if (response.success) {
          this.todayChecked = true;
          this.consecutiveDays = response.data.consecutiveDays;
          
          // 立即添加今日签到记录到日历数据
          const today = new Date().toISOString().split('T')[0];
          if (!this.checkInRecords.some(record => record.date === today)) {
            this.checkInRecords.push({ date: today });
          }
          
          // 重新获取统计数据以确保准确性
          await this.initCheckIn();
          
          return { 
            success: true, 
            message: response.message,
            consecutiveDays: response.data.consecutiveDays
          };
        } else {
          return { success: false, message: response.message };
        }
      } catch (error) {
        this.error = '签到失败';
        return { success: false, message: '签到失败，请重试' };
      } finally {
        this.loading = false;
      }
    },

    /**
     * 获取签到日历数据
     */
    async getCalendarData(year, month) {
      try {
        const response = await api.get(`/api/checkin/calendar?year=${year}&month=${month}`);
        if (response.success) {
          this.checkInRecords = response.data;
          return response.data;
        }
        return [];
      } catch (error) {
        console.error('获取签到日历失败:', error);
        return [];
      }
    }
  }
});