import { defineStore } from "pinia";
import publicContentService from "@/services/publicContentService";

/**
 * 公开内容存储
 * 管理公开内容的状态
 */
export const usePublicContentStore = defineStore("publicContent", {
  state: () => ({
    loading: false,
    error: null,
    contents: [],
    hotContents: [],
    latestContents: [],
    availableTags: [], // 可用标签列表
    pagination: {
      total: 0,
      page: 1,
      pageSize: 10, // 标准分页大小
    },
    filters: {
      tags: [], // 等级过滤（多选）
    },
  }),

  getters: {
    /**
     * 获取过滤后的内容
     */
    filteredContents(state) {
      let result = [...state.contents];

      // 搜索过滤
      if (state.filters.search) {
        const searchQuery = state.filters.search.toLowerCase();
        result = result.filter(
          (item) =>
            item.name.toLowerCase().includes(searchQuery) ||
            (item.description &&
              item.description.toLowerCase().includes(searchQuery))
        );
      }

      // 标签过滤
      if (state.filters.tag) {
        result = result.filter((item) => {
          if (!item.tags) return false;
          const tags = Array.isArray(item.tags)
            ? item.tags
            : item.tags.split(",").map((tag) => tag.trim());
          return tags.includes(state.filters.tag);
        });
      }

      // 排序
      result.sort((a, b) => {
        const key = state.filters.sortBy;
        const orderFactor = state.filters.sortOrder === "desc" ? -1 : 1;
        return orderFactor * (a[key] < b[key] ? -1 : 1);
      });

      // 更新总数
      this.pagination.total = result.length;

      return result;
    },
  },

  actions: {
    /**
     * 获取公开内容列表
     */
    async fetchPublicContents(resetPage = true) {
      this.loading = true;
      try {
        if (resetPage) {
          this.pagination.page = 1;
        }

        const params = {
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
          sortBy: "updatedAt",
          sortOrder: "DESC",
        };

        // 添加过滤参数
        if (this.filters.tags && this.filters.tags.length > 0) {
          params.tags = this.filters.tags.join(',');
        }

        const response = await publicContentService.getPublicContents(params);

        if (response && response.success) {
          if (resetPage) {
            this.contents = response.contents || [];
          } else {
            this.contents = [...this.contents, ...(response.contents || [])];
          }
          
          if (response.pagination) {
            this.pagination = {
              ...this.pagination,
              ...response.pagination,
            };
          }
          
          // 更新可用标签
          if (response.availableTags) {
            this.availableTags = response.availableTags;
          }
        } else {
          throw new Error(response?.error || "获取公开内容列表失败");
        }
      } catch (err) {
        console.error("获取公开内容列表失败:", err);
        this.error = err.message;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 获取热门内容
     */
    async fetchHotContents(limit = 6) {
      try {
        const response = await publicContentService.getHotContents(limit);
        if (response && response.success) {
          this.hotContents = response.contents || [];
        } else {
          throw new Error(response?.error || "获取热门内容失败");
        }
      } catch (err) {
        console.error("获取热门内容失败:", err);
      }
    },

    /**
     * 获取最新内容
     */
    async fetchLatestContents(limit = 6) {
      try {
        const response = await publicContentService.getLatestContents(limit);
        if (response && response.success) {
          this.latestContents = response.contents || [];
        } else {
          throw new Error(response?.error || "获取最新内容失败");
        }
      } catch (err) {
        console.error("获取最新内容失败:", err);
      }
    },

    /**
     * 更新过滤条件
     */
    updateFilters(filters) {
      this.filters = { ...this.filters, ...filters };
      this.fetchPublicContents(true); // 重新加载内容
    },

    /**
     * 清除过滤条件
     */
    clearFilters() {
      this.filters = {
        tags: [],
      };
      this.fetchPublicContents(true);
    },

    /**
     * 加载更多内容
     */
    async loadMoreContents() {
      if (this.loading || this.contents.length >= this.pagination.total) {
        return;
      }

      this.pagination.page += 1;
      await this.fetchPublicContents(false); // 不重置页面，追加内容
    },

    /**
     * 更新分页
     */
    updatePagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination };
      this.fetchPublicContents();
    },

    /**
     * 搜索内容
     */
    async searchContents(keyword) {
      this.loading = true;
      try {
        const response = await publicContentService.searchContents(keyword);
        if (response && response.success) {
          this.contents = response.contents || [];
          if (response.pagination) {
            this.pagination = {
              ...this.pagination,
              ...response.pagination,
            };
          }
        } else {
          throw new Error(response?.error || "搜索内容失败");
        }
      } catch (err) {
        console.error("搜索内容失败:", err);
        this.error = err.message;
      } finally {
        this.loading = false;
      }
    },
  },
});
