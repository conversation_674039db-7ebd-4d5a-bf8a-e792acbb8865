<!--
  未授权页面
  当用户尝试访问没有权限的页面时显示
-->
<template>
  <div class="unauthorized-page">
    <div class="unauthorized-container">
      <el-result icon="error" title="403" sub-title="抱歉，您没有权限访问此页面">
        <div class="feature-info" v-if="feature">
          <p>您需要 <strong>{{ getFeatureName(feature) }}</strong> 权限才能访问此功能。</p>
          <p>目前该功能仅对部分用户开放，请联系管理员申请权限。</p>
        </div>
        <template #extra>
          <div class="action-buttons">
            <el-button type="primary" @click="goHome">返回首页</el-button>
            <el-button @click="goToProfile">查看个人信息</el-button>
          </div>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import { ref, computed } from 'vue';

const router = useRouter();
const route = useRoute();

// 获取路由参数中的功能标识
const feature = computed(() => route.params.feature);

// 功能名称映射
const featureNames = {
  'content_creation': '内容创建与管理',
  'video_export': '视频导出',
  // 可以添加更多功能名称映射
};

// 获取功能名称
function getFeatureName(featureKey) {
  return featureNames[featureKey] || featureKey;
}

// 返回首页
function goHome() {
  router.push('/');
}

// 前往个人信息页面
function goToProfile() {
  router.push('/profile');
}
</script>

<style scoped>
.unauthorized-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.unauthorized-container {
  max-width: 40rem;
  width: 100%;
  padding: 2rem;
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1);
}

.feature-info {
  margin: 1.5rem 0;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
  border-left: 0.25rem solid #e6a23c;
  color: #606266;
}

.feature-info p {
  margin: 0.5rem 0;
  line-height: 1.5;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
}
</style>
