"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("favorites", {
      id: {
        type: Sequelize.STRING(21),
        allowNull: false,
        primaryKey: true,
      },
      user_id: {
        type: Sequelize.STRING(21),
        allowNull: false,
        references: {
          model: "users",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      content_id: {
        type: Sequelize.STRING(21),
        allowNull: false,
        references: {
          model: "contents",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
    });

    // 添加唯一索引，确保用户不能重复收藏同一内容
    await queryInterface.addIndex("favorites", ["user_id", "content_id"], {
      unique: true,
      name: "favorites_user_content_unique",
    });

    // 添加索引，提高查询性能
    await queryInterface.addIndex("favorites", ["user_id"], {
      name: "favorites_user_id_index",
    });
    await queryInterface.addIndex("favorites", ["content_id"], {
      name: "favorites_content_id_index",
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("favorites");
  },
};
