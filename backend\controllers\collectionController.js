/**
 * 合集控制器
 * 处理合集相关的HTTP请求
 */
const collectionService = require("../services/collectionService");

/**
 * 获取用户的合集列表
 * GET /api/collections
 */
async function getUserCollections(req, res) {
  try {
    const userId = req.user.id;
    const options = {
      page: req.query.page,
      limit: req.query.limit,
      search: req.query.search,
      status: req.query.status,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder,
    };

    const result = await collectionService.getUserCollections(userId, options);

    res.json({
      success: true,
      ...result,
    });
  } catch (error) {
    console.error("获取用户合集列表失败:", error);
    res.status(500).json({
      success: false,
      error: `获取合集列表失败: ${error.message}`,
    });
  }
}

/**
 * 获取公开合集列表
 * GET /api/collections/public
 */
async function getPublicCollections(req, res) {
  try {
    const options = {
      page: req.query.page,
      limit: req.query.limit,
      search: req.query.search,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder,
    };

    const result = await collectionService.getPublicCollections(options);

    res.json({
      success: true,
      ...result,
    });
  } catch (error) {
    console.error("获取公开合集列表失败:", error);
    res.status(500).json({
      success: false,
      error: `获取公开合集列表失败: ${error.message}`,
    });
  }
}

/**
 * 根据ID获取合集详情
 * GET /api/collections/:id
 */
async function getCollectionById(req, res) {
  try {
    const { id } = req.params;
    const userId = req.user ? req.user.id : null;

    const collection = await collectionService.getCollectionById(id, userId);

    // 增加浏览次数
    await collectionService.incrementViewCount(id);

    res.json({
      success: true,
      collection,
    });
  } catch (error) {
    console.error("获取合集详情失败:", error);
    const statusCode = error.message.includes("不存在") ? 404 : 
                      error.message.includes("无权访问") ? 403 : 500;
    res.status(statusCode).json({
      success: false,
      error: error.message,
    });
  }
}

/**
 * 创建合集
 * POST /api/collections
 */
async function createCollection(req, res) {
  try {
    const userId = req.user.id;
    const collectionData = req.body;

    const collection = await collectionService.createCollection(collectionData, userId);

    res.status(201).json({
      success: true,
      collection,
    });
  } catch (error) {
    console.error("创建合集失败:", error);
    res.status(400).json({
      success: false,
      error: `创建合集失败: ${error.message}`,
    });
  }
}

/**
 * 更新合集
 * PUT /api/collections/:id
 */
async function updateCollection(req, res) {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const updateData = req.body;

    const collection = await collectionService.updateCollection(id, updateData, userId);

    res.json({
      success: true,
      collection,
    });
  } catch (error) {
    console.error("更新合集失败:", error);
    const statusCode = error.message.includes("不存在") ? 404 : 
                      error.message.includes("无权") ? 403 : 500;
    res.status(statusCode).json({
      success: false,
      error: error.message,
    });
  }
}

/**
 * 删除合集
 * DELETE /api/collections/:id
 */
async function deleteCollection(req, res) {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    await collectionService.deleteCollection(id, userId);

    res.json({
      success: true,
      message: "合集删除成功",
    });
  } catch (error) {
    console.error("删除合集失败:", error);
    const statusCode = error.message.includes("不存在") ? 404 : 
                      error.message.includes("无权") ? 403 : 500;
    res.status(statusCode).json({
      success: false,
      error: error.message,
    });
  }
}

/**
 * 添加内容到合集
 * POST /api/collections/:id/items
 */
async function addContentToCollection(req, res) {
  try {
    const { id: collectionId } = req.params;
    const { contentIds } = req.body;
    const userId = req.user.id;

    if (!contentIds || !Array.isArray(contentIds) || contentIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: "缺少必要参数: contentIds (数组)",
      });
    }

    const results = await collectionService.addMultipleContentsToCollection(
      collectionId,
      contentIds,
      userId
    );

    res.status(201).json({
      success: true,
      results,
    });
  } catch (error) {
    console.error("添加内容到合集失败:", error);
    const statusCode = error.message.includes("不存在") ? 404 : 
                      error.message.includes("无权") ? 403 : 500;
    res.status(statusCode).json({
      success: false,
      error: error.message,
    });
  }
}

/**
 * 从合集中移除内容
 * DELETE /api/collections/:id/items/:contentId
 */
async function removeContentFromCollection(req, res) {
  try {
    const { id: collectionId, contentId } = req.params;
    const userId = req.user.id;

    await collectionService.removeContentFromCollection(collectionId, contentId, userId);

    res.json({
      success: true,
      message: "内容移除成功",
    });
  } catch (error) {
    console.error("从合集中移除内容失败:", error);
    const statusCode = error.message.includes("不存在") ? 404 : 
                      error.message.includes("无权") ? 403 : 500;
    res.status(statusCode).json({
      success: false,
      error: error.message,
    });
  }
}

/**
 * 更新合集内容排序
 * PUT /api/collections/:id/items/order
 */
async function updateCollectionItemOrder(req, res) {
  try {
    const { id: collectionId } = req.params;
    const { itemOrders } = req.body;
    const userId = req.user.id;

    if (!itemOrders || !Array.isArray(itemOrders)) {
      return res.status(400).json({
        success: false,
        error: "缺少必要参数: itemOrders",
      });
    }

    await collectionService.updateCollectionItemOrder(collectionId, itemOrders, userId);

    res.json({
      success: true,
      message: "排序更新成功",
    });
  } catch (error) {
    console.error("更新合集内容排序失败:", error);
    const statusCode = error.message.includes("不存在") ? 404 : 
                      error.message.includes("无权") ? 403 : 500;
    res.status(statusCode).json({
      success: false,
      error: error.message,
    });
  }
}

/**
 * 收藏/取消收藏合集
 * POST /api/collections/:id/favorite
 */
async function toggleCollectionFavorite(req, res) {
  try {
    const { id: collectionId } = req.params;
    const userId = req.user.id;

    const result = await collectionService.toggleCollectionFavorite(collectionId, userId);

    res.json({
      success: true,
      ...result,
    });
  } catch (error) {
    console.error("收藏/取消收藏合集失败:", error);
    const statusCode = error.message.includes("不存在") ? 404 :
                      error.message.includes("无法收藏") ? 403 : 500;
    res.status(statusCode).json({
      success: false,
      error: error.message,
    });
  }
}



/**
 * 发布合集
 * POST /api/collections/:id/publish
 */
async function publishCollection(req, res) {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const collection = await collectionService.updateCollection(id, { status: "published" }, userId);

    res.json({
      success: true,
      collection,
      message: "合集发布成功",
    });
  } catch (error) {
    console.error("发布合集失败:", error);
    const statusCode = error.message.includes("不存在") ? 404 :
                      error.message.includes("无权") ? 403 : 500;
    res.status(statusCode).json({
      success: false,
      error: error.message,
    });
  }
}

/**
 * 下架合集
 * POST /api/collections/:id/unpublish
 */
async function unpublishCollection(req, res) {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const collection = await collectionService.updateCollection(id, { status: "draft" }, userId);

    res.json({
      success: true,
      collection,
      message: "合集下架成功",
    });
  } catch (error) {
    console.error("下架合集失败:", error);
    const statusCode = error.message.includes("不存在") ? 404 :
                      error.message.includes("无权") ? 403 : 500;
    res.status(statusCode).json({
      success: false,
      error: error.message,
    });
  }
}

/**
 * 获取用户收藏的合集列表
 * GET /api/collections/favorites
 */
async function getUserFavoriteCollections(req, res) {
  try {
    const userId = req.user.id;
    const options = {
      page: req.query.page,
      limit: req.query.limit,
      search: req.query.search,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder,
    };

    const result = await collectionService.getUserFavoriteCollections(userId, options);

    res.json({
      success: true,
      ...result,
    });
  } catch (error) {
    console.error("获取用户收藏合集列表失败:", error);
    res.status(500).json({
      success: false,
      error: `获取收藏合集列表失败: ${error.message}`,
    });
  }
}

module.exports = {
  getUserCollections,
  getPublicCollections,
  getCollectionById,
  createCollection,
  updateCollection,
  deleteCollection,
  addContentToCollection,
  removeContentFromCollection,
  updateCollectionItemOrder,
  publishCollection,
  unpublishCollection,
  toggleCollectionFavorite,
  getUserFavoriteCollections,
};
