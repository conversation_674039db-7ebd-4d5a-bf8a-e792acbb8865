<template>
  <div class="error-test-page">
    <div class="page-header">
      <h1>错误统计测试页面</h1>
      <p>用于测试错误收集和上报功能</p>
    </div>

    <div class="test-buttons">
      <el-button type="danger" @click="triggerJavaScriptError">
        触发JavaScript错误
      </el-button>
      
      <el-button type="warning" @click="triggerVueError">
        触发Vue组件错误
      </el-button>
      
      <el-button type="info" @click="triggerNetworkError">
        触发网络错误
      </el-button>
      
      <el-button type="primary" @click="triggerPromiseError">
        触发Promise错误
      </el-button>
      
      <el-button type="success" @click="triggerResourceError">
        触发资源加载错误
      </el-button>
    </div>

    <div class="test-results">
      <h3>测试结果</h3>
      <div class="result-list">
        <div v-for="(result, index) in testResults" :key="index" class="result-item">
          <el-tag :type="result.type">{{ result.message }}</el-tag>
          <span class="result-time">{{ result.time }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { get } from '@/utils/httpClient';
import { reportError } from '@/utils/errorHandler';

const testResults = ref([]);

const addResult = (message, type = 'info') => {
  testResults.value.unshift({
    message,
    type,
    time: new Date().toLocaleTimeString()
  });
};

// 触发JavaScript错误
const triggerJavaScriptError = () => {
  try {
    // 故意访问未定义的变量
    console.log(undefinedVariable.someProperty);
  } catch (error) {
    addResult('JavaScript错误已触发', 'danger');
    // 手动上报错误（正常情况下会自动捕获）
    reportError(error, { test: true, errorType: 'manual_js_error' });
  }
};

// 触发Vue组件错误
const triggerVueError = () => {
  try {
    // 创建一个会导致Vue错误的情况
    const invalidComponent = null;
    invalidComponent.render();
  } catch (error) {
    addResult('Vue组件错误已触发', 'warning');
    reportError(error, { test: true, errorType: 'manual_vue_error' });
  }
};

// 触发网络错误
const triggerNetworkError = async () => {
  try {
    // 请求一个不存在的API端点
    await get('/api/nonexistent-endpoint');
  } catch (error) {
    addResult('网络错误已触发', 'info');
    // 网络错误会被httpClient自动捕获和上报
  }
};

// 触发Promise错误
const triggerPromiseError = () => {
  // 创建一个被拒绝的Promise
  Promise.reject(new Error('测试Promise错误'))
    .catch(error => {
      addResult('Promise错误已触发', 'primary');
      // Promise错误会被全局错误处理器自动捕获
    });
};

// 触发资源加载错误
const triggerResourceError = () => {
  // 创建一个不存在的图片元素
  const img = new Image();
  img.onerror = () => {
    addResult('资源加载错误已触发', 'success');
  };
  img.src = '/nonexistent-image.jpg';
};

// 页面加载时添加说明
addResult('错误测试页面已加载，点击按钮测试各种错误类型', 'info');
</script>

<style scoped>
.error-test-page {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-header h1 {
  color: #303133;
  margin-bottom: 0.5rem;
}

.page-header p {
  color: #606266;
  margin: 0;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 3rem;
}

.test-buttons .el-button {
  min-width: 150px;
}

.test-results {
  background: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-results h3 {
  margin: 0 0 1rem 0;
  color: #303133;
}

.result-list {
  max-height: 300px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-time {
  color: #909399;
  font-size: 0.875rem;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .error-test-page {
    padding: 1rem;
  }
  
  .test-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .test-buttons .el-button {
    width: 100%;
    max-width: 300px;
  }
  
  .result-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
</style>
