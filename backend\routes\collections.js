/**
 * 合集API路由
 * 提供合集的CRUD操作接口
 */
const express = require("express");
const router = express.Router();
const collectionController = require("../controllers/collectionController");
const {
  authenticate,
  optionalAuthenticate,
} = require("../middleware/authMiddleware");
const { checkFeaturePermission } = require("../middleware/featurePermissionMiddleware");

/**
 * 获取公开合集列表
 * GET /api/collections/public
 * 无需认证
 */
router.get("/public", collectionController.getPublicCollections);

/**
 * 获取用户的合集列表
 * GET /api/collections
 * 需要认证和内容创建权限
 */
router.get(
  "/",
  authenticate,
  checkFeaturePermission("content_creation"),
  collectionController.getUserCollections
);

/**
 * 获取用户收藏的合集列表
 * GET /api/collections/favorites
 * 需要认证
 */
router.get(
  "/favorites",
  authenticate,
  collectionController.getUserFavoriteCollections
);

/**
 * 根据ID获取合集详情
 * GET /api/collections/:id
 * 可选认证（用于检查收藏状态）
 */
router.get("/:id", optionalAuthenticate, collectionController.getCollectionById);

/**
 * 创建合集
 * POST /api/collections
 * 需要认证和内容创建权限
 */
router.post(
  "/",
  authenticate,
  checkFeaturePermission("content_creation"),
  collectionController.createCollection
);

/**
 * 更新合集
 * PUT /api/collections/:id
 * 需要认证和内容创建权限
 */
router.put(
  "/:id",
  authenticate,
  checkFeaturePermission("content_creation"),
  collectionController.updateCollection
);

/**
 * 删除合集
 * DELETE /api/collections/:id
 * 需要认证和内容创建权限
 */
router.delete(
  "/:id",
  authenticate,
  checkFeaturePermission("content_creation"),
  collectionController.deleteCollection
);

/**
 * 添加内容到合集
 * POST /api/collections/:id/items
 * 需要认证和内容创建权限
 */
router.post(
  "/:id/items",
  authenticate,
  checkFeaturePermission("content_creation"),
  collectionController.addContentToCollection
);

/**
 * 从合集中移除内容
 * DELETE /api/collections/:id/items/:contentId
 * 需要认证和内容创建权限
 */
router.delete(
  "/:id/items/:contentId",
  authenticate,
  checkFeaturePermission("content_creation"),
  collectionController.removeContentFromCollection
);

/**
 * 更新合集内容排序
 * PUT /api/collections/:id/items/order
 * 需要认证和内容创建权限
 */
router.put(
  "/:id/items/order",
  authenticate,
  checkFeaturePermission("content_creation"),
  collectionController.updateCollectionItemOrder
);

/**
 * 发布合集
 * POST /api/collections/:id/publish
 * 需要认证和内容创建权限
 */
router.post(
  "/:id/publish",
  authenticate,
  checkFeaturePermission("content_creation"),
  collectionController.publishCollection
);

/**
 * 下架合集
 * POST /api/collections/:id/unpublish
 * 需要认证和内容创建权限
 */
router.post(
  "/:id/unpublish",
  authenticate,
  checkFeaturePermission("content_creation"),
  collectionController.unpublishCollection
);

/**
 * 发布合集
 * POST /api/collections/:id/publish
 * 需要认证和内容创建权限
 */
router.post(
  "/:id/publish",
  authenticate,
  checkFeaturePermission("content_creation"),
  collectionController.publishCollection
);

/**
 * 下架合集
 * POST /api/collections/:id/unpublish
 * 需要认证和内容创建权限
 */
router.post(
  "/:id/unpublish",
  authenticate,
  checkFeaturePermission("content_creation"),
  collectionController.unpublishCollection
);

/**
 * 收藏/取消收藏合集
 * POST /api/collections/:id/favorite
 * 需要认证
 */
router.post(
  "/:id/favorite",
  authenticate,
  collectionController.toggleCollectionFavorite
);

module.exports = router;
