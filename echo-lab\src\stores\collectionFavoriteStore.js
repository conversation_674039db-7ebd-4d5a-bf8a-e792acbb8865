/**
 * 合集收藏状态存储
 * 管理用户收藏合集的状态，与favoriteStore保持一致的处理方式
 */
import { defineStore } from "pinia";
import collectionService from "@/services/collectionService";
import { useUserStore } from "@/stores/userStore";

export const useCollectionFavoriteStore = defineStore("collectionFavorite", {
  state: () => ({
    loading: false,
    error: null,
    favoriteCollections: [], // 用户收藏的合集列表
    favoriteCollectionIds: new Set(), // 使用Set存储已收藏的合集ID，方便快速查询
  }),

  getters: {
    /**
     * 检查合集是否已收藏
     * @param {string} collectionId 合集ID
     * @returns {boolean} 是否已收藏
     */
    isFavorite: (state) => (collectionId) => {
      return state.favoriteCollectionIds.has(collectionId);
    },

    /**
     * 获取收藏合集数量
     * @returns {number} 收藏合集数量
     */
    favoriteCount: (state) => {
      return state.favoriteCollections.length;
    },
  },

  actions: {
    /**
     * 获取用户收藏的合集列表
     */
    async fetchFavoriteCollections() {
      const userStore = useUserStore();
      if (!userStore.isLoggedIn) {
        return;
      }

      this.loading = true;
      try {
        // 使用简化的API获取收藏合集
        const response = await collectionService.getUserFavoriteCollections();
        if (response && response.success) {
          this.favoriteCollections = response.collections || [];
          // 更新收藏ID集合
          this.favoriteCollectionIds = new Set(this.favoriteCollections.map((item) => item.id));
        } else {
          throw new Error(response?.error || "获取收藏合集列表失败");
        }
      } catch (err) {
        console.error("获取收藏合集列表失败:", err);
        this.error = err.message;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 更新本地收藏状态
     * @param {string} collectionId 合集ID
     * @param {boolean} isFavorited 是否收藏
     * @param {Object} collectionData 合集数据（可选）
     */
    updateLocalFavoriteState(collectionId, isFavorited, collectionData = null) {
      if (isFavorited) {
        // 添加收藏
        this.favoriteCollectionIds.add(collectionId);
        if (collectionData && !this.favoriteCollections.find(c => c.id === collectionId)) {
          this.favoriteCollections.unshift(collectionData);
        }
      } else {
        // 移除收藏
        this.favoriteCollectionIds.delete(collectionId);
        this.favoriteCollections = this.favoriteCollections.filter(
          (item) => item.id !== collectionId
        );
      }
    },

    /**
     * 切换收藏状态
     * @param {string} collectionId 合集ID
     * @param {Object} collectionData 合集数据（可选）
     */
    async toggleFavorite(collectionId, collectionData = null) {
      const userStore = useUserStore();
      if (!userStore.isLoggedIn) {
        return { success: false, error: "请先登录" };
      }

      try {
        const response = await collectionService.toggleCollectionFavorite(collectionId);
        if (response && response.success) {
          // 更新本地状态
          this.updateLocalFavoriteState(collectionId, response.isFavorited, collectionData);
          return { success: true, isFavorited: response.isFavorited };
        } else {
          throw new Error(response?.error || "收藏操作失败");
        }
      } catch (err) {
        console.error("收藏操作失败:", err);
        return { success: false, error: err.message };
      }
    },



    /**
     * 清空收藏状态
     */
    clearFavorites() {
      this.favoriteCollections = [];
      this.favoriteCollectionIds.clear();
      this.error = null;
    },

    /**
     * 重置状态
     */
    reset() {
      this.loading = false;
      this.error = null;
      this.favoriteCollections = [];
      this.favoriteCollectionIds.clear();
    },
  },
});
