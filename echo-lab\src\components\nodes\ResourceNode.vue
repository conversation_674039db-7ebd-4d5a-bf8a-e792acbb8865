<!--
  资源管理节点组件
  整合标注、翻译和音频功能
-->
<template>
  <div class="resource-node-wrapper">
    <BaseNode :nodeId="nodeId">
      <div class="resource-node-content">
        <!-- 源节点部分 -->
        <div class="source-section">
          <div class="section-title">源节点：</div>
          <div v-if="hasSourceNode" class="source-info">
            <el-tag v-for="sourceNode in sourceNodes" :key="sourceNode.id" size="small" class="source-tag"
              :closable="true" @close="removeSourceNode(sourceNode.id)">
              {{ getSourceNodeLabel(sourceNode) }}
            </el-tag>
          </div>
          <div v-else class="source-empty">
            <el-alert type="warning" :closable="false" show-icon>
              请连接一个文本内容节点作为输入源
            </el-alert>
          </div>
        </div>

        <!-- 标注部分 -->
        <ResourceNodeAnnotation :nodeId="nodeId" :processedResult="processedResult" :params="params"
          :hasSourceNode="hasSourceNode" @update:params="updateParams" @process-node="processNode" />

        <!-- 翻译部分 -->
        <ResourceNodeTranslation :nodeId="nodeId" :processedResult="processedResult" :params="params"
          :hasSourceNode="hasSourceNode" @update:params="updateParams" @process-node="processNode" />

        <!-- 音频部分 -->
        <ResourceNodeAudio :nodeId="nodeId" :processedResult="processedResult" :params="params"
          :hasSourceNode="hasSourceNode" @update:params="updateParams" @process-node="processNode" />
      </div>
    </BaseNode>
  </div>
</template>

<script setup>
import { computed, ref, watch, onMounted } from 'vue';
import { useNodeStore } from '@/core/stores/nodeStore';
import BaseNode from './BaseNode.vue';
import ResourceNodeAnnotation from './ResourceNodeAnnotation.vue';
import ResourceNodeTranslation from './ResourceNodeTranslation.vue';
import ResourceNodeAudio from './ResourceNodeAudio.vue';
import { getSourceNodeLabel } from './ResourceNodeUtils';

const props = defineProps({
  nodeId: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['node-updated']);

const nodeStore = useNodeStore();

// 节点数据
const node = computed(() => nodeStore.getNode(props.nodeId));

// 节点参数
const params = computed({
  get: () => {
    const defaultParams = {
      annotations: {},
      translations: {},
      targets: [],
      autoAnnotate: true,
      autoTranslate: true
    };

    if (!node.value) return defaultParams;

    // 如果节点存在但没有targets，直接返回带默认值的参数，不要调用updateNodeParams
    if (!node.value.params.targets || node.value.params.targets.length === 0) {
      return {
        ...defaultParams,
        ...node.value.params,
        targets: []
      };
    }

    return {
      ...defaultParams,
      ...node.value.params
    };
  },
  set: (value) => nodeStore.updateNodeParams(props.nodeId, value)
});

// 源节点
const sourceNodes = computed(() => {
  if (!node.value || !node.value.sourceIds || node.value.sourceIds.length === 0) {
    return [];
  }

  return node.value.sourceIds.map(id => nodeStore.getNode(id)).filter(Boolean);
});

// 是否有源节点
const hasSourceNode = computed(() => sourceNodes.value.length > 0);

// 处理结果
const processedResult = ref(null);

// 更新参数
function updateParams(newParams) {
  params.value = newParams;
}

// 移除源节点
function removeSourceNode(sourceId) {
  nodeStore.disconnectNodes(sourceId, props.nodeId);
}

// 处理节点
function processNode() {
  try {
    // 强制刷新处理节点
    processedResult.value = nodeStore.processNode(props.nodeId, true);

    // 移除不需要的字段
    if (processedResult.value) {
      delete processedResult.value.sourceContent;
      delete processedResult.value.sourceId;
      delete processedResult.value.sourceLanguage;
    }

    // 通知父组件节点已更新
    emit('node-updated', props.nodeId);
  } catch (error) {
    console.error('处理节点失败:', error);
    processedResult.value = null;
  }
}

// 统一的节点处理逻辑
function handleNodeChange() {
  if (nodeStore.getNode(props.nodeId)) {
    processNode();
  }
}

// 监听源节点变化（统一处理）
watch(sourceNodes, handleNodeChange, { immediate: true, deep: true });
</script>

<style scoped>
/* 使用scoped样式，确保样式不会污染全局 */
.resource-node-wrapper {
  position: relative;
}

.resource-node-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.section-title {
  font-size: 0.875rem;
  color: #606266;
  margin-bottom: 0.25rem;
}

.source-info {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.source-tag {
  margin-right: 0;
}

.preview-section {
  margin-top: 0.75rem;
  padding: 0.625rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-weight: bold;
  color: #606266;
}

.preview-info {
  text-align: center;
  font-size: 0.875rem;
  padding: 0.625rem;
  border-radius: 0.25rem;
  margin-top: 0.75rem;
}

/* 统一的预览背景颜色 */
.success-bg {
  color: #67c23a;
  background-color: #f0f9eb;
}

.warning-bg {
  color: #e6a23c;
  background-color: #fdf6ec;
}

/* 确保音频预览与其他预览保持一致的颜色 */
.audio-preview.success-bg {
  color: #67c23a;
  background-color: #f0f9eb;
}

.audio-preview.warning-bg {
  color: #e6a23c;
  background-color: #fdf6ec;
}

.preview-empty {
  text-align: center;
  color: #909399;
  font-size: 0.875rem;
  padding: 0.625rem;
  background-color: #f5f7fa;
  border-radius: 0.25rem;
  margin-top: 0.75rem;
}

.preview-progress {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.progress-bar {
  height: 0.5rem;
  background-color: #e4e7ed;
  border-radius: 0.25rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #67C23A;
  border-radius: 0.25rem;
  transition: width 0.3s ease;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.status-tag {
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

.status-tag.success {
  background-color: #f0f9eb;
  color: #67c23a;
  border: 0.0625rem solid #e1f3d8;
}

.status-tag.warning {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 0.0625rem solid #faecd8;
}

.status-tag.info {
  background-color: #f4f4f5;
  color: #909399;
  border: 0.0625rem solid #e9e9eb;
}

/* 对话框样式 */
.dialog-content {
  max-height: 100%;
  overflow-y: auto;
}

.dialog-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.dialog-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.annotations-table-container,
.translations-table-container,
.audio-table-container {
  max-height: 60vh;
  overflow-y: auto;
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 0.25rem 0;
}

.original-text-cell,
.processed-text-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  max-height: 6.25rem;
  overflow-y: auto;
  word-break: break-all;
  white-space: pre-wrap;
  padding: 0.5rem;
  border-radius: 0.25rem;
  line-height: 1.5;
}

.original-text-cell {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
}

.processed-text-cell {
  background-color: #f0f9eb;
  border: 1px solid #e1f3d8;
  margin-top: 0.5rem;
}

.text-label {
  font-weight: bold;
  color: #606266;
  font-size: 0.75rem;
}

.processed-text {
  color: #67c23a;
}

/* 高亮原词样式 */
.highlighted-original {
  background-color: #fef0f0;
  color: #f56c6c;
  border-radius: 0.25rem;
  padding: 0 0.25rem;
  font-weight: bold;
  display: inline-block;
}

/* 高亮替换词样式 */
.highlighted-replacement {
  background-color: #f0f9eb;
  color: #67c23a;
  border-radius: 0.25rem;
  padding: 0 0.25rem;
  font-weight: bold;
  display: inline-block;
}

.language-detection {
  display: flex;
  align-items: center;
  margin-top: 0.5rem;
  font-size: 0.75rem;
}

.language-label {
  margin-right: 0.5rem;
}

.empty-annotation,
.empty-translation {
  border-color: #E6A23C;
  background-color: rgba(230, 162, 60, 0.05);
}

.annotation-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.character-annotation-preview {
  width: 100%;
  min-height: 2rem;
  line-height: 1.5;
  font-size: 0.95rem;
  margin-bottom: 0.5rem;
}

.params-row {
  display: flex;
  flex-direction: row;
  gap: 1.5rem;
  margin-bottom: 0.5rem;
}

.param-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.param-item .label {
  margin-right: 0.5rem;
  min-width: 4rem;
}

.param-item .unit {
  margin-left: 0.5rem;
}

.fixed-value {
  text-align: center;
  color: #606266;
  font-size: 0.875rem;
}

.duration-info {
  color: #909399;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.no-audio {
  color: #909399;
  font-size: 0.875rem;
  font-style: italic;
  display: block;
  text-align: center;
}

.audio-player-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.audio-player {
  width: 100%;
  min-height: 30px;
  max-height: 40px;
  display: block;
}

.label {
  white-space: nowrap;
}

.voice-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.speaker-tag {
  align-self: flex-start;
}

.voice-name {
  font-size: 0.75rem;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.compact-speed-settings {
  margin-bottom: 0.75rem;
  padding: 0.5rem 0.75rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
  border: 1px solid #e4e7ed;
}

.speed-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;
}

.speed-label {
  font-weight: bold;
  color: #606266;
}

.status-tags {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 对话框底部布局 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.dialog-footer-left {
  display: flex;
  align-items: center;
}

.dialog-footer-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* 生成音频按钮样式 */
.generate-audio-button {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.3s;
}
</style>
