/**
 * 管理员用户等级路由
 * 提供用户等级和订阅管理相关的API
 */
const express = require('express');
const router = express.Router();
const db = require('../../models');
const { adminOnly } = require('../../middleware/adminMiddleware');
const { authenticate } = require('../../middleware/authMiddleware');

/**
 * 获取所有用户等级
 * GET /api/admin/user-levels
 */
router.get('/', authenticate, adminOnly, async (req, res) => {
  try {
    const levels = await db.UserLevel.findAll({
      order: [['level', 'ASC']]
    });
    
    res.json({
      success: true,
      levels
    });
  } catch (error) {
    console.error('获取用户等级失败:', error);
    res.status(500).json({
      success: false,
      error: '获取用户等级失败'
    });
  }
});

/**
 * 创建用户等级
 * POST /api/admin/user-levels
 */
router.post('/', authenticate, adminOnly, async (req, res) => {
  try {
    const { level, name, description, isDefault } = req.body;
    
    // 验证必要字段
    if (level === undefined || !name) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数: level, name'
      });
    }
    
    // 检查等级是否已存在
    const existingLevel = await db.UserLevel.findOne({
      where: { level }
    });
    
    if (existingLevel) {
      return res.status(400).json({
        success: false,
        error: `等级 ${level} 已存在`
      });
    }
    
    // 如果设置为默认等级，将其他等级设为非默认
    if (isDefault) {
      await db.UserLevel.update(
        { isDefault: false },
        { where: {} }
      );
    }
    
    // 创建等级
    const newLevel = await db.UserLevel.create({
      level,
      name,
      description,
      isDefault: !!isDefault
    });
    
    res.json({
      success: true,
      level: newLevel
    });
  } catch (error) {
    console.error('创建用户等级失败:', error);
    res.status(500).json({
      success: false,
      error: '创建用户等级失败'
    });
  }
});

/**
 * 更新用户等级
 * PUT /api/admin/user-levels/:id
 */
router.put('/:id', authenticate, adminOnly, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, isDefault } = req.body;
    
    // 查找等级
    const level = await db.UserLevel.findByPk(id);
    
    if (!level) {
      return res.status(404).json({
        success: false,
        error: '等级不存在'
      });
    }
    
    // 如果设置为默认等级，将其他等级设为非默认
    if (isDefault && !level.isDefault) {
      await db.UserLevel.update(
        { isDefault: false },
        { where: {} }
      );
    }
    
    // 更新等级
    await level.update({
      name: name || level.name,
      description: description !== undefined ? description : level.description,
      isDefault: isDefault !== undefined ? isDefault : level.isDefault
    });
    
    res.json({
      success: true,
      level
    });
  } catch (error) {
    console.error('更新用户等级失败:', error);
    res.status(500).json({
      success: false,
      error: '更新用户等级失败'
    });
  }
});

/**
 * 删除用户等级
 * DELETE /api/admin/user-levels/:id
 */
router.delete('/:id', authenticate, adminOnly, async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查找等级
    const level = await db.UserLevel.findByPk(id);
    
    if (!level) {
      return res.status(404).json({
        success: false,
        error: '等级不存在'
      });
    }
    
    // 检查是否有用户使用该等级
    const usersCount = await db.User.count({
      where: { level: level.level }
    });
    
    if (usersCount > 0) {
      return res.status(400).json({
        success: false,
        error: `无法删除等级，有 ${usersCount} 个用户正在使用该等级`
      });
    }
    
    // 检查是否为默认等级
    if (level.isDefault) {
      return res.status(400).json({
        success: false,
        error: '无法删除默认等级'
      });
    }
    
    // 删除等级
    await level.destroy();
    
    res.json({
      success: true,
      message: '等级已删除'
    });
  } catch (error) {
    console.error('删除用户等级失败:', error);
    res.status(500).json({
      success: false,
      error: '删除用户等级失败'
    });
  }
});

module.exports = router;
