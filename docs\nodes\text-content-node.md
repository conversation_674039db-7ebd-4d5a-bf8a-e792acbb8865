# 文本内容节点

本文档详细说明了 Echo Lab 中文本内容节点的功能、参数和使用方法。

## 概述

文本内容节点是 Echo Lab 中最基础的节点类型，用于输入和管理文本内容。它支持多种分句模式，可以将文本分割为多个段落或句子，并自动检测语言。

## 节点参数

文本内容节点具有以下参数：

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `text` | 文本内容 | `""` |
| `mode` | 分句模式，可选值: `paragraph`(段落), `sentence`(句子), `none`(不分句) | `paragraph` |
| `splitBy` | 分隔符，用于分句 | `\n` |
| `segments` | 分句结果数组 | `[]` |
| `segmentsCustomized` | 是否自定义分句 | `false` |
| `language` | 语言代码，如 `ja`, `en`, `zh-CN` | `auto` |

## 分句模式

### 段落模式 (paragraph)

段落模式按照指定的分隔符（默认为换行符 `\n`）将文本分割为多个段落。

```javascript
// 段落模式分句示例
const text = "今日は良い天気ですね。\n日本語の勉強は楽しいです。";
const segments = splitByParagraph(text, "\n");
// 结果:
// [
//   { id: "seg_abc123", content: "今日は良い天気ですね。", language: "ja" },
//   { id: "seg_def456", content: "日本語の勉強は楽しいです。", language: "ja" }
// ]
```

### 句子模式 (sentence)

句子模式尝试将文本分割为单独的句子，通常根据句号、问号、感叹号等标点符号进行分割。

```javascript
// 句子模式分句示例
const text = "今日は良い天気ですね。日本語の勉強は楽しいです。";
const segments = splitBySentence(text);
// 结果:
// [
//   { id: "seg_abc123", content: "今日は良い天気ですね。", language: "ja" },
//   { id: "seg_def456", content: "日本語の勉強は楽しいです。", language: "ja" }
// ]
```

### 不分句模式 (none)

不分句模式将整个文本作为一个段落处理，不进行分割。

```javascript
// 不分句模式示例
const text = "今日は良い天気ですね。日本語の勉強は楽しいです。";
const segments = [{ id: "seg_abc123", content: text, language: "ja" }];
```

## 语言检测

文本内容节点使用 cld3-asm 库进行语言检测，可以自动检测每个分句的语言。

```javascript
// 语言检测示例
async function detectLanguage(text) {
  const detector = await cld3.loadModule();
  const result = detector.findLanguage(text);
  return result.language;
}
```

## 分句算法

### 段落分句

段落分句根据指定的分隔符将文本分割为多个段落。

```javascript
// 段落分句算法
function splitByParagraph(text, splitBy = "\n") {
  // 分割文本
  const paragraphs = text.split(splitBy);
  
  // 过滤空段落
  const filteredParagraphs = paragraphs.filter(p => p.trim().length > 0);
  
  // 创建分句结果
  return filteredParagraphs.map(p => ({
    id: `seg_${nanoid()}`,
    content: p.trim(),
    language: "auto"  // 稍后会更新为检测到的语言
  }));
}
```

### 句子分句

句子分句尝试将文本分割为单独的句子，支持多种语言的句子边界检测。

```javascript
// 句子分句算法
function splitBySentence(text) {
  // 定义句子结束标记
  const sentenceEndings = ['.', '。', '!', '！', '?', '？', ';', '；'];
  
  // 初始化结果
  const sentences = [];
  let currentSentence = '';
  
  // 遍历文本
  for (let i = 0; i < text.length; i++) {
    const char = text[i];
    currentSentence += char;
    
    // 检查是否是句子结束
    if (sentenceEndings.includes(char) && i < text.length - 1) {
      // 检查下一个字符是否是空格或换行
      const nextChar = text[i + 1];
      if (nextChar === ' ' || nextChar === '\n' || nextChar === '\r') {
        // 添加当前句子到结果
        sentences.push(currentSentence.trim());
        currentSentence = '';
      }
    }
  }
  
  // 添加最后一个句子（如果有）
  if (currentSentence.trim().length > 0) {
    sentences.push(currentSentence.trim());
  }
  
  // 创建分句结果
  return sentences.map(s => ({
    id: `seg_${nanoid()}`,
    content: s,
    language: "auto"  // 稍后会更新为检测到的语言
  }));
}
```

## 自定义分句

用户可以手动编辑分句结果，包括修改内容、删除分句、添加分句等。当用户手动编辑分句结果时，`segmentsCustomized` 参数会被设置为 `true`，表示分句结果已被自定义。

```javascript
// 自定义分句示例
function customizeSegments(segments) {
  // 设置自定义标志
  this.params.segmentsCustomized = true;
  
  // 更新分句结果
  this.params.segments = segments;
}
```

## 节点方法

### 更新文本

更新文本内容并重新分句。

```javascript
// 更新文本方法
function updateText(text) {
  // 更新文本
  this.params.text = text;
  
  // 如果没有自定义分句，重新分句
  if (!this.params.segmentsCustomized) {
    this.resegment();
  }
}
```

### 重新分句

根据当前的分句模式重新分句。

```javascript
// 重新分句方法
async function resegment() {
  // 获取文本
  const text = this.params.text;
  
  // 根据分句模式分句
  let segments;
  if (this.params.mode === 'paragraph') {
    segments = splitByParagraph(text, this.params.splitBy);
  } else if (this.params.mode === 'sentence') {
    segments = splitBySentence(text);
  } else {
    // 不分句模式
    segments = [{
      id: `seg_${nanoid()}`,
      content: text,
      language: "auto"
    }];
  }
  
  // 检测语言
  for (const segment of segments) {
    segment.language = await detectLanguage(segment.content);
  }
  
  // 更新分句结果
  this.params.segments = segments;
  this.params.segmentsCustomized = false;
}
```

### 更新分句模式

更新分句模式并重新分句。

```javascript
// 更新分句模式方法
function updateMode(mode) {
  // 更新分句模式
  this.params.mode = mode;
  
  // 重新分句
  this.resegment();
}
```

### 更新分隔符

更新分隔符并重新分句。

```javascript
// 更新分隔符方法
function updateSplitBy(splitBy) {
  // 更新分隔符
  this.params.splitBy = splitBy;
  
  // 如果是段落模式，重新分句
  if (this.params.mode === 'paragraph') {
    this.resegment();
  }
}
```

## 节点 UI

文本内容节点的 UI 包括文本编辑器和分句设置。

```vue
<!-- 文本内容节点 UI -->
<template>
  <div class="text-content-node">
    <div class="text-editor">
      <textarea
        v-model="text"
        @input="updateText"
        placeholder="输入文本内容..."
      ></textarea>
    </div>
    <div class="segment-settings">
      <div class="mode-selector">
        <label>分句模式:</label>
        <select v-model="mode" @change="updateMode">
          <option value="paragraph">段落</option>
          <option value="sentence">句子</option>
          <option value="none">不分句</option>
        </select>
      </div>
      <div v-if="mode === 'paragraph'" class="split-by-input">
        <label>分隔符:</label>
        <input
          v-model="splitBy"
          @input="updateSplitBy"
          placeholder="分隔符"
        />
      </div>
    </div>
    <div class="segments-list">
      <div
        v-for="segment in segments"
        :key="segment.id"
        class="segment-item"
      >
        <div class="segment-content">{{ segment.content }}</div>
        <div class="segment-language">{{ segment.language }}</div>
      </div>
    </div>
  </div>
</template>
```

## 节点示例

以下是一个文本内容节点的示例：

```javascript
// 文本内容节点示例
const textContentNode = {
  id: "textContent_123abc",
  type: "textContent",
  customName: "日语文本",
  params: {
    text: "今日は良い天気ですね。\n日本語の勉強は楽しいです。",
    mode: "paragraph",
    splitBy: "\n",
    segments: [
      {
        id: "seg_abc123",
        content: "今日は良い天気ですね。",
        language: "ja"
      },
      {
        id: "seg_def456",
        content: "日本語の勉強は楽しいです。",
        language: "ja"
      }
    ],
    segmentsCustomized: false
  },
  sourceIds: [],
  position: { x: 100, y: 100 }
};
```

## 最佳实践

1. **文本输入**:
   - 使用段落模式处理多段文本
   - 使用句子模式处理长段落
   - 使用不分句模式处理短文本

2. **语言检测**:
   - 检查自动检测的语言是否正确
   - 必要时手动修改语言

3. **分句优化**:
   - 避免过长的分句
   - 避免过短的分句
   - 确保每个分句都是完整的语义单元
