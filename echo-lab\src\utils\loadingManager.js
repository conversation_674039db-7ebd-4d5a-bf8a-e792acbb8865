/**
 * Loading管理器
 * 提供更完善的loading控制和调试功能
 */

class LoadingManager {
  constructor() {
    this.isHiding = false;
    this.isHidden = false;
    this.hideCallCount = 0;
    this.debugMode = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
  }

  /**
   * 记录调试信息
   */
  log(...args) {
    if (this.debugMode) {
      console.log('[LoadingManager]', ...args);
    }
  }

  /**
   * 获取loading元素
   */
  getLoadingElement() {
    return document.getElementById('loading');
  }

  /**
   * 检查loading是否可见
   */
  isVisible() {
    const element = this.getLoadingElement();
    if (!element) return false;
    
    const style = window.getComputedStyle(element);
    return style.display !== 'none' && style.opacity !== '0' && !this.isHidden;
  }

  /**
   * 隐藏loading（主要方法）
   */
  hide() {
    this.hideCallCount++;
    this.log(`hide() 被调用第 ${this.hideCallCount} 次`);

    // 如果已经在隐藏过程中或已隐藏，直接返回
    if (this.isHiding || this.isHidden) {
      this.log('Loading已在隐藏过程中或已隐藏，跳过');
      return Promise.resolve();
    }

    const element = this.getLoadingElement();
    if (!element) {
      this.log('Loading元素不存在');
      this.isHidden = true;
      return Promise.resolve();
    }

    this.log('开始隐藏loading');
    this.isHiding = true;

    return new Promise((resolve) => {
      try {
        // 添加过渡效果
        element.style.transition = 'opacity 0.3s ease';
        element.style.opacity = '0';

        // 等待动画完成后移除元素
        setTimeout(() => {
          try {
            if (element && element.parentNode) {
              element.parentNode.removeChild(element);
              this.log('Loading元素已移除');
            }
            this.isHidden = true;
            this.isHiding = false;
            resolve();
          } catch (error) {
            this.log('移除loading元素时出错:', error);
            this.forceHide();
            resolve();
          }
        }, 300);

      } catch (error) {
        this.log('隐藏loading时出错:', error);
        this.forceHide();
        resolve();
      }
    });
  }

  /**
   * 强制隐藏loading
   */
  forceHide() {
    this.log('强制隐藏loading');
    try {
      const element = this.getLoadingElement();
      if (element) {
        element.style.display = 'none';
        element.style.opacity = '0';
      }
      this.isHidden = true;
      this.isHiding = false;
    } catch (error) {
      this.log('强制隐藏时出错:', error);
    }
  }

  /**
   * 获取状态信息
   */
  getStatus() {
    return {
      isVisible: this.isVisible(),
      isHiding: this.isHiding,
      isHidden: this.isHidden,
      hideCallCount: this.hideCallCount,
      elementExists: !!this.getLoadingElement()
    };
  }

  /**
   * 设置超时隐藏
   */
  setTimeoutHide(timeout = 2000) {
    this.log(`设置 ${timeout}ms 后超时隐藏`);
    setTimeout(() => {
      if (this.isVisible()) {
        this.log('超时触发，强制隐藏loading');
        this.hide();
      }
    }, timeout);
  }
}

// 创建全局实例
const loadingManager = new LoadingManager();

// 导出方法
export const hideInitialLoading = () => loadingManager.hide();
export const forceHideLoading = () => loadingManager.forceHide();
export const isLoadingVisible = () => loadingManager.isVisible();
export const getLoadingStatus = () => loadingManager.getStatus();
export const setLoadingTimeout = (timeout) => loadingManager.setTimeoutHide(timeout);

// 开发环境下暴露到全局
if (loadingManager.debugMode) {
  window.loadingManager = loadingManager;
}

export default loadingManager;