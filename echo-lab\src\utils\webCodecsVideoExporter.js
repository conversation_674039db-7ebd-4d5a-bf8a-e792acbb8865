/**
 * 基于WebCodecs API的前端视频合成工具
 * 使用浏览器原生编码能力，性能更好，文件更小
 * 专为Chrome浏览器优化，完全替代后端处理
 */

// 静态导入MP4Muxer
import { Muxer, ArrayBufferTarget } from "mp4-muxer";

/**
 * WebCodecs视频导出器类
 */
export class WebCodecsVideoExporter {
  constructor() {
    this.isSupported = this._checkSupport();
  }

  /**
   * 检查浏览器支持
   */
  _checkSupport() {
    return (
      "VideoEncoder" in window &&
      "VideoFrame" in window &&
      "AudioEncoder" in window &&
      "AudioData" in window
    );
  }

  /**
   * 从时间线数据生成视频
   * @param {Array} timeline - 时间线数据
   * @param {AudioBuffer} audioBuffer - 音频缓冲区
   * @param {Function} renderContent - 内容渲染函数
   * @param {Object} options - 导出选项
   */
  async exportVideo(timeline, audioBuffer, renderContent, options = {}) {
    if (!this.isSupported) {
      throw new Error("当前浏览器不支持WebCodecs API，请使用Chrome 94+");
    }

    const {
      width = 1280,
      height = 720,
      frameRate = 30,
      quality = "medium",
      fileName = "video",
      generateSubtitles = false,
      subtitleLanguages = [],
      configJson = null,
      onProgress = null,
      signal = null,
    } = options;

    // 质量预设
    const qualityPresets = {
      low: { bitrate: 1000000 }, // 1Mbps
      medium: { bitrate: 2500000 }, // 2.5Mbps
      high: { bitrate: 5000000 }, // 5Mbps
    };

    const bitrate =
      qualityPresets[quality]?.bitrate || qualityPresets.medium.bitrate;

    try {
      // 注意：准备阶段已在frontendVideoExporter中完成，这里直接开始生成帧
      // 阶段1: 生成帧
      if (onProgress) onProgress({ phase: "generating-frames", progress: 5 });

      const frames = await this._generateFrames(timeline, renderContent, {
        width,
        height,
        signal,
        onProgress: (progress, current, total) => {
          if (onProgress) {
            const calculatedProgress = Math.min(5 + progress * 55, 60);
            onProgress({
              phase: "generating-frames",
              progress: calculatedProgress, // 帧生成占55%，从5%到60%
              current: current,
              total: total,
            });
          }
        },
      });

      // 阶段2: 初始化编码器
      if (onProgress)
        onProgress({ phase: "initializing-encoder", progress: 60 });

      const { videoEncoder, audioEncoder, muxer, target, audioConfig } =
        await this._initializeEncoders({
          width,
          height,
          frameRate,
          bitrate,
          audioBuffer,
        });

      // 阶段3: 编码视频帧
      if (onProgress) onProgress({ phase: "encoding-video", progress: 62 });

      await this._encodeVideoFrames(videoEncoder, frames, frameRate, {
        signal,
        onProgress: (progress, current, total) => {
          if (onProgress) {
            onProgress({
              phase: "encoding-video",
              progress: Math.min(62 + progress * 18, 80), // 视频编码占18%，从62%到80%
              current: current,
              total: total,
            });
          }
        },
      });

      // 阶段4: 编码音频
      if (onProgress) onProgress({ phase: "encoding-audio", progress: 80 });

      // 检查是否需要重采样或声道转换
      let processedAudioBuffer = audioBuffer;
      const encoderSampleRate = audioConfig.sampleRate;
      const encoderChannels = audioConfig.numberOfChannels;

      const needsResampling = audioBuffer.sampleRate !== encoderSampleRate;
      const needsChannelConversion = audioBuffer.numberOfChannels !== encoderChannels;

      if (needsResampling || needsChannelConversion) {
        console.log(
          `🔄 音频处理: ${audioBuffer.sampleRate}Hz/${audioBuffer.numberOfChannels}声道 -> ${encoderSampleRate}Hz/${encoderChannels}声道`
        );
        processedAudioBuffer = await this._processAudio(
          audioBuffer,
          encoderSampleRate,
          encoderChannels
        );
        console.log(`✅ 音频处理完成: 新时长=${processedAudioBuffer.duration.toFixed(3)}s`);
      } else {
        console.log(`✅ 音频格式匹配，无需处理: ${audioBuffer.sampleRate}Hz/${audioBuffer.numberOfChannels}声道`);
      }

      await this._encodeAudio(audioEncoder, processedAudioBuffer, {
        signal,
        onProgress: (progress) => {
          if (onProgress) {
            onProgress({
              phase: "encoding-audio",
              progress: Math.min(80 + progress * 10, 90), // 音频编码占10%，从80%到90%
              frameProgress: progress,
            });
          }
        },
      });

      // 阶段5: 完成编码
      if (onProgress) onProgress({ phase: "finalizing", progress: 90 });

      await videoEncoder.flush();
      await audioEncoder.flush();

      // 阶段6: 生成最终文件
      muxer.finalize();
      const videoBlob = new Blob([target.buffer], { type: "video/mp4" });

      // 阶段7: 生成字幕文件（如果需要）
      let subtitleFiles = [];
      if (generateSubtitles && subtitleLanguages.length > 0) {
        if (onProgress)
          onProgress({ phase: "generating-subtitles", progress: 95 });

        const { generateSRTSubtitles } = await import("./videoExporter.js");

        for (const language of subtitleLanguages) {
          const srtContent = generateSRTSubtitles(
            timeline,
            language,
            configJson
          );
          subtitleFiles.push({
            name: `subtitle_${language}.srt`,
            content: srtContent,
          });
        }
      }

      if (onProgress) onProgress({ phase: "complete", progress: 100 });

      return {
        videoBlob,
        fileName: `${fileName}.mp4`,
        subtitleFiles,
      };
    } catch (error) {
      if (error.name === "AbortError") {
        console.log("视频导出被取消");
        throw error;
      }

      console.error("WebCodecs视频导出失败:", error);
      throw new Error(`视频导出失败: ${error.message}`);
    }
  }

  /**
   * 判断时间线项目是否需要生成视频帧
   * @param {Object} item - 时间线项目
   * @returns {boolean} 是否需要生成帧
   */
  _shouldGenerateVideoFrame(item) {
    // 有内容或图片就生成帧
    return Boolean(item.content || item.imageUrl);
  }

  /**
   * 获取项目的显示信息（用于日志）
   * @param {Object} item - 时间线项目
   * @returns {Object} 显示信息
   */
  _getItemDisplayInfo(item) {
    if (item.imageUrl) {
      return {
        type: "图片",
        content: "图片内容",
      };
    }

    if (item.content) {
      return {
        type: "文本",
        content: item.content.substring(0, 20) + "...",
      };
    }

    return {
      type: "空白",
      content: "无内容",
    };
  }

  /**
   * 生成视频帧（带缓存优化）
   */
  async _generateFrames(timeline, renderContent, options) {
    const frames = [];

    // 导入帧缓存系统
    const { frameCache, resetCacheStats, printFinalCacheStats } = await import(
      "./frameCache.js"
    );

    // 重置缓存统计
    resetCacheStats();

    console.log(`开始生成 ${timeline.length} 个帧，启用缓存优化`);

    for (let i = 0; i < timeline.length; i++) {
      if (options.signal?.aborted) {
        throw new DOMException("操作已取消", "AbortError");
      }

      const item = timeline[i];

      // 生成缓存键，包含渲染选项
      const cacheOptions = {
        width: options.width,
        height: options.height,
        containerWidth: options.containerWidth,
        containerHeight: options.containerHeight,
        // 从renderContent函数中提取样式信息（如果可能）
        // 这里我们假设样式信息在item或全局配置中
      };

      // 首先判断是否需要为此项目生成视频帧
      if (this._shouldGenerateVideoFrame(item)) {
        const cacheKey = frameCache.generateCacheKey(item, cacheOptions);

        // 尝试从缓存获取
        let canvas = frameCache.get(cacheKey);

        if (!canvas) {
          // 缓存未命中，执行实际渲染
          const contentElement = await Promise.resolve(renderContent(item));
          // 使用现有的renderElementToCanvas函数
          const { renderElementToCanvas } = await import("./videoExporter.js");
          canvas = await renderElementToCanvas(contentElement, {
            width: options.width,
            height: options.height,
            // 传递容器尺寸信息，用于正确的缩放计算
            containerWidth: options.containerWidth,
            containerHeight: options.containerHeight,
            signal: options.signal,
          });
          console.log('renderElementToCanvas', canvas);
          // 将结果存入缓存
          frameCache.set(cacheKey, canvas);
        }
        const frameDuration = item.displayDuration || item.duration;
        const frameStartTime = item.startTime;

        // 验证时长有效性
        if (!frameDuration || frameDuration <= 0) {
          console.error(`项目 ${i + 1} 缺少有效的时长:`, item);
          throw new Error(`项目 ${i + 1} 缺少有效的时长`);
        }

        // 获取显示信息
        const displayInfo = this._getItemDisplayInfo(item);

        console.log(
          `帧 ${i + 1}/${timeline.length}: ${displayInfo.type}="${
            displayInfo.content
          }" 显示时长=${frameDuration.toFixed(3)}s`
        );

        frames.push({
          canvas,
          duration: frameDuration,
          startTime: frameStartTime,
          originalItem: item, // 保存原始时间线项目的引用
        });
      } else {
        // 跳过不需要生成帧的项目（如停顿项目）
        const displayInfo = this._getItemDisplayInfo(item);
        console.log(
          `跳过 ${i + 1}/${timeline.length}: ${displayInfo.type}="${
            displayInfo.content
          }"`
        );
      }

      // 更新进度 - 传递0-1的原始进度值，让上层处理映射
      if (options.onProgress) {
        options.onProgress((i + 1) / timeline.length, i + 1, timeline.length);
      }
    }

    // 打印最终缓存统计
    printFinalCacheStats();

    return frames;
  }

  /**
   * 初始化编码器
   */
  async _initializeEncoders(options) {
    const { width, height, frameRate, bitrate, audioBuffer } = options;

    const target = new ArrayBufferTarget();
    const muxer = new Muxer({
      target,
      video: {
        codec: "avc",
        width,
        height,
        frameRate,
      },
      audio: {
        codec: "aac",
        numberOfChannels: audioBuffer.numberOfChannels,
        sampleRate: audioBuffer.sampleRate,
      },
      fastStart: "in-memory",
      firstTimestampBehavior: "offset",
    });

    // 创建视频编码器
    let videoEncoderError = null;
    const videoEncoder = new VideoEncoder({
      output: (chunk, metadata) => {
        muxer.addVideoChunk(chunk, metadata);
      },
      error: (error) => {
        console.error("视频编码错误:", error);
        videoEncoderError = error;
        // 不要在这里关闭编码器或抛出错误
      },
    });

    // 根据分辨率选择合适的AVC Level
    let codec;
    const pixelCount = width * height;

    if (pixelCount <= 414720) {
      // 480p及以下 (854x480 = 409920)
      codec = "avc1.42001E"; // Level 3.0 - 最大414720像素
    } else if (pixelCount <= 983040) {
      // 720p (1280x720 = 921600)
      codec = "avc1.42001F"; // Level 3.1 - 最大983040像素
    } else if (pixelCount <= 2073600) {
      // 1080p (1920x1080 = 2073600)
      codec = "avc1.420028"; // Level 4.0 - 最大2073600像素
    } else {
      codec = "avc1.42002A"; // Level 4.2 - 更高分辨率
    }

    const videoConfig = {
      codec,
      width,
      height,
      bitrate,
      framerate: frameRate,
    };

    console.log("配置视频编码器:", videoConfig);

    videoEncoder.configure(videoConfig);

    // 检查是否有配置错误
    if (videoEncoderError) {
      throw new Error(`视频编码器配置失败: ${videoEncoderError.message}`);
    }

    console.log("视频编码器配置完成，状态:", videoEncoder.state);

    // 创建音频编码器
    const audioEncoder = new AudioEncoder({
      output: (chunk, metadata) => {
        muxer.addAudioChunk(chunk, metadata);
      },
      error: (error) => {
        console.error("音频编码错误:", error);
      },
    });

    // 配置音频编码器 - WebCodecs只支持44.1kHz和48kHz
    const originalSampleRate = audioBuffer.sampleRate;
    const targetChannels = 1; // 强制使用单声道

    // WebCodecs AudioEncoder只支持44100和48000Hz
    // 对于24kHz，使用48kHz（2倍关系，质量更好）
    let targetSampleRate;
    if (originalSampleRate === 24000) {
      targetSampleRate = 48000; // 24kHz -> 48kHz (2倍关系)
    } else if (originalSampleRate <= 44100) {
      targetSampleRate = 44100;
    } else {
      targetSampleRate = 48000;
    }

    const audioConfig = {
      codec: "mp4a.40.2", // AAC-LC
      sampleRate: targetSampleRate,
      numberOfChannels: targetChannels,
      bitrate: 128000, // 使用128kbps获得更好的音频质量
    };

    console.log(`🎵 音频编码器配置: 原始=${originalSampleRate}Hz/${audioBuffer.numberOfChannels}声道 -> 目标=${targetSampleRate}Hz/${targetChannels}声道`);

    try {
      audioEncoder.configure(audioConfig);
      console.log("音频编码器配置成功，状态:", audioEncoder.state);

      // 等待一小段时间确保配置完成
      await new Promise((resolve) => setTimeout(resolve, 100));

      if (audioEncoder.state !== "configured") {
        throw new Error(`音频编码器配置后状态异常: ${audioEncoder.state}`);
      }
    } catch (error) {
      console.error("音频编码器配置失败:", error);
      throw new Error(`音频编码器配置失败: ${error.message}`);
    }

    return { videoEncoder, audioEncoder, muxer, target, audioConfig };
  }

  /**
   * 编码视频帧
   */
  async _encodeVideoFrames(encoder, frames, frameRate, options) {
    const frameDuration = 1000000 / frameRate; // 微秒
    let cumulativeTime = 0; // 累积时间（微秒）

    for (let i = 0; i < frames.length; i++) {
      if (options.signal?.aborted) {
        throw new DOMException("操作已取消", "AbortError");
      }

      const frame = frames[i];
      const canvas = frame.canvas;

      // 使用累积时间作为帧开始时间
      const frameStartTime = cumulativeTime;

      // 计算这一帧应该重复多少次 - 使用displayDuration
      const frameCount = Math.round(frame.duration * frameRate);

      console.log(
        `编码帧 ${i + 1}: 累积时间=${(cumulativeTime / 1000000).toFixed(
          3
        )}s, 帧数=${frameCount}, 显示时长=${frame.duration.toFixed(3)}s`
      );

      for (let j = 0; j < frameCount; j++) {
        // 计算当前子帧的时间戳
        const currentTimestamp = frameStartTime + j * frameDuration;

        // 创建VideoFrame
        const videoFrame = new VideoFrame(canvas, {
          timestamp: currentTimestamp,
          duration: frameDuration,
        });

        // 检查编码器状态
        if (encoder.state !== "configured") {
          throw new Error(`VideoEncoder状态异常: ${encoder.state}`);
        }

        // 编码帧
        encoder.encode(videoFrame, { keyFrame: i === 0 && j === 0 });

        // 释放帧
        videoFrame.close();
      }

      // 更新累积时间
      cumulativeTime += frame.duration * 1000000; // 转换为微秒

      // 更新进度 - 传递0-1的原始进度值，让上层处理映射
      if (options.onProgress) {
        options.onProgress((i + 1) / frames.length, i + 1, frames.length);
      }
    }
  }

  /**
   * 编码音频
   */
  async _encodeAudio(encoder, audioBuffer, options) {
    const originalSampleRate = audioBuffer.sampleRate;
    const numberOfChannels = audioBuffer.numberOfChannels;
    const frameSize = 1024; // AAC帧大小

    console.log(`🎵 开始音频编码: ${originalSampleRate}Hz, ${numberOfChannels}声道, ${audioBuffer.duration.toFixed(3)}s`);

    // 获取音频数据
    const audioData = [];
    for (let channel = 0; channel < numberOfChannels; channel++) {
      audioData.push(audioBuffer.getChannelData(channel));
    }

    const totalSamples = audioBuffer.length;
    let sampleIndex = 0;
    let timestamp = 0;

    while (sampleIndex < totalSamples) {
      if (options.signal?.aborted) {
        throw new DOMException("操作已取消", "AbortError");
      }

      const remainingSamples = totalSamples - sampleIndex;
      const currentFrameSize = Math.min(frameSize, remainingSamples);

      // 创建planar格式的音频数据（每个声道分开存储）
      const frameData = new Float32Array(currentFrameSize * numberOfChannels);

      // 对于planar格式，数据应该按声道分组，而不是交错
      for (let channel = 0; channel < numberOfChannels; channel++) {
        const channelOffset = channel * currentFrameSize;
        for (let i = 0; i < currentFrameSize; i++) {
          frameData[channelOffset + i] = audioData[channel][sampleIndex + i];
        }
      }

      // 创建AudioData - 关键修复：使用编码器配置的采样率
      const audioFrame = new AudioData({
        format: "f32-planar",
        sampleRate: originalSampleRate, // 保持使用原始采样率，让编码器处理重采样
        numberOfChannels,
        numberOfFrames: currentFrameSize,
        timestamp: timestamp,
        data: frameData,
      });

      // 编码音频帧
      encoder.encode(audioFrame);

      // 释放帧
      audioFrame.close();

      sampleIndex += currentFrameSize;
      // 关键修复：时间戳计算必须使用原始采样率
      timestamp += (currentFrameSize / originalSampleRate) * 1000000;

      // 更新进度
      if (options.onProgress) {
        options.onProgress(sampleIndex / totalSamples);
      }
    }

    console.log(`✅ 音频编码完成: ${totalSamples} 样本, ${(timestamp / 1000000).toFixed(3)}s`);
  }

  /**
   * 处理音频：重采样和声道转换
   */
  async _processAudio(audioBuffer, targetSampleRate, targetChannels) {
    const duration = audioBuffer.duration;
    const newLength = Math.round(duration * targetSampleRate);

    // 创建离线音频上下文进行处理
    const offlineContext = new OfflineAudioContext(
      targetChannels,
      newLength,
      targetSampleRate
    );

    // 创建音频源
    const bufferSource = offlineContext.createBufferSource();
    bufferSource.buffer = audioBuffer;

    // 如果需要声道转换
    if (audioBuffer.numberOfChannels !== targetChannels) {
      if (audioBuffer.numberOfChannels > targetChannels) {
        // 立体声转单声道：使用ChannelMergerNode
        const merger = offlineContext.createChannelMerger(targetChannels);
        bufferSource.connect(merger);
        merger.connect(offlineContext.destination);
      } else {
        // 单声道转立体声：直接连接（会自动复制到所有声道）
        bufferSource.connect(offlineContext.destination);
      }
    } else {
      // 声道数相同，直接连接
      bufferSource.connect(offlineContext.destination);
    }

    bufferSource.start();

    // 执行处理
    return await offlineContext.startRendering();
  }

  /**
   * 重采样音频到目标采样率（保留原方法以兼容）
   */
  async _resampleAudio(audioBuffer, targetSampleRate) {
    return await this._processAudio(audioBuffer, targetSampleRate, audioBuffer.numberOfChannels);
  }

  /**
   * 下载生成的视频
   */
  downloadVideo(videoBlob, fileName) {
    const url = URL.createObjectURL(videoBlob);
    const link = document.createElement("a");
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * 下载视频和字幕文件（打包为ZIP）
   */
  async downloadVideoWithSubtitles(videoBlob, fileName, subtitleFiles = []) {
    if (subtitleFiles.length === 0) {
      // 如果没有字幕文件，直接下载视频
      this.downloadVideo(videoBlob, fileName);
      return;
    }

    try {
      // 动态导入JSZip
      const JSZip = (await import("jszip")).default;
      const zip = new JSZip();

      // 添加视频文件
      zip.file(fileName, videoBlob);

      // 添加字幕文件
      for (const subtitle of subtitleFiles) {
        zip.file(subtitle.name, subtitle.content);
      }

      // 生成ZIP文件
      const zipBlob = await zip.generateAsync({ type: "blob" });

      // 下载ZIP文件
      const baseName = fileName.replace(/\.[^/.]+$/, ""); // 移除扩展名
      const zipFileName = `${baseName}_with_subtitles.zip`;

      const url = URL.createObjectURL(zipBlob);
      const link = document.createElement("a");
      link.href = url;
      link.download = zipFileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("打包下载失败:", error);
      // 如果打包失败，至少下载视频文件
      this.downloadVideo(videoBlob, fileName);
      throw new Error(`打包下载失败: ${error.message}`);
    }
  }
}

// 创建单例实例
export const webCodecsExporter = new WebCodecsVideoExporter();
