/**
 * 用户偏好设置工具
 */

const STORAGE_KEY = 'user_preferences';

export const USER_LEVELS = [
  { key: 'N5', name: 'N5 初学者', description: '基础词汇和语法' },
  { key: 'N4', name: 'N4 基础', description: '日常会话和基本表达' },
  { key: 'N3', name: 'N3 进阶', description: '中级语法和词汇' },
  { key: 'N2', name: 'N2 中级', description: '复杂语法和表达' },
  { key: 'N1', name: 'N1 高级', description: '高级语法和专业词汇' }
];

/**
 * 获取用户偏好设置
 */
export function getUserPreferences() {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.error('获取用户偏好失败:', error);
    return {};
  }
}

/**
 * 保存用户偏好设置
 */
export function saveUserPreferences(preferences) {
  try {
    const current = getUserPreferences();
    const updated = { ...current, ...preferences };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
    return true;
  } catch (error) {
    console.error('保存用户偏好失败:', error);
    return false;
  }
}

/**
 * 获取用户等级
 */
export function getUserLevel() {
  const preferences = getUserPreferences();
  return preferences.level || null;
}

/**
 * 设置用户等级
 */
export function setUserLevel(level) {
  return saveUserPreferences({ level });
}

/**
 * 获取等级信息
 */
export function getLevelInfo(levelKey) {
  return USER_LEVELS.find(level => level.key === levelKey);
}