# 播放策略重置功能修复说明

## 问题描述

播放页面的播放策略重置功能存在问题：
- 重置时没有使用服务器端数据携带的环节信息
- 重置后显示的是当前配置状态，而不是原始的服务器端配置
- 用户期望重置能回到服务器端的原始环节配置

## 修复内容

### 1. 修复 PlaybackSettingsPanel.vue 中的重置逻辑

**修复前：**
```javascript
// 重置时使用当前的 props.settings（可能是已修改的配置）
localSettings.value = JSON.parse(JSON.stringify(props.settings));
```

**修复后：**
```javascript
// 使用服务器端配置重置，而不是当前的props.settings
if (props.serverConfig && props.serverConfig.sections) {
  // 基于服务器端环节信息创建默认配置
  const resetConfig = {
    sections: props.serverConfig.sections.map(serverSection => ({
      // 保留服务器端的环节结构信息
      ...serverSection,
      // 重置为默认的播放参数
      pauseDuration: 3000,
      repeatCount: 4,
      repeatSpeeds: [1.0, 1.0, 1.0, 1.0],
      repeatPauses: [3000, 3000, 3000, 3000],
      enableTranslation: false,
      translationLanguage: '',
      translationPosition: 2,
      enableKeywords: false,
      keywordPosition: 2,
      keywordRepeatCount: 2
    }))
  };
  localSettings.value = resetConfig;
}
```

### 2. 在配置管理器中添加重置到服务器配置的方法

**新增方法：**
```javascript
const resetToServerConfig = () => {
  if (!serverConfig.value || !serverConfig.value.sections) {
    console.error('无法重置：服务器配置不可用');
    return false;
  }

  // 基于服务器端环节信息创建默认配置
  const resetConfig = {
    sections: serverConfig.value.sections.map(serverSection => ({
      ...serverSection,
      // 重置为默认的播放参数
      pauseDuration: 3000,
      repeatCount: 4,
      repeatSpeeds: [1.0, 1.0, 1.0, 1.0],
      repeatPauses: [3000, 3000, 3000, 3000],
      enableTranslation: false,
      translationLanguage: '',
      translationPosition: 2,
      enableKeywords: false,
      keywordPosition: 2,
      keywordRepeatCount: 2
    }))
  };

  settings.value = resetConfig;
  currentTemplate.value = null;
  clearStorage(); // 清除本地存储
  
  return true;
};
```

### 3. 修复 Player.vue 中的重置逻辑

**修复前：**
```javascript
// 使用默认配置重置
configManager.applyDefaultConfig();
```

**修复后：**
```javascript
// 使用服务器端配置重置
const success = configManager.resetToServerConfig();
if (!success) {
  ElMessage.error('无法获取服务器端配置信息');
  return;
}
```

## 修复效果

### 重置行为变化：
1. **重置按钮文本**：从"重置"改为"重置为原始配置"，更明确地说明功能
2. **重置提示**：从"重置为默认配置"改为"重置为服务器端的原始环节配置"
3. **重置逻辑**：现在会正确使用服务器端的环节信息，包括：
   - 环节ID、标题、描述
   - 处理模式（sequence/source）
   - 源节点信息
   - 用户可编辑性设置

### 保留的服务器端信息：
- `id`: 环节唯一标识
- `title`: 环节标题
- `description`: 环节描述  
- `processingMode`: 处理模式（基于序列或基于源节点）
- `userEditable`: 用户是否可编辑
- `sourceIndex`: 序列索引（如果是基于序列）
- `sourceNodeIds`: 源节点ID列表（如果是基于源节点）

### 重置的播放参数：
- `pauseDuration`: 3000ms
- `repeatCount`: 4次
- `repeatSpeeds`: [1.0, 1.0, 1.0, 1.0]
- `repeatPauses`: [3000, 3000, 3000, 3000]
- `enableTranslation`: false
- `enableKeywords`: false
- 其他相关参数

## 验证方法

1. 打开播放页面
2. 修改播放策略设置（如重复次数、播放速度等）
3. 点击"重置为原始配置"按钮
4. 确认重置后的配置：
   - 环节结构应该与服务器端一致
   - 播放参数应该回到默认值
   - 模板状态应该被清除
   - 本地存储应该被清除

## 技术要点

1. **正确的数据源**：重置时使用 `serverConfig` 而不是当前的 `settings`
2. **保留结构信息**：重置时保留服务器端的环节结构，只重置播放参数
3. **清除状态**：重置时清除模板状态和本地存储
4. **错误处理**：当服务器配置不可用时给出明确提示

这样修复后，重置功能就能正确地使用服务器端数据携带的环节信息了。