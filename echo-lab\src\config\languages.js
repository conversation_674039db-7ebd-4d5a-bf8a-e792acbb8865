/**
 * 语言配置
 * 统一管理系统中使用的语言定义
 */

// 支持的语言列表
export const SUPPORTED_LANGUAGES = [
  { value: 'zh-CN', label: '中文简体' },
  { value: 'zh-TW', label: '中文繁体' },
  { value: 'en', label: '英语' },
  { value: 'ja', label: '日语' }
];

/**
 * 获取语言标签
 * @param {string} langCode 语言代码
 * @returns {string} 语言标签
 */
export function getLanguageLabel(langCode) {
  const lang = SUPPORTED_LANGUAGES.find(l => l.value === langCode);
  return lang ? lang.label : langCode;
}

/**
 * 获取语言标签类型（用于UI显示）
 * @param {string} langCode 语言代码
 * @returns {string} 标签类型
 */
export function getLanguageTagType(langCode) {
  switch (langCode) {
    case 'zh-CN':
    case 'zh-TW':
      return 'success';
    case 'en':
      return 'info';
    case 'ja':
      return 'warning';
    default:
      return '';
  }
}

export default {
  SUPPORTED_LANGUAGES,
  getLanguageLabel,
  getLanguageTagType
};
