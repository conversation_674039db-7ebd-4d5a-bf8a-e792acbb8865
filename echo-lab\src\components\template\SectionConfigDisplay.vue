<template>
  <div class="section-config-display">
    <!-- 环节标题 -->
    <div v-if="section.title" class="config-group title-group">
      <div class="group-header">
        <span class="group-icon">📝</span>
        <span class="group-title">环节标题</span>
      </div>
      <div class="title-display">
        <div class="title-text">{{ section.title }}</div>
      </div>
    </div>

    <!-- 基本播放设置 -->
    <div class="config-group">
      <div class="group-header">
        <span class="group-icon">🎵</span>
        <span class="group-title">基本播放设置</span>
      </div>
      <div class="config-grid">
        <div class="config-item">
          <div class="config-label">停顿时长</div>
          <div class="config-value">{{ section.pauseDuration }}ms</div>
        </div>
        <div class="config-item">
          <div class="config-label">重复次数</div>
          <div class="config-value highlight">{{ section.repeatCount }}次</div>
        </div>
      </div>
    </div>

    <!-- 翻译设置 -->
    <div class="config-group">
      <div class="group-header">
        <span class="group-icon">🌐</span>
        <span class="group-title">翻译设置</span>
        <el-tag :type="section.enableTranslation ? 'success' : 'info'" size="small" class="status-tag">
          {{ section.enableTranslation ? '已启用' : '未启用' }}
        </el-tag>
      </div>
      <div v-if="section.enableTranslation" class="config-grid">
        <div class="config-item">
          <div class="config-label">翻译语言</div>
          <div class="config-value">{{ getLanguageName(section.translationLanguage) }}</div>
        </div>
        <div class="config-item">
          <div class="config-label">插入位置</div>
          <div class="config-value">第{{ section.translationPosition }}次重复后</div>
        </div>
      </div>
    </div>

    <!-- 关键词设置 -->
    <div class="config-group">
      <div class="group-header">
        <span class="group-icon">🔑</span>
        <span class="group-title">关键词设置</span>
        <el-tag :type="section.enableKeywords ? 'success' : 'info'" size="small" class="status-tag">
          {{ section.enableKeywords ? '已启用' : '未启用' }}
        </el-tag>
      </div>
      <div v-if="section.enableKeywords" class="config-grid">
        <div class="config-item">
          <div class="config-label">插入位置</div>
          <div class="config-value">第{{ section.keywordPosition }}次重复后</div>
        </div>
        <div class="config-item">
          <div class="config-label">重复次数</div>
          <div class="config-value">{{ section.keywordRepeatCount }}次</div>
        </div>

      </div>
    </div>

    <!-- 高级重复设置 -->
    <div v-if="hasAdvancedSettings" class="config-group advanced">
      <div class="group-header">
        <span class="group-icon">⚡</span>
        <span class="group-title">高级重复设置</span>
      </div>
      <div class="advanced-settings">
        <div class="advanced-item">
          <div class="advanced-label">每次重复速度</div>
          <div class="advanced-values">
            <span v-for="(speed, idx) in section.repeatSpeeds" :key="idx" class="value-chip">
              第{{ idx + 1 }}次: {{ speed }}x
            </span>
          </div>
        </div>
        <div class="advanced-item">
          <div class="advanced-label">每次重复停顿</div>
          <div class="advanced-values">
            <span v-for="(pause, idx) in section.repeatPauses" :key="idx" class="value-chip">
              第{{ idx + 1 }}次: {{ pause }}ms
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { getLanguageLabel } from '@/config/languages';

// Props
const props = defineProps({
  section: {
    type: Object,
    required: true
  }
});

// Computed
const hasAdvancedSettings = computed(() => {
  const { section } = props;

  // 检查是否有自定义的重复速度（与默认值 1.0 不同）
  const hasCustomSpeeds = section.repeatSpeeds &&
    section.repeatSpeeds.some(speed => speed !== 1.0);

  // 检查是否有自定义的重复停顿
  const hasCustomPauses = section.repeatPauses &&
    section.repeatPauses.some(pause => pause !== section.pauseDuration);

  return hasCustomSpeeds || hasCustomPauses;
});

// Methods
const getLanguageName = (language) => {
  return getLanguageLabel(language) || language || '未设置';
};
</script>

<style scoped>
.section-config-display {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.config-group {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 0.75rem;
  border: 1px solid #e9ecef;
}

.config-group.advanced {
  background: #fff3cd;
  border-color: #ffc107;
}

.config-group.title-group {
  background: #e8f5e8;
  border-color: #4caf50;
}

.title-display {
  background: white;
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.title-text {
  font-size: 1rem;
  font-weight: 600;
  color: #2e7d32;
  text-align: center;
}

.group-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #dee2e6;
}

.group-icon {
  font-size: 1rem;
}

.group-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #495057;
  flex: 1;
}

.status-tag {
  margin-left: auto;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 0.75rem;
}

.config-item {
  background: white;
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  text-align: center;
}

.config-label {
  font-size: 0.75rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.config-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #495057;
}

.config-value.highlight {
  color: #667eea;
  background: #e3f2fd;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
}

.advanced-settings {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.advanced-item {
  background: white;
  padding: 0.5rem;
  border-radius: 0.25rem;
  border: 0.0625rem solid #dee2e6;
}

.advanced-label {
  font-size: 0.75rem;
  color: #6c757d;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.advanced-values {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.value-chip {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 0.75rem;
  font-size: 0.625rem;
  font-weight: 600;
}

/* 移动端适配 */
@media (max-width: 48rem) {
  .section-config-display {
    gap: 0.75rem;
  }
  
  .config-group {
    padding: 0.5rem;
  }
  
  .group-header {
    margin-bottom: 0.5rem;
    padding-bottom: 0.375rem;
  }
  
  .group-title {
    font-size: 0.8rem;
  }
  
  .title-display {
    padding: 0.5rem;
  }
  
  .title-text {
    font-size: 0.9rem;
  }
  
  .config-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .config-item {
    padding: 0.375rem;
  }
  
  .config-label {
    font-size: 0.7rem;
  }
  
  .config-value {
    font-size: 0.8rem;
  }
  
  .advanced-settings {
    gap: 0.375rem;
  }
  
  .advanced-item {
    padding: 0.375rem;
  }
  
  .advanced-label {
    font-size: 0.7rem;
    margin-bottom: 0.375rem;
  }
  
  .advanced-values {
    gap: 0.125rem;
  }
  
  .value-chip {
    padding: 0.0625rem 0.375rem;
    font-size: 0.6rem;
  }
}
</style>
