<!--
  合集统计组件
  显示合集的详细统计信息
-->
<template>
  <div class="collection-stats">
    <div class="stats-header">
      <h3>合集统计</h3>
      <el-button @click="refreshStats" :loading="loading" size="small" link>
        <el-icon><i-ep-refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 概览统计 -->
    <div class="stats-overview">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon :size="24" color="#409eff">
            <i-ep-collection />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.totalCollections }}</div>
          <div class="stat-label">总合集数</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <el-icon :size="24" color="#67c23a">
            <i-ep-check />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.publishedCollections }}</div>
          <div class="stat-label">已发布</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <el-icon :size="24" color="#e6a23c">
            <i-ep-edit />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.draftCollections }}</div>
          <div class="stat-label">草稿</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <el-icon :size="24" color="#f56c6c">
            <i-ep-star />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.totalFavorites }}</div>
          <div class="stat-label">总收藏数</div>
        </div>
      </div>
    </div>

    <!-- 详细统计 -->
    <div class="stats-details">
      <!-- 浏览量统计 -->
      <div class="detail-section">
        <h4>浏览量统计</h4>
        <div class="chart-container">
          <div class="simple-chart">
            <div v-for="(item, index) in stats.topViewedCollections" 
                 :key="item.id" 
                 class="chart-bar">
              <div class="bar-label">{{ item.name }}</div>
              <div class="bar-container">
                <div class="bar-fill" 
                     :style="{ width: `${(item.viewCount / stats.maxViews) * 100}%` }">
                </div>
                <span class="bar-value">{{ item.viewCount }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 收藏量统计 -->
      <div class="detail-section">
        <h4>收藏量统计</h4>
        <div class="chart-container">
          <div class="simple-chart">
            <div v-for="(item, index) in stats.topFavoritedCollections" 
                 :key="item.id" 
                 class="chart-bar">
              <div class="bar-label">{{ item.name }}</div>
              <div class="bar-container">
                <div class="bar-fill" 
                     :style="{ width: `${(item.favoriteCount / stats.maxFavorites) * 100}%` }">
                </div>
                <span class="bar-value">{{ item.favoriteCount }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 内容分布 -->
      <div class="detail-section">
        <h4>内容分布</h4>
        <div class="distribution-grid">
          <div class="distribution-item">
            <span class="distribution-label">平均内容数</span>
            <span class="distribution-value">{{ stats.averageItemCount }}</span>
          </div>
          <div class="distribution-item">
            <span class="distribution-label">最多内容数</span>
            <span class="distribution-value">{{ stats.maxItemCount }}</span>
          </div>
          <div class="distribution-item">
            <span class="distribution-label">空合集数</span>
            <span class="distribution-value">{{ stats.emptyCollections }}</span>
          </div>
          <div class="distribution-item">
            <span class="distribution-label">公开合集比例</span>
            <span class="distribution-value">{{ stats.publicRatio }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 建议和提示 -->
    <div class="stats-suggestions" v-if="suggestions.length > 0">
      <h4>优化建议</h4>
      <div class="suggestions-list">
        <div v-for="suggestion in suggestions" 
             :key="suggestion.type" 
             class="suggestion-item"
             :class="`suggestion-${suggestion.level}`">
          <el-icon>
            <i-ep-warning v-if="suggestion.level === 'warning'" />
            <i-ep-info v-else-if="suggestion.level === 'info'" />
            <i-ep-success v-else />
          </el-icon>
          <span>{{ suggestion.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useCollectionStore } from '@/stores/collectionStore';

const collectionStore = useCollectionStore();

// 响应式数据
const loading = ref(false);

// 计算统计数据
const stats = computed(() => {
  const collections = collectionStore.collections;
  
  if (!collections.length) {
    return {
      totalCollections: 0,
      publishedCollections: 0,
      draftCollections: 0,
      totalFavorites: 0,
      totalViews: 0,
      averageItemCount: 0,
      maxItemCount: 0,
      emptyCollections: 0,
      publicRatio: 0,
      maxViews: 1,
      maxFavorites: 1,
      topViewedCollections: [],
      topFavoritedCollections: []
    };
  }

  const totalCollections = collections.length;
  const publishedCollections = collections.filter(c => c.status === 'published').length;
  const draftCollections = totalCollections - publishedCollections;
  const totalFavorites = collections.reduce((sum, c) => sum + (c.favoriteCount || 0), 0);
  const totalViews = collections.reduce((sum, c) => sum + (c.viewCount || 0), 0);
  const totalItems = collections.reduce((sum, c) => sum + (c.itemCount || 0), 0);
  const averageItemCount = Math.round(totalItems / totalCollections);
  const maxItemCount = Math.max(...collections.map(c => c.itemCount || 0));
  const emptyCollections = collections.filter(c => (c.itemCount || 0) === 0).length;
  const publicCollections = collections.filter(c => c.isPublic).length;
  const publicRatio = Math.round((publicCollections / totalCollections) * 100);

  // 排序获取Top数据
  const sortedByViews = [...collections].sort((a, b) => (b.viewCount || 0) - (a.viewCount || 0));
  const sortedByFavorites = [...collections].sort((a, b) => (b.favoriteCount || 0) - (a.favoriteCount || 0));
  
  const maxViews = Math.max(...collections.map(c => c.viewCount || 0), 1);
  const maxFavorites = Math.max(...collections.map(c => c.favoriteCount || 0), 1);

  return {
    totalCollections,
    publishedCollections,
    draftCollections,
    totalFavorites,
    totalViews,
    averageItemCount,
    maxItemCount,
    emptyCollections,
    publicRatio,
    maxViews,
    maxFavorites,
    topViewedCollections: sortedByViews.slice(0, 5),
    topFavoritedCollections: sortedByFavorites.slice(0, 5)
  };
});

// 计算建议
const suggestions = computed(() => {
  const suggestions = [];
  const collections = collectionStore.collections;

  if (stats.value.emptyCollections > 0) {
    suggestions.push({
      type: 'empty',
      level: 'warning',
      message: `您有 ${stats.value.emptyCollections} 个空合集，建议添加内容或删除`
    });
  }

  if (stats.value.draftCollections > stats.value.publishedCollections) {
    suggestions.push({
      type: 'draft',
      level: 'info',
      message: '草稿合集较多，考虑发布一些优质合集与他人分享'
    });
  }

  if (stats.value.publicRatio < 20) {
    suggestions.push({
      type: 'public',
      level: 'info',
      message: '公开合集比例较低，分享优质内容可以获得更多关注'
    });
  }

  if (stats.value.totalViews === 0) {
    suggestions.push({
      type: 'views',
      level: 'warning',
      message: '合集浏览量为零，建议优化标题和描述，提高可发现性'
    });
  }

  return suggestions;
});

// 刷新统计
const refreshStats = async () => {
  loading.value = true;
  try {
    await collectionStore.fetchUserCollections();
  } finally {
    loading.value = false;
  }
};

// 初始化
onMounted(() => {
  if (collectionStore.collections.length === 0) {
    refreshStats();
  }
});
</script>

<style scoped>
.collection-stats {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.stats-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 0.5rem;
  border: 1px solid #e9ecef;
}

.stat-icon {
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #606266;
  margin-top: 0.25rem;
}

.stats-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.detail-section h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
}

.chart-container {
  background: #f8f9fa;
  border-radius: 0.5rem;
  padding: 1rem;
}

.simple-chart {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.chart-bar {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.bar-label {
  font-size: 0.875rem;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bar-container {
  position: relative;
  height: 1.5rem;
  background: #e9ecef;
  border-radius: 0.75rem;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
  border-radius: 0.75rem;
  transition: width 0.3s ease;
}

.bar-value {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.75rem;
  color: #303133;
  font-weight: 500;
}

.distribution-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.distribution-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 0.5rem;
  text-align: center;
}

.distribution-label {
  font-size: 0.875rem;
  color: #606266;
  margin-bottom: 0.5rem;
}

.distribution-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #303133;
}

.stats-suggestions h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
}

.suggestion-warning {
  background: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fbc4c4;
}

.suggestion-info {
  background: #f0f9ff;
  color: #409eff;
  border: 1px solid #b3d8ff;
}

.suggestion-success {
  background: #f0f9ff;
  color: #67c23a;
  border: 1px solid #c2e7b0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .stats-overview {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .stats-details {
    grid-template-columns: 1fr;
  }
  
  .distribution-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
}
</style>