/**
 * 管理日志服务
 * 用于记录管理员操作日志
 */
const db = require("../models");

/**
 * 记录管理员操作日志
 * @param {Object} logData 日志数据
 * @param {string} logData.adminId 管理员ID
 * @param {string} logData.adminEmail 管理员邮箱
 * @param {string} logData.action 操作类型
 * @param {string} logData.targetType 操作目标类型
 * @param {string} logData.targetId 操作目标ID
 * @param {Object} logData.details 操作详情
 * @param {Object} logData.beforeData 操作前数据
 * @param {Object} logData.afterData 操作后数据
 * @param {string} logData.ipAddress IP地址
 * @param {string} logData.result 操作结果
 * @param {string} logData.message 操作结果消息
 * @returns {Promise<Object>} 创建结果
 */
async function logAdminAction(logData) {
  try {
    // 创建日志记录
    const log = await db.AdminLog.create(logData);

    return {
      success: true,
      log,
    };
  } catch (error) {
    console.error("记录管理员操作日志失败:", error);
    // 即使日志记录失败，也不应该影响主要业务流程
    return {
      success: false,
      error: `记录管理员操作日志失败: ${error.message}`,
    };
  }
}

/**
 * 获取管理员操作日志列表
 * @param {Object} options 查询选项
 * @param {number} options.page 页码
 * @param {number} options.limit 每页数量
 * @param {string} options.adminId 管理员ID
 * @param {string} options.action 操作类型
 * @param {string} options.targetType 操作目标类型
 * @param {string} options.targetId 操作目标ID
 * @param {Date} options.startDate 开始日期
 * @param {Date} options.endDate 结束日期
 * @returns {Promise<Object>} 查询结果
 */
async function getAdminLogs(options = {}) {
  try {
    const {
      page = 1,
      limit = 20,
      adminId,
      action,
      targetType,
      targetId,
      startDate,
      endDate,
    } = options;

    // 构建查询条件
    const where = {};

    if (adminId) {
      where.adminId = adminId;
    }

    if (action) {
      where.action = action;
    }

    if (targetType) {
      where.targetType = targetType;
    }

    if (targetId) {
      where.targetId = targetId;
    }

    // 日期范围查询
    if (startDate || endDate) {
      where.created_at = {};

      if (startDate) {
        where.created_at[db.Sequelize.Op.gte] = new Date(startDate);
      }

      if (endDate) {
        where.created_at[db.Sequelize.Op.lte] = new Date(endDate);
      }
    }

    // 查询日志
    const { count, rows } = await db.AdminLog.findAndCountAll({
      where,
      order: [["created_at", "DESC"]],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit),
    });

    return {
      success: true,
      total: count,
      logs: rows,
      page: parseInt(page),
      limit: parseInt(limit),
    };
  } catch (error) {
    console.error("获取管理员操作日志失败:", error);
    return {
      success: false,
      error: `获取管理员操作日志失败: ${error.message}`,
    };
  }
}

module.exports = {
  logAdminAction,
  getAdminLogs,
};
