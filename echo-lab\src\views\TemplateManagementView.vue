<template>
  <div class="template-management-view">
    <SmartPageHeader title="学习模式" :force-show-back="true">
      <template #actions>
        <el-button v-if="userStore.isLoggedIn" type="primary" @click="handleCreate">
          <el-icon>
            <i-ep-plus />
          </el-icon>
          新建模式
        </el-button>
        <el-button v-else type="primary" @click="goToLogin">
          <el-icon>
            <i-ep-user />
          </el-icon>
          登录后创建
        </el-button>
      </template>
    </SmartPageHeader>

    <!-- 主要内容 -->
    <div class="page-content">
      <TemplateManager v-model:showCreateDialog="showCreateDialog" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SmartPageHeader from '@/components/common/SmartPageHeader.vue';
import TemplateManager from '@/components/template/TemplateManager.vue';
import { useTemplateStore } from '@/stores/templateStore';
import { useUserStore } from '@/stores/userStore';
import { isMobileDevice } from '@/utils/deviceDetector';

// 页面标题
document.title = '学习模式 - Echo Lab';

// Router and Stores
const router = useRouter();
const templateStore = useTemplateStore();
const userStore = useUserStore();

// 状态
const showCreateDialog = ref(false);
const isMobile = computed(() => isMobileDevice());

// 处理创建操作
const handleCreate = () => {
  if (isMobile.value) {
    ElMessage.warning('请在电脑端进行模式管理操作');
    return;
  }
  showCreateDialog.value = true;
};

// 跳转到登录页面
const goToLogin = () => {
  router.push('/login?redirect=' + encodeURIComponent('/templates'));
};

// 页面挂载时加载模板数据
onMounted(() => {
  templateStore.loadTemplates();
});
</script>

<style scoped>
.template-management-view {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header-description {
  font-size: 0.9rem;
  color: #606266;
  margin: 0;
  line-height: 1.5;
  font-weight: 400;
}

.page-content {
  width: 100%;
  padding: 0;
}

/* 移动端适配 */
@media (max-width: 48rem) {
  .template-management-view {
    padding: 0;
  }
  
  .page-content {
    padding: 0;
  }
}
</style>
