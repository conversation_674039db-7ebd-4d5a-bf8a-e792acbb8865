/**
 * 隐藏初始加载画面
 * 在应用初始化完成后调用此函数
 */
export function hideInitialLoading() {
  const loadingElement = document.getElementById('loading');
  
  if (!loadingElement) {
    return;
  }
  
  // 防止重复隐藏
  if (loadingElement.dataset.hiding === 'true') {
    return;
  }
  
  loadingElement.dataset.hiding = 'true';
  
  // 添加淡出效果
  loadingElement.style.transition = 'opacity 0.3s ease';
  loadingElement.style.opacity = '0';
  
  // 动画完成后移除元素
  setTimeout(() => {
    if (loadingElement && loadingElement.parentNode) {
      loadingElement.parentNode.removeChild(loadingElement);
    }
  }, 300);
}