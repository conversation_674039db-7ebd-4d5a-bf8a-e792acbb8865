<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import * as Vue from 'vue';
import StandardDialog from '../common/StandardDialog.vue';
import ContentDisplay from './ContentDisplay.vue';
import { getLanguageLabel } from '@/config/languages.js';
import { useUserStore } from '@/stores/userStore';
// 移除已废弃的 handleApiError 引用
import router from '@/router';
import { checkFeaturePermission, getFeatureUsage } from '@/services/featurePermissionService';

import {
  frontendVideoExporter,
  shouldUseFrontendExport,
  getExportInfo
} from '../../utils/frontendVideoExporter';
import { VIDEO_PHASES, VIDEO_PERFORMANCE, VIDEO_HINT_MESSAGES } from '@/config/video';

// 不再需要导入FFmpeg加载状态

// 获取用户状态
const userStore = useUserStore();

// 检查用户是否已登录
const isLoggedIn = computed(() => userStore.isLoggedIn);

// 检查用户是否有视频导出权限
const hasExportPermission = ref(false);

// 功能使用情况
const usageInfo = ref({
  hasLimit: false,
  dailyLimit: null,
  monthlyLimit: null,
  dailyUsage: 0,
  monthlyUsage: 0,
  dailyRemaining: null,
  monthlyRemaining: null
});

// 在组件挂载时检查权限和使用情况
onMounted(async () => {
  // 检测前端导出支持
  frontendExportSupported.value = shouldUseFrontendExport();
  exportInfo.value = getExportInfo();

  if (frontendExportSupported.value) {
    console.log('检测到WebCodecs支持，将使用前端视频合成');
  } else {
    console.log('浏览器不支持WebCodecs API');
  }

  if (isLoggedIn.value) {
    try {
      // 检查权限
      hasExportPermission.value = await checkFeaturePermission('video_export');

      // 获取使用情况
      if (hasExportPermission.value) {
        const usage = await getFeatureUsage('video_export');
        if (usage) {
          usageInfo.value = usage;
        }
      }
    } catch (error) {
      console.error('检查视频导出权限或使用情况失败:', error);
      hasExportPermission.value = false;
    }
  }
});

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  timeline: {
    type: Array,
    default: () => []
  },
  videoConfig: {
    type: Object,
    default: () => ({})
  },
  configJson: {
    type: Object,
    default: null
  },
  contentName: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue', 'export-complete']);

// 对话框可见性计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val);
  }
});

// 导出选项
const exportOptions = ref({
  // 视频选项
  videoQuality: 'medium',
  videoWidth: 1280,
  videoHeight: 720,

  // 字幕选项
  generateSubtitles: true,
  subtitleLanguages: [], // 将在组件挂载时从时间线中提取所有语言

  // 文件名
  fileName: 'echo_lab_video'
});

// 前端导出支持检测
const frontendExportSupported = ref(false);
const exportInfo = ref(null);

// 导出状态
const exportStatus = ref({
  isExporting: false,
  isCancelling: false, // 是否正在取消
  phase: '', // 'generating-frames', 'initializing-encoder', 'encoding-video', 'encoding-audio', 'finalizing', 'complete', 'cancelled'
  progress: 0,
  error: null
});

// 文件名验证
const fileNameError = ref('');

// 验证文件名
const validateFileName = () => {
  const fileName = exportOptions.value.fileName.trim();

  // 清除之前的错误
  fileNameError.value = '';

  // 检查是否为空
  if (!fileName) {
    fileNameError.value = '文件名不能为空';
    return false;
  }

  // 检查长度
  if (fileName.length > 100) {
    fileNameError.value = '文件名不能超过100个字符';
    return false;
  }

  // 检查是否包含非法字符（Windows和Unix系统的非法字符）
  const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
  if (invalidChars.test(fileName)) {
    fileNameError.value = '文件名包含非法字符，请避免使用 < > : " / \\ | ? * 等符号';
    return false;
  }

  // 检查是否以点开头或结尾
  if (fileName.startsWith('.') || fileName.endsWith('.')) {
    fileNameError.value = '文件名不能以点号开头或结尾';
    return false;
  }

  // 检查Windows保留名称
  const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
  if (reservedNames.includes(fileName.toUpperCase())) {
    fileNameError.value = '文件名不能使用系统保留名称';
    return false;
  }

  return true;
};

// 视频质量预设
const videoQualityPresets = VIDEO_PERFORMANCE.QUALITY_PRESETS;

// 可用语言选项 - 从时间线和翻译资源获取
const languageOptions = computed(() => {
  const languages = new Set();

  // 1. 从时间线中获取源语言
  if (props.timeline && props.timeline.length > 0) {
    props.timeline.forEach(item => {
      if (item.language) {
        languages.add(item.language);
      }
    });
  }

  // 2. 从configJson中获取翻译语言
  if (props.configJson?.resources?.translations) {
    Object.keys(props.configJson.resources.translations).forEach(lang => {
      languages.add(lang);
    });
  }

  // 转换为选项数组并排序
  return Array.from(languages)
    .sort()
    .map(lang => ({
      value: lang,
      label: getLanguageLabel(lang)
    }));
});

// 极简的状态描述
const getStatusText = (phase) => {
  switch (phase) {
    case 'preparing':
      return '准备中';
    case 'generating-frames':
    case 'initializing-encoder':
    case 'encoding-video':
    case 'encoding-audio':
    case 'finalizing':
      return '生成中';
    case 'generating-subtitles':
      return '生成字幕';
    case 'complete':
      return '导出完成';
    case 'cancelled':
      return '已取消';
    case 'error':
      return '导出失败';
    default:
      return '处理中';
  }
};

// 获取当前状态文本
const currentStatusText = computed(() => {
  return getStatusText(exportStatus.value.phase);
});

// 删除未使用的calculateSmoothProgress函数，进度计算已在webCodecsVideoExporter中处理



// 初始化字幕语言和文件名的函数
const initializeOptions = () => {
  // 设置所有可用语言为默认选中
  exportOptions.value.subtitleLanguages = languageOptions.value.map(lang => lang.value);

  // 如果有contentName，使用它作为文件名
  if (props.contentName) {
    exportOptions.value.fileName = props.contentName;
  }
};

// 监听contentName变化
watch(() => props.contentName, (newName) => {
  if (newName) {
    exportOptions.value.fileName = newName;
  }
});

// 监听languageOptions变化
watch(languageOptions, (newOptions) => {
  if (newOptions.length > 0) {
    exportOptions.value.subtitleLanguages = newOptions.map(lang => lang.value);
  }
});

// 在组件挂载时初始化
onMounted(() => {
  initializeOptions();
});

// 当前质量预设
const currentQualityPreset = computed(() => {
  return videoQualityPresets[exportOptions.value.videoQuality] || videoQualityPresets.medium;
});

// 更新视频尺寸
const updateVideoDimensions = () => {
  const preset = currentQualityPreset.value;
  exportOptions.value.videoWidth = preset.width;
  exportOptions.value.videoHeight = preset.height;
};

// 监听质量变化
const handleQualityChange = () => {
  updateVideoDimensions();
};





// 关闭对话框
const handleClose = (done) => {
  console.log('handleClose called');
  if (exportStatus.value.isExporting) {
    ElMessage.warning('导出过程中无法关闭，请等待导出完成或取消导出');
    return; // 阻止对话框关闭
  }
  // 允许对话框关闭
  dialogVisible.value = false;
  done();
};

// 取消控制器
const abortController = ref(null);

// 取消导出
const cancelExport = async () => {
  console.log('用户请求取消导出');

  // 设置取消状态
  exportStatus.value.isCancelling = true;

  try {
    // 中止所有异步操作
    if (abortController.value) {
      console.log('中止前端导出操作');
      abortController.value.abort();
      abortController.value = null;
    }
  } finally {
    // 更新本地状态
    exportStatus.value.isExporting = false;
    exportStatus.value.isCancelling = false;
    exportStatus.value.phase = 'cancelled';
    exportStatus.value.progress = 100;

    // 显示成功消息
    ElMessage.success('导出任务已取消');
  }
};



// 前端导出处理函数
const handleFrontendExport = async () => {
  console.log('开始前端视频导出');

  // 获取质量预设
  const qualityPreset = currentQualityPreset.value;

  // 获取播放器的实际尺寸作为参考
  const playerContainer = document.querySelector('.aspect-ratio-container');
  let playerWidth = 1280;
  let playerHeight = 720;

  if (playerContainer) {
    const rect = playerContainer.getBoundingClientRect();
    playerWidth = rect.width;
    playerHeight = rect.height;
    console.log('播放器实际尺寸:', playerWidth, 'x', playerHeight);
  }

  // 计算缩放比例 - 保持播放器的比例关系
  const scaleX = qualityPreset.width / playerWidth;
  const scaleY = qualityPreset.height / playerHeight;
  const scale = Math.min(scaleX, scaleY); // 使用较小的缩放比例保持比例

  console.log('缩放比例:', scale, '播放器尺寸:', playerWidth, 'x', playerHeight, '输出尺寸:', qualityPreset.width, 'x', qualityPreset.height);

  // 创建渲染内容的函数
  const renderContent = async (item) => {
    // 调试所有项目
    console.log('渲染项目:', {
      type: item.type,
      isCover: item.isCover,
      hasImageUrl: !!item.imageUrl,
      hasContent: !!item.content
    });

    // 计算样式信息，用于缓存键生成
    const baseRemSize = 16; // 1rem = 16px (浏览器默认)
    const configFontSize = props.videoConfig.fontSize || 1.25; // 配置的字体大小(rem)
    const actualFontSize = configFontSize * baseRemSize; // 转换为像素

    const textStyle = {
      fontSize: `${actualFontSize}px`, // 使用像素单位
      color: props.videoConfig.textColor || '#FFFFFF',
      textShadow: '1px 1px 2px rgba(0, 0, 0, 0.7)', // 转换为像素单位
      backgroundColor: props.videoConfig.backgroundColor || '#000000'
    };

    // 将样式信息附加到item上，供缓存系统使用
    item._renderOptions = {
      fontSize: textStyle.fontSize,
      textColor: textStyle.color,
      backgroundColor: textStyle.backgroundColor,
      textShadow: textStyle.textShadow,
      copyright: props.videoConfig.copyright
    };

    // 创建一个容器元素，模拟播放器的外观
    const container = document.createElement('div');

    // 设置容器尺寸为播放器的实际尺寸
    container.style.width = `${playerWidth}px`;
    container.style.height = `${playerHeight}px`;
    container.style.backgroundColor = textStyle.backgroundColor;
    container.style.display = 'flex';
    container.style.justifyContent = 'center';
    container.style.alignItems = 'center';
    container.style.position = 'relative';
    container.style.overflow = 'hidden';

    // 设置基准字体大小，与播放器保持一致
    const baseFontSize = 16; // 浏览器默认字体大小
    container.style.fontSize = `${baseFontSize}px`;

    // 创建ContentDisplay容器
    const contentDisplayContainer = document.createElement('div');
    contentDisplayContainer.style.width = '100%';
    contentDisplayContainer.style.height = '100%';
    contentDisplayContainer.style.display = 'flex';
    contentDisplayContainer.style.justifyContent = 'center';
    contentDisplayContainer.style.alignItems = 'center';

    container.appendChild(contentDisplayContainer);

    // 自动判断内容类型
    function getContentType(item) {
      if (item.imageUrl) return 'image';
      if (item.content) return 'text';
      return 'text';
    }

    // 转换时间线项
    const normalizedItem = {
      ...item,
      contentType: getContentType(item)
    };

    // 如果没有imageUrl但是封面，从videoConfig获取
    if (!normalizedItem.imageUrl && item.isCover && props.videoConfig?.cover?.imageUrl) {
      normalizedItem.imageUrl = props.videoConfig.cover.imageUrl;
      normalizedItem.contentType = 'image';
    }

    // 使用Vue创建ContentDisplay组件
    const { createApp } = Vue;
    const app = createApp({
      render() {
        return Vue.h(ContentDisplay, {
          item: normalizedItem,
          copyright: props.videoConfig.copyright,
          textStyle: textStyle
        });
      }
    });

    app.mount(contentDisplayContainer);



    return container;
  };

  try {
    // 使用前端导出器（Timeline模式）
    const result = await frontendVideoExporter.exportVideoFromTimeline(
      props.timeline,
      renderContent,
      {
        videoQuality: exportOptions.value.videoQuality,
        fileName: exportOptions.value.fileName,
        generateSubtitles: exportOptions.value.generateSubtitles,
        subtitleLanguages: exportOptions.value.subtitleLanguages,
        configJson: props.configJson,
        // 传递容器尺寸信息，用于正确的缩放计算
        containerWidth: playerWidth,
        containerHeight: playerHeight,
        onProgress: (progress) => {
          if (!exportStatus.value.isCancelling) {
            exportStatus.value.phase = progress.phase;
            // 直接使用webCodecsVideoExporter计算好的进度，确保在0-100范围内
            const clampedProgress = Math.max(0, Math.min(100, Math.round(progress.progress)));
            exportStatus.value.progress = clampedProgress;

            // 只在关键进度点输出日志
            if (clampedProgress % 10 === 0 || clampedProgress === 100) {
              console.log(`导出进度: ${progress.phase} - ${clampedProgress}%`);
            }
          }
        },
        signal: abortController.value?.signal
      }
    );

    // 导出完成，自动下载
    await frontendVideoExporter.downloadResult(result);

    // 标记完成
    exportStatus.value.isExporting = false;
    exportStatus.value.phase = VIDEO_PHASES.COMPLETE;
    exportStatus.value.progress = 100;

    ElMessage.success('视频导出完成！');
    emit('export-complete', result);

  } catch (error) {
    if (error.name === 'AbortError') {
      console.log('前端导出被取消');
      exportStatus.value.isExporting = false;
      exportStatus.value.isCancelling = false;
      return;
    }

    console.error('前端导出失败:', error);
    exportStatus.value.error = error.message;
    exportStatus.value.isExporting = false;
    ElMessage.error(`前端导出失败: ${error.message}`);
  }
};

// 开始导出
const startExport = async () => {
  console.log('开始导出视频', {
    timeline: props.timeline?.length || 0,
    audioBuffer: !!props.audioBuffer,
    isLoggedIn: isLoggedIn.value
  });

  // 检查用户是否已登录
  if (!isLoggedIn.value) {
    ElMessage.error('请先登录后再导出视频');
    // 跳转到登录页面
    router.push('/login?redirect=' + encodeURIComponent(router.currentRoute.value.fullPath));
    return;
  }

  // 检查用户是否有视频导出权限
  if (!hasExportPermission.value) {
    ElMessage.error('您没有视频导出权限，请升级会员等级');
    // 跳转到升级页面
    router.push('/upgrade?redirect=' + encodeURIComponent(router.currentRoute.value.fullPath));
    return;
  }

  // 检查使用限制
  if (usageInfo.value.hasLimit && usageInfo.value.dailyRemaining <= 0) {
    ElMessage.error('您今日的导出次数已用完，请明天再试或升级会员获取更多次数');
    return;
  }

  if (!props.timeline || props.timeline.length === 0) {
    ElMessage.error('没有可导出的内容');
    return;
  }

  // 不再检查m3u8Url，因为现在使用Timeline模式

  // 验证文件名
  if (!validateFileName()) {
    ElMessage.error(fileNameError.value);
    return;
  }

  try {
    // 创建新的AbortController
    abortController.value = new AbortController();

    // 设置导出状态
    exportStatus.value = {
      isExporting: true,
      isCancelling: false,
      phase: 'preparing',
      progress: 0,
      error: null
    };

    // 检查前端导出支持
    if (!frontendExportSupported.value) {
      ElMessage.error('当前浏览器不支持WebCodecs API，请使用Chrome 94+浏览器');
      exportStatus.value.isExporting = false;
      return;
    }

    console.log('使用前端WebCodecs进行视频合成');
    await handleFrontendExport();






  } catch (error) {
    // 如果是取消导致的错误或者用户已经取消，不显示错误消息
    if (error.name === 'AbortError' || exportStatus.value.isCancelling) {
      console.log('导出已被用户取消');
      return; // 取消函数已经处理了状态更新
    }

    console.error('视频导出失败:', error);

    // 设置错误状态
    exportStatus.value.isExporting = false;
    exportStatus.value.isCancelling = false;
    exportStatus.value.phase = 'error';
    exportStatus.value.error = error.message || '未知错误';
    exportStatus.value.statusMessage = ''; // 清除状态消息

    // 清除控制器
    abortController.value = null;

    // 显示错误消息
    ElMessage.error(`视频导出失败: ${exportStatus.value.error}`);
  }
}
</script>

<template>
  <StandardDialog v-model="dialogVisible" title="导出视频" width="500px" :close-on-click-modal="!exportStatus.isExporting"
    :show-confirm="!exportStatus.isExporting" confirm-text="开始导出" @confirm="startExport" :before-close="handleClose"
    :confirm-disabled="!isLoggedIn || (isLoggedIn && !hasExportPermission) || (isLoggedIn && hasExportPermission && usageInfo.hasLimit && usageInfo.dailyRemaining <= 0)">
    <!-- 导出选项 -->
    <div v-if="!exportStatus.isExporting" class="export-options">
      <!-- 浏览器兼容性警告 -->
      <div v-if="!frontendExportSupported" class="browser-warning">
        <el-icon class="warning-icon">
          <i-ep-warning-filled />
        </el-icon>
        <div class="warning-content">
          <h4>浏览器兼容性提示</h4>
          <p>当前浏览器不支持视频导出功能，请使用最新版Chrome浏览器</p>
        </div>
      </div>

      <!-- 使用限制提示 -->
      <el-alert v-if="isLoggedIn && hasExportPermission && usageInfo.hasLimit" type="info"
        :title="`今日剩余导出次数: ${usageInfo.dailyRemaining}/${usageInfo.dailyLimit} · 本月剩余: ${usageInfo.monthlyRemaining}/${usageInfo.monthlyLimit}`"
        :closable="false" show-icon style="margin-bottom: 1rem;" />

      <!-- 达到限制提示 -->
      <el-alert v-if="isLoggedIn && hasExportPermission && usageInfo.hasLimit && usageInfo.dailyRemaining <= 0"
        type="warning" title="您今日的导出次数已用完，请明天再试或升级会员获取更多次数" :closable="false" show-icon style="margin-bottom: 1rem;" />



      <div class="section">
        <h3 class="section-title">视频质量</h3>
        <div class="quality-grid">
          <div v-for="option in [
            { value: 'low', title: '标准质量', resolution: '480p', desc: '适合快速分享' },
            { value: 'medium', title: '高清质量', resolution: '720p', desc: '推荐选择' },
            { value: 'high', title: '超清质量', resolution: '1080p', desc: '最佳画质' }
          ]" :key="option.value" class="quality-card" :class="{ active: exportOptions.videoQuality === option.value }"
            @click="exportOptions.videoQuality = option.value; handleQualityChange()">
            <div class="quality-header">
              <div class="quality-info">
                <div class="quality-title">{{ option.title }}</div>
                <div class="quality-resolution">{{ option.resolution }}</div>
              </div>
              <el-radio :value="option.value" v-model="exportOptions.videoQuality" @click.stop />
            </div>
            <div class="quality-desc">{{ option.desc }}</div>
          </div>
        </div>
      </div>

      <div class="section">
        <h3 class="section-title">文件设置</h3>
        <div class="form-group">
          <label class="form-label">文件名</label>
          <el-input v-model="exportOptions.fileName" placeholder="输入文件名（不含扩展名）" @blur="validateFileName"></el-input>
          <div v-if="fileNameError" class="file-name-error">{{ fileNameError }}</div>
        </div>
      </div>

      <div class="section">
        <h3 class="section-title">字幕选项</h3>
        <div class="form-group">
          <el-checkbox v-model="exportOptions.generateSubtitles">同时生成字幕文件</el-checkbox>
        </div>

        <div v-if="exportOptions.generateSubtitles" class="form-group">
          <label class="form-label">字幕语言</label>
          <el-checkbox-group v-model="exportOptions.subtitleLanguages" class="subtitle-languages">
            <el-checkbox v-for="lang in languageOptions" :key="lang.value" :value="lang.value">
              {{ lang.label }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
    </div>

    <!-- 导出进度 -->
    <div v-else class="export-progress">
      <div class="progress-header">
        <h3>{{ currentStatusText }}</h3>
        <div class="progress-percentage" :class="{ 'cancelled-text': exportStatus.phase === 'cancelled' }">
          {{ exportStatus.progress }}%
        </div>
      </div>

      <el-progress :percentage="exportStatus.progress" :stroke-width="20" :show-text="false" :status="exportStatus.phase === 'cancelled' ? 'exception' :
        exportStatus.phase === 'error' ? 'exception' :
          exportStatus.phase === 'complete' ? 'success' : ''" class="progress-bar">
      </el-progress>

      <div v-if="exportStatus.error" class="error-message">
        <el-alert type="error" :title="exportStatus.error" :closable="false"></el-alert>
      </div>
    </div>

    <!-- 对话框底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <!-- 未导出状态 -->
        <el-button v-if="!exportStatus.isExporting" @click="dialogVisible = false">取消</el-button>

        <!-- 导出中状态 -->
        <el-button v-else-if="!exportStatus.isCancelling" @click="cancelExport" type="danger" :loading="false">
          取消导出
        </el-button>

        <!-- 取消中状态 -->
        <el-button v-else type="danger" disabled :loading="true">
          取消中...
        </el-button>

        <!-- 开始导出按钮 -->
        <el-button v-if="!exportStatus.isExporting" type="primary" @click="startExport">
          开始导出
        </el-button>
      </div>
    </template>
  </StandardDialog>
</template>

<style scoped>
.export-options {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 0.625rem;
}

/* 浏览器警告样式 */
.browser-warning {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: #fef7e0;
  border: 1px solid #fadb14;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.warning-icon {
  color: #faad14;
  font-size: 1.25rem;
  margin-top: 0.125rem;
}

.warning-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #d48806;
}

.warning-content p {
  margin: 0;
  font-size: 0.8rem;
  color: #ad6800;
  line-height: 1.4;
}

/* 分区样式 */
.section {
  margin-bottom: 1.5rem;
}

.section:last-child {
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
}

/* 质量选项网格 */
.quality-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

.quality-card {
  border: 1px solid #e4e7ed;
  border-radius: 0.5rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #fff;
}

.quality-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.quality-card.active {
  border-color: #409eff;
  background: #f0f8ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.quality-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.quality-info {
  flex: 1;
}

.quality-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.quality-resolution {
  font-size: 0.75rem;
  font-weight: 500;
  color: #409eff;
}

.quality-desc {
  font-size: 0.75rem;
  color: #666;
  line-height: 1.4;
}

/* 表单样式 */
.form-group {
  margin-bottom: 1rem;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 0.5rem;
}

.subtitle-languages {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.file-name-error {
  color: #f56c6c;
  font-size: 0.75rem;
  margin-top: 0.5rem;
}

/* 进度相关样式 */
.export-progress {
  padding: 1rem 0;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.progress-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}



.progress-percentage {
  font-weight: bold;
  font-size: 1.2rem;
  color: #409EFF;
}

.progress-bar {
  margin: 1rem 0;
}

.progress-bar :deep(.el-progress-bar__outer) {
  background-color: #f0f2f5;
  border-radius: 10px;
}

.progress-bar :deep(.el-progress-bar__inner) {
  border-radius: 10px;
  transition: width 0.3s ease;
}

.cancelling-text {
  color: #F56C6C;
  font-size: 0.85rem;
}

.error-message {
  margin-top: 1rem;
}

.cancelled-text {
  color: #F56C6C;
  font-weight: bold;
}

.status-message {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
  color: #409EFF;
  font-size: 0.9rem;
  text-align: center;
  font-style: italic;
}
</style>
