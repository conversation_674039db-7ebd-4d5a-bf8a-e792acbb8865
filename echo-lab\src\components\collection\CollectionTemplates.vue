<!--
  合集模板组件
  提供预设的合集模板，快速创建合集
-->
<template>
  <div class="collection-templates">
    <h3 class="templates-title">选择模板快速创建</h3>
    
    <div class="templates-grid">
      <div v-for="template in templates" :key="template.id" 
           class="template-card" 
           @click="selectTemplate(template)">
        <div class="template-icon">
          <el-icon :size="32">
            <component :is="template.icon" />
          </el-icon>
        </div>
        
        <div class="template-info">
          <h4 class="template-name">{{ template.name }}</h4>
          <p class="template-description">{{ template.description }}</p>
          
          <div class="template-features">
            <el-tag v-for="feature in template.features" 
                    :key="feature" 
                    size="small" 
                    type="info">
              {{ feature }}
            </el-tag>
          </div>
        </div>
        
        <div class="template-action">
          <el-button type="primary" size="small">
            使用模板
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// Emits
const emit = defineEmits(['template-selected']);

// 模板数据
const templates = ref([
  {
    id: 'daily-practice',
    name: '每日练习',
    description: '适合日常听力练习的合集模板',
    icon: 'i-ep-calendar',
    features: ['按日期组织', '循序渐进', '适合初学者'],
    config: {
      tags: '每日练习,听力训练',
      isPublic: false,
      suggestedContentCount: 7
    }
  },
  {
    id: 'topic-series',
    name: '主题系列',
    description: '围绕特定主题的内容合集',
    icon: 'i-ep-collection-tag',
    features: ['主题聚焦', '深度学习', '系统性强'],
    config: {
      tags: '主题学习,系列课程',
      isPublic: true,
      suggestedContentCount: 10
    }
  },
  {
    id: 'difficulty-levels',
    name: '难度分级',
    description: '按难度等级组织的学习合集',
    icon: 'i-ep-sort',
    features: ['难度递进', '分级学习', '适合进阶'],
    config: {
      tags: '分级学习,难度进阶',
      isPublic: true,
      suggestedContentCount: 15
    }
  },
  {
    id: 'exam-prep',
    name: '考试准备',
    description: '针对考试的专项训练合集',
    icon: 'i-ep-medal',
    features: ['考试导向', '重点突出', '高效备考'],
    config: {
      tags: '考试准备,专项训练',
      isPublic: false,
      suggestedContentCount: 20
    }
  }
]);

// 选择模板
const selectTemplate = (template) => {
  emit('template-selected', template);
};
</script>

<style scoped>
.collection-templates {
  padding: 1.5rem;
}

.templates-title {
  margin: 0 0 1.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #303133;
  text-align: center;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.template-card {
  border: 1px solid #e4e7ed;
  border-radius: 0.75rem;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.template-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.template-icon {
  text-align: center;
  margin-bottom: 1rem;
  color: #409eff;
}

.template-info {
  text-align: center;
  margin-bottom: 1rem;
}

.template-name {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
}

.template-description {
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
  color: #606266;
  line-height: 1.5;
}

.template-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
}

.template-action {
  text-align: center;
}
</style>