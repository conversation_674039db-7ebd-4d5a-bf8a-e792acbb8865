<script setup>
import { onMounted, onUnmounted, ref } from 'vue';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },

  playbackRate: {
    type: Number,
    default: 1
  },
  playbackSpeeds: {
    type: Array,
    default: () => [0.5, 0.75, 1, 1.25, 1.5, 2.0]
  },
  canDownload: {
    type: Boolean,
    default: false
  },
  // 文本样式
  textStyle: {
    type: Object,
    default: () => ({
      fontSizePercent: 100,
      color: '#FFFFFF',
      textShadow: '0.0625rem 0.0625rem 0.125rem rgba(0, 0, 0, 0.7)',
      backgroundColor: '#000000'
    })
  },
  // 定时关闭状态
  autoShutdownState: {
    type: Object,
    default: () => ({
      enabled: false,
      minutes: 15,
      remainingSeconds: 0
    })
  }
});

const emit = defineEmits([
  'close',
  'playback-rate-change',
  'text-style-change',
  'toggle-auto-shutdown',
  'set-auto-shutdown-time',
  'toggle-loop'
]);

const loop = ref(false);



// 设置播放速度
const setPlaybackRate = (speed) => {
  emit('playback-rate-change', speed);
};

// 移除了下载音频和导出视频的方法

// 全屏功能已移除

// 预设字体大小选项已直接在模板中使用

// 预设颜色选项
const colorOptions = [
  { label: '白色', value: '#FFFFFF' },
  { label: '金色', value: '#FFD700' },
  { label: '红色', value: '#FF6B6B' },
  { label: '青色', value: '#4ECDC4' },
  { label: '绿色', value: '#9ACD32' }
];

// 设置字体大小
const setFontSize = (percent) => {
  const newTextStyle = { ...props.textStyle, fontSizePercent: percent };
  emit('text-style-change', newTextStyle);
};

// 设置字体颜色
const setFontColor = (color) => {
  const newTextStyle = { ...props.textStyle, color };
  emit('text-style-change', newTextStyle);
};

// 切换定时关闭状态
const toggleAutoShutdown = () => {
  emit('toggle-auto-shutdown');
};

// 设置定时关闭时间
const setAutoShutdownTime = (minutes) => {
  emit('set-auto-shutdown-time', minutes);
};

// 格式化剩余时间
const formatRemainingTime = () => {
  const minutes = Math.floor(props.autoShutdownState.remainingSeconds / 60);
  const seconds = props.autoShutdownState.remainingSeconds % 60;

  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

// 关闭菜单
const closeMenu = () => {
  emit('close');
};

// 阻止滚动穿透，但允许菜单内部滚动
const preventScrollThrough = (e) => {
  // 检查触摸事件是否发生在菜单面板内
  if (props.show) {
    // 获取菜单面板元素
    const menuPanel = document.querySelector('.mobile-menu-panel');
    if (menuPanel) {
      // 检查事件目标是否是菜单面板或其子元素
      if (!menuPanel.contains(e.target)) {
        e.preventDefault();
      }
    } else {
      e.preventDefault();
    }
  }
};

// 监听滚动事件
onMounted(() => {
  document.addEventListener('touchmove', preventScrollThrough, { passive: false });
});

onUnmounted(() => {
  document.removeEventListener('touchmove', preventScrollThrough);
});

// 监听 ESC 键关闭菜单
const handleKeyDown = (e) => {
  if (e.key === 'Escape' && props.show) {
    closeMenu();
  }
};

onMounted(() => {
  window.addEventListener('keydown', handleKeyDown);
});

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyDown);
});

const toggleLoop = () => {
  emit('toggle-loop', loop.value);
};
</script>

<template>
  <teleport to="body">
    <!-- 移动端菜单弹框 -->
    <div class="mobile-menu-overlay" v-if="show" @click="closeMenu"></div>
    <div class="mobile-menu-panel" v-if="show">
      <div class="mobile-menu-header">
        <span>播放设置</span>
        <div class="close-button" @click="closeMenu">
          <el-icon>
            <i-ep-close />
          </el-icon>
        </div>
      </div>

      <div class="mobile-menu-content">


        <!-- 播放速度 -->
        <div class="mobile-menu-item">
          <div class="item-label">
            <el-icon>
              <i-ep-clock />
            </el-icon>
            <span>播放速度</span>
          </div>
          <div class="speed-buttons">
            <div v-for="speed in playbackSpeeds" :key="speed" class="speed-button"
              :class="{ 'active-speed': speed === playbackRate }" @click="setPlaybackRate(speed)">
              {{ speed }}x
            </div>
          </div>
        </div>

        <!-- 循环播放开关 -->
        <div class="mobile-menu-item">
          <div class="item-label">
            <el-icon>
              <svg viewBox="0 0 1024 1024" width="16" height="16">
                <path fill="currentColor"
                  d="M512 128c-212.1 0-384 171.9-384 384s171.9 384 384 384 384-171.9 384-384-171.9-384-384-384zm0 704c-176.7 0-320-143.3-320-320s143.3-320 320-320 320 143.3 320 320-143.3 320-320 320zm-64-320h128v-64h-128v-64l-96 96 96 96v-64z" />
              </svg>
            </el-icon>
            <span class="loop-label">循环播放</span>
            <el-switch v-model="loop" @change="toggleLoop" class="custom-switch" />
          </div>
        </div>

        <!-- 字体大小设置 -->
        <div class="mobile-menu-item">
          <div class="section-title">字体大小</div>
          <div class="slider-label">{{ textStyle.fontSizePercent }}%</div>
          <div class="slider-container">
            <el-slider v-model="textStyle.fontSizePercent" :min="80" :max="200" :step="5" :show-tooltip="false"
              :format-tooltip="value => `${value}%`" @change="setFontSize" class="custom-slider" />
          </div>
        </div>

        <!-- 字体颜色设置 -->
        <div class="mobile-menu-item">
          <div class="section-title">字体颜色</div>
          <div class="color-options">
            <div v-for="color in colorOptions" :key="color.value" class="color-option"
              :style="{ backgroundColor: color.value }" :class="{ 'active-color': textStyle.color === color.value }"
              @click="setFontColor(color.value)" :title="color.label">
            </div>
          </div>
        </div>

        <!-- 定时关闭 -->
        <div class="mobile-menu-item">
          <div class="item-label">
            <el-icon>
              <svg viewBox="0 0 1024 1024" width="16" height="16">
                <path fill="currentColor"
                  d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z">
                </path>
                <path fill="currentColor"
                  d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z">
                </path>
              </svg>
            </el-icon>
            <span>定时关闭</span>
            <span v-if="autoShutdownState.enabled" class="remaining-time">{{ formatRemainingTime() }}</span>
          </div>

          <!-- 开关 -->
          <div class="shutdown-switch">
            <span>启用定时关闭</span>
            <el-switch :model-value="autoShutdownState.enabled" @change="toggleAutoShutdown" active-color="#ff0000"
              class="custom-switch" />
          </div>

          <div class="slider-label">{{ autoShutdownState.minutes }} 分钟</div>
          <div class="slider-container">
            <el-slider v-model="autoShutdownState.minutes" :min="5" :max="120" :step="5" :show-tooltip="false"
              @change="setAutoShutdownTime" class="custom-slider" />
          </div>
        </div>

        <!-- 移除了导出视频和下载音频功能 -->
      </div>
    </div>
  </teleport>
</template>

<style scoped>
/* 移动端菜单样式 */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.mobile-menu-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  background-color: rgba(25, 25, 25, 0.98);
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
  z-index: 20000;
  box-shadow: 0 -0.25rem 1.5rem rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  max-height: 75vh;
  display: flex;
  flex-direction: column;
  animation: fadeInPanel 0.3s ease-out;
  overflow: visible;
  /* 防止整体溢出 */
}

@keyframes fadeInPanel {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.mobile-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  position: sticky;
  top: 0;
  background-color: rgba(25, 25, 25, 0.98);
  z-index: 2;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.15);
}

.mobile-menu-header span {
  font-size: 1.125rem;
  font-weight: 600;
  color: #fff;
  letter-spacing: 0.02em;
}

.mobile-menu-content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-bottom: calc(env(safe-area-inset-bottom, 1rem) + 0.5rem);
  padding-top: 0.5rem;
}

.close-button {
  width: 2.25rem;
  height: 2.25rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.15);
  cursor: pointer;
  color: white;
  font-weight: bold;
  transition: all 0.2s ease;
}

.close-button:hover,
.close-button:active {
  background-color: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.mobile-menu-item {
  padding: 1.25rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  display: block;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.item-label {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  color: #fff;
}

.item-label span {
  margin-left: 0.5rem;
  font-size: 0.9375rem;
  font-weight: 500;
  letter-spacing: 0.01em;
}

.speed-buttons {
  display: flex;
  flex-wrap: nowrap;
  /* 确保不换行 */
  gap: 0.375rem;
  padding: 0 0.25rem;
  margin-top: 0.375rem;
  overflow-x: auto;
  /* 如果内容超出，允许水平滚动 */
  scrollbar-width: none;
  /* 隐藏滚动条 Firefox */
  -ms-overflow-style: none;
  /* 隐藏滚动条 IE/Edge */
}

.speed-buttons::-webkit-scrollbar {
  display: none;
  /* 隐藏滚动条 Chrome/Safari */
}

.speed-button {
  padding: 0.5rem 0.375rem;
  background-color: rgba(255, 255, 255, 0.08);
  border-radius: 0.25rem;
  color: #fff;
  font-size: 0.875rem;
  cursor: pointer;
  text-align: center;
  transition: all 0.2s ease;
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.1);
  flex: 0 0 auto;
  /* 不伸缩，保持自身大小 */
  min-width: 2.5rem;
}

.speed-button:hover {
  background-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-0.0625rem);
}

.speed-button.active-speed {
  background-color: #ff0000;
  color: #fff;
  font-weight: 500;
  box-shadow: 0 0.125rem 0.375rem rgba(255, 0, 0, 0.3);
}

.download-button {
  display: inline-block;
  padding: 0.625rem 1.25rem;
  background-color: #ff0000;
  border-radius: 0.375rem;
  color: #fff;
  font-size: 0.9375rem;
  font-weight: 600;
  cursor: pointer;
  text-align: center;
  transition: all 0.2s ease;
  margin-top: 0.75rem;
  box-shadow: 0 0.125rem 0.375rem rgba(255, 0, 0, 0.3);
  letter-spacing: 0.01em;
}

.download-button:hover,
.download-button:active {
  background-color: #e60000;
  transform: translateY(-0.125rem);
  box-shadow: 0 0.25rem 0.75rem rgba(255, 0, 0, 0.4);
}

/* 文本样式设置相关样式 */
.text-style-section {
  margin-top: 1rem;
  margin-bottom: 1.5rem;
  width: 100%;
  display: block;
}

.section-title {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1.25rem;
  font-weight: 600;
  display: block;
  width: 100%;
  letter-spacing: 0.01em;
  position: relative;
  padding-left: 0.75rem;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.25rem;
  height: 0.875rem;
  width: 0.25rem;
  background-color: #ff0000;
  border-radius: 0.125rem;
}

/* 统一滑块容器样式 */
.slider-container {
  padding: 0 1rem 0 0.5rem;
  margin-top: 0.5rem;
}

.custom-slider {
  --el-slider-height: 0.5rem;
  --el-slider-button-size: 1rem;
  margin: 0;
}

.custom-slider :deep(.el-slider__runway) {
  background-color: rgba(255, 255, 255, 0.2);
  height: 0.5rem;
  margin: 0;
}

.custom-slider :deep(.el-slider__bar) {
  background-color: #ff0000;
  height: 0.5rem;
}

.custom-slider :deep(.el-slider__button) {
  border: 0.125rem solid #ffffff;
  background-color: #ff0000;
  width: 1rem;
  height: 1rem;
  transition: transform 0.2s;
  box-shadow: 0 0 0.25rem rgba(0, 0, 0, 0.5);
}

.custom-slider :deep(.el-slider__button):hover {
  transform: scale(1.2);
}

/* 添加提示文字样式 */
.custom-slider :deep(.el-tooltip__popper) {
  background-color: rgba(0, 0, 0, 0.8) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #fff !important;
  font-size: 0.875rem !important;
  padding: 0.25rem 0.5rem !important;
  border-radius: 0.25rem !important;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.3) !important;
  z-index: 30000 !important;
}

.custom-slider :deep(.el-tooltip__popper .el-popper__arrow::before) {
  background-color: rgba(0, 0, 0, 0.8) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.color-options {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  margin-top: 0.5rem;
}

.color-option {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.3s;
  border: 2px solid transparent;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.active-color {
  border-color: #409eff;
  transform: scale(1.1);
}

/* 定时关闭相关样式 */
.remaining-time {
  font-size: 0.75rem;
  margin-left: 0.5rem;
  color: #fff;
  background-color: rgba(255, 0, 0, 0.8);
  border-radius: 0.25rem;
  padding: 0.125rem 0.25rem;
}

.shutdown-switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1rem 0;
  color: #fff;
}

/* 移除不需要的样式 */
.time-options-grid,
.time-option {
  display: none;
}

/* 恢复 .slider-label 样式 */
.slider-label {
  text-align: center;
  color: #fff;
  font-size: 0.95rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
  margin-top: 0.25rem;
}

.custom-switch {
  --el-switch-on-color: #ff0000;
}

.loop-label {
  margin-right: 1rem;
}
</style>
