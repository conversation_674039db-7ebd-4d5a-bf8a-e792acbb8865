/**
 * 声音信息模型
 * 存储TTS服务的声音信息
 */
module.exports = (sequelize, DataTypes) => {
  const Voice = sequelize.define(
    "Voice",
    {
      // 自增长ID，主键
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
        comment: "主键ID",
      },
      // 说话人ID，用于API调用
      speaker_id: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        comment: "说话人ID，用于API调用",
        field: "speaker_id",
      },
      // 服务ID
      service_id: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "TTS服务ID，如google、baidu等",
        field: "service_id",
      },
      // 语言代码
      language_code: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "语言代码，如ja、zh-CN等",
        field: "language_code",
      },
      // 声音名称
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "声音名称，如女声A、男声B等",
      },
      // 性别
      gender: {
        type: DataTypes.ENUM("male", "female"),
        allowNull: false,
        defaultValue: "female",
        comment: "声音性别",
      },
      // 是否高级声音
      is_premium: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "是否为高级声音",
        field: "is_premium",
      },
      // 是否禁用
      disabled: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "是否禁用",
      },
      // API参数，JSON格式
      api_params: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "API调用参数，JSON格式",
        field: "api_params",
      },
    },
    {
      // 表名
      tableName: "voices",
      // 时间戳
      timestamps: true,
      // 使用下划线命名法
      underscored: true,
      // 索引
      indexes: [
        {
          name: "voices_service_id",
          fields: ["service_id"],
        },
        {
          name: "voices_language_code",
          fields: ["language_code"],
        },
      ],
    }
  );

  return Voice;
};
