# Echo Lab 直接部署指南

本文档详细说明了如何在服务器上直接部署 Echo Lab 前端和后端。

## 部署准备

### 服务器环境

- **操作系统**：Alibaba Cloud Linux 3（基于 RHEL/CentOS）
- **CPU**：2 核
- **内存**：4GB
- **存储**：50GB
- **带宽**：5Mbps

### 软件要求

- **Node.js**：v18.x
- **MySQL**：v8.0
- **Nginx**：v1.20
- **Git**：v2.30
- **PM2**：最新版本

## 部署步骤

### 1. 安装基础软件

```bash
# 更新系统
sudo yum update -y

# 安装基础工具
sudo yum install -y git wget curl vim

# 安装 Node.js
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 安装 PM2
sudo npm install -g pm2

# 安装 MySQL
sudo yum install -y mysql-server
sudo systemctl start mysqld
sudo systemctl enable mysqld

# 安装 Nginx
sudo yum install -y nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 2. 配置 MySQL

```bash
# 设置 MySQL 安全配置
sudo mysql_secure_installation

# 创建数据库和用户
mysql -u root -p
```

在 MySQL 命令行中执行以下命令：

```sql
CREATE DATABASE `echo-lab` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'echolab'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON `echo-lab`.* TO 'echolab'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. 克隆代码

```bash
# 创建项目目录
mkdir -p /var/www
cd /var/www

# 克隆代码
git clone https://github.com/yourusername/JapRepeater.git
cd JapRepeater
```

### 4. 配置后端

```bash
# 进入后端目录
cd backend

# 创建环境配置文件
cp .env.example .env
```

编辑 `.env` 文件，配置以下环境变量：

```
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=echo-lab
DB_USER=echolab
DB_PASSWORD=your_password

# JWT 配置
JWT_SECRET=your_jwt_secret

# 阿里云 OSS 配置
OSS_REGION=your_oss_region
OSS_ACCESS_KEY_ID=your_oss_access_key_id
OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
OSS_BUCKET=your_oss_bucket

# Google Cloud 配置
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/google-credentials.json

# 百度 AI 配置
BAIDU_APP_ID=your_baidu_app_id
BAIDU_API_KEY=your_baidu_api_key
BAIDU_SECRET_KEY=your_baidu_secret_key

# 邮件配置
EMAIL_HOST=your_email_host
EMAIL_PORT=your_email_port
EMAIL_USER=your_email_user
EMAIL_PASSWORD=your_email_password
EMAIL_FROM=your_email_from
```

### 5. 安装后端依赖

```bash
# 安装后端依赖
npm install

# 初始化数据库
npx sequelize-cli db:migrate
npx sequelize-cli db:seed:all
```

### 6. 配置前端

```bash
# 进入前端目录
cd ../echo-lab

# 安装前端依赖
npm install
```

### 7. 构建前端

```bash
# 构建前端
npm run build
```

### 8. 配置 Nginx

创建 Nginx 配置文件：

```bash
# 创建 Nginx 配置文件
sudo nano /etc/nginx/conf.d/echolab.club.conf
```

配置文件内容：

```nginx
server {
    listen 80;
    server_name echolab.club www.echolab.club;

    # 重定向到 HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name echolab.club www.echolab.club;

    # SSL 配置
    ssl_certificate /etc/letsencrypt/live/echolab.club/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/echolab.club/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;

    # 前端配置
    location / {
        root /var/www/JapRepeater/echo-lab/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # API 配置
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 9. 配置 SSL 证书

```bash
# 安装 Certbot
sudo yum install -y certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d echolab.club -d www.echolab.club

# 自动续期证书
sudo certbot renew --dry-run
```

### 10. 启动服务

```bash
# 创建 PM2 配置文件
cd /var/www/JapRepeater/backend
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'echo-lab-backend',
    script: 'app.js',
    instances: 'max',
    exec_mode: 'cluster',
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    }
  }]
};
EOF

# 启动后端服务
pm2 start ecosystem.config.js

# 保存 PM2 进程列表
pm2 save

# 配置 PM2 开机自启
pm2 startup

# 重启 Nginx
sudo systemctl restart nginx
```

### 11. 验证部署

访问以下 URL 验证部署是否成功：

- 前端：https://echolab.club
- 后端：https://echolab.club/api/health

## 部署脚本

为了简化部署过程，可以创建一个部署脚本：

```bash
# 创建部署脚本
cd /var/www/JapRepeater
cat > deploy.sh << 'EOF'
#!/bin/bash

# Echo Lab 部署脚本

# 设置变量
FRONTEND_DIR="echo-lab"
BACKEND_DIR="backend"
NGINX_CONF_DIR="/etc/nginx/conf.d"
NGINX_CONF_FILE="echolab.club.conf"
WWW_DIR="/var/www/JapRepeater/echo-lab/dist"

# 输出彩色文本
function echo_color() {
  case $1 in
    "red") COLOR='\033[0;31m' ;;
    "green") COLOR='\033[0;32m' ;;
    "yellow") COLOR='\033[0;33m' ;;
    "blue") COLOR='\033[0;34m' ;;
    "purple") COLOR='\033[0;35m' ;;
    "cyan") COLOR='\033[0;36m' ;;
    *) COLOR='\033[0m' ;;
  esac
  NC='\033[0m'
  
  echo -e "${COLOR}$2${NC}"
}

# 更新代码
echo_color "blue" "正在更新代码..."
git pull

# 构建前端
echo_color "blue" "正在构建前端..."
cd $FRONTEND_DIR
npm install
npm run build
cd ..

# 安装后端依赖
echo_color "blue" "正在安装后端依赖..."
cd $BACKEND_DIR
npm install
cd ..

# 重启后端服务
echo_color "blue" "正在重启后端服务..."
cd $BACKEND_DIR
pm2 restart ecosystem.config.js
cd ..

# 重启 Nginx
echo_color "blue" "正在重启 Nginx..."
sudo systemctl restart nginx

echo_color "green" "部署完成！"
EOF

# 设置脚本权限
chmod +x deploy.sh
```

## 更新部署

使用部署脚本更新部署：

```bash
# 更新部署
cd /var/www/JapRepeater
./deploy.sh
```

## 回滚部署

如果需要回滚到之前的版本，可以使用 Git 回滚：

```bash
# 回滚到指定提交
cd /var/www/JapRepeater
git reset --hard <commit_hash>

# 重新部署
./deploy.sh
```

## 日志管理

### 查看后端日志

```bash
# 查看后端日志
pm2 logs echo-lab-backend
```

### 查看 Nginx 日志

```bash
# 查看 Nginx 访问日志
sudo tail -f /var/log/nginx/access.log

# 查看 Nginx 错误日志
sudo tail -f /var/log/nginx/error.log
```

## 数据库备份

定期备份数据库是很重要的：

```bash
# 创建备份目录
mkdir -p /var/backups/echo-lab

# 创建备份脚本
cat > /var/backups/backup_db.sh << 'EOF'
#!/bin/bash

# 设置变量
DB_NAME="echo-lab"
DB_USER="echolab"
DB_PASSWORD="your_password"
BACKUP_DIR="/var/backups/echo-lab"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="${BACKUP_DIR}/${DB_NAME}_${DATE}.sql"

# 备份数据库
mysqldump -u ${DB_USER} -p${DB_PASSWORD} ${DB_NAME} > ${BACKUP_FILE}

# 压缩备份文件
gzip ${BACKUP_FILE}

# 删除 7 天前的备份
find ${BACKUP_DIR} -name "${DB_NAME}_*.sql.gz" -mtime +7 -delete
EOF

# 设置脚本权限
chmod +x /var/backups/backup_db.sh

# 添加定时任务
(crontab -l 2>/dev/null; echo "0 2 * * * /var/backups/backup_db.sh") | crontab -
```

## 故障排除

### 前端无法访问

1. 检查 Nginx 是否正在运行：
   ```bash
   sudo systemctl status nginx
   ```

2. 检查 Nginx 配置是否正确：
   ```bash
   sudo nginx -t
   ```

3. 检查前端文件是否正确部署：
   ```bash
   ls -la /var/www/JapRepeater/echo-lab/dist
   ```

### 后端无法访问

1. 检查后端服务是否正在运行：
   ```bash
   pm2 status
   ```

2. 检查后端日志：
   ```bash
   pm2 logs echo-lab-backend
   ```

3. 检查数据库连接：
   ```bash
   mysql -u echolab -p -e "SHOW DATABASES;"
   ```

### 数据库问题

1. 检查数据库服务是否正在运行：
   ```bash
   sudo systemctl status mysqld
   ```

2. 检查数据库连接：
   ```bash
   mysql -u echolab -p -e "SHOW DATABASES;"
   ```

3. 检查数据库表：
   ```bash
   mysql -u echolab -p -e "USE \`echo-lab\`; SHOW TABLES;"
   ```

## 安全配置

### 防火墙配置

```bash
# 安装防火墙
sudo yum install -y firewalld
sudo systemctl start firewalld
sudo systemctl enable firewalld

# 配置防火墙
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 安全头部配置

编辑 Nginx 配置文件，添加安全头部：

```nginx
# Nginx 安全头部配置
add_header X-Frame-Options "SAMEORIGIN";
add_header X-XSS-Protection "1; mode=block";
add_header X-Content-Type-Options "nosniff";
add_header Referrer-Policy "no-referrer-when-downgrade";
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'";
```

### 数据库安全配置

```bash
# MySQL 安全配置
sudo mysql_secure_installation
```

## 性能优化

### Nginx 优化

编辑 Nginx 配置文件，添加性能优化配置：

```nginx
# Nginx 性能优化
worker_processes auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 65535;
    multi_accept on;
    use epoll;
}

http {
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # 缓存设置
    open_file_cache max=1000 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
```

### MySQL 优化

编辑 MySQL 配置文件，添加性能优化配置：

```ini
# MySQL 性能优化
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
max_connections = 1000
```
