module.exports = {
  apps: [
    {
      name: "echo-lab-backend",
      script: "./backend/app.js",
      instances: "max", // 使用最大实例数（根据CPU核心数）
      exec_mode: "cluster", // 使用集群模式
      autorestart: true,
      watch: false,
      max_memory_restart: "1G",
      env: {
        NODE_ENV: "production",
        PORT: 3000,
      },
      env_development: {
        NODE_ENV: "development",
        PORT: 3000,
      },
      // 增加超时设置
      kill_timeout: 10000, // 等待进程正常退出的时间（毫秒）
      wait_ready: true, // 等待进程发送ready信号
      listen_timeout: 50000, // 等待进程监听端口的时间（毫秒）

      // 日志配置
      log_file: "./logs/combined.log", // 合并日志文件
      out_file: "./logs/out.log", // 标准输出日志
      error_file: "./logs/error.log", // 错误日志
      log_date_format: "YYYY-MM-DD HH:mm:ss Z", // 日志时间格式
      merge_logs: true, // 合并集群日志
      max_restarts: 10, // 最大重启次数
      min_uptime: "10s", // 最小运行时间
    },
  ],
};
