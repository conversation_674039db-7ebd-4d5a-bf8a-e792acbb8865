/**
 * 播放器配置管理 Composable
 * 统一管理播放器配置的保存、加载、模板匹配等逻辑
 * 重构后的版本 - 修复模板状态持久化问题
 */
import { ref, computed } from "vue";
import { useTemplateStore } from "@/stores/templateStore";
import globalPlaybackService from '@/services/globalPlaybackService';


const validateConfig = (config) => {
  const errors = [];
  
  if (!config || typeof config !== 'object') {
    errors.push('配置必须是对象');
    return { valid: false, errors };
  }
  
  if (!config.sections || !Array.isArray(config.sections)) {
    errors.push('配置必须包含sections数组');
    return { valid: false, errors };
  }
  
  return { valid: errors.length === 0, errors };
};

export function usePlayerConfig(contentId) {
  const templateStore = useTemplateStore();

  // 配置状态
  const settings = ref({ sections: [] });
  const currentTemplate = ref(null);
  const serverConfig = ref({ sections: [] });
  
  // 配置状态跟踪
  const configState = ref({
    type: 'server', // 'custom' | 'template' | 'global' | 'server'
    source: null,   // 具体的模板对象或null
    name: ''        // 显示名称
  });

  // 存储键名
  const storageKeys = computed(() => {
    const id = contentId.value;
    return {
      settings: `ps_${id}`,
      template: `pt_${id}`,
      source: `src_${id}`
    };
  });



  /**
   * 统一的localStorage操作
   */
  const setStorageItem = (key, value) => {
    if (value) {
      localStorage.setItem(key, typeof value === 'string' ? value : JSON.stringify(value));
    } else {
      localStorage.removeItem(key);
    }
  };

  /**
   * 保存配置到localStorage
   */
  const saveToStorage = () => {
    try {
      const keys = storageKeys.value;
      setStorageItem(keys.settings, settings.value);
      setStorageItem(keys.template, currentTemplate.value?.id);
      console.log("已保存配置到localStorage");
    } catch (error) {
      console.error("保存配置失败:", error);
    }
  };

  /**
   * 从localStorage加载配置
   */
  const loadFromStorage = () => {
    try {
      const keys = storageKeys.value;
      const savedSettings = localStorage.getItem(keys.settings);
      const savedTemplateId = localStorage.getItem(keys.template);
      const savedSource = localStorage.getItem(keys.source);

      console.log("从localStorage加载配置:", {
        settingsKey: keys.settings,
        templateKey: keys.template,
        sourceKey: keys.source,
        savedSettings: savedSettings ? "found" : "not found",
        savedTemplateId: savedTemplateId || "none",
        savedSource: savedSource || "none"
      });

      return {
        settings: savedSettings ? JSON.parse(savedSettings) : null,
        templateId: savedTemplateId,
        source: savedSource
      };
    } catch (error) {
      console.error("从localStorage加载配置失败:", error);
      return {
        settings: null,
        templateId: null,
      };
    }
  };

  /**
   * 清除存储
   */
  const clearStorage = () => {
    const keys = storageKeys.value;
    localStorage.removeItem(keys.settings);
    localStorage.removeItem(keys.template);
    localStorage.removeItem(keys.source);
  };

  /**
   * 检查设置是否与模板匹配（简化版）
   */
  const isSettingsMatchTemplate = () => {
    // 简化逻辑：只要用户修改了设置，就认为不匹配
    return false;
  };

  /**
   * 智能更新模板状态（仅在用户主动修改时调用）
   */
  const updateTemplateState = () => {
    console.log("用户修改设置，清除模板和来源状态");
    currentTemplate.value = null;
    updateConfigState('custom');
    
    // 清除所有关联标记
    const keys = storageKeys.value;
    setStorageItem(keys.template, null);
    setStorageItem(keys.source, null);
  };



  /**
   * 应用模板（只加载到内存，不保存到localStorage）
   */
  const applyTemplate = async (template) => {
    try {
      console.log("应用模板:", template.name, template.id);

      // 1. 应用模板配置到内存
      const appliedConfig = applyTemplateToServerConfig(serverConfig.value, template);
      settings.value = appliedConfig;

      // 2. 设置当前模板（临时状态）
      currentTemplate.value = template;
      console.log("已设置当前模板:", currentTemplate.value.name, currentTemplate.value.id);

      // 3. 更新配置状态显示
      updateConfigState('template', template);

      // 注意：不保存到localStorage，只有在用户点击"应用设置"时才保存

      // 4. 增加模板使用次数
      if (template.type === "user") {
        templateStore.useTemplate(template.id).catch(console.error);
      }

      console.log("模板应用成功（仅内存）:", template.name);
      return true;
    } catch (error) {
      console.error("应用模板失败:", error);
      return false;
    }
  };

  /**
   * 智能加载配置
   */
  const smartLoadConfig = async () => {
    try {
      console.log("开始智能加载配置...");

      // 1. 从localStorage加载保存的数据
      const savedData = loadFromStorage();

      // 2. 如果有保存的用户设置，使用用户设置
      if (savedData.settings) {
        console.log("找到保存的用户设置，正在应用...");
        settings.value = savedData.settings;

        // 3. 根据配置来源和模板ID设置状态
        if (savedData.source === 'server') {
          console.log('检测到 server 来源标记，设置为内容默认');
          currentTemplate.value = null;
          updateConfigState('server');
        } else if (savedData.templateId) {
          console.log('检测到模板ID:', savedData.templateId);
          const savedTemplate = templateStore.templateById(savedData.templateId);
          if (savedTemplate) {
            console.log('找到模板:', savedTemplate.name);
            currentTemplate.value = savedTemplate;
            updateConfigState('template', savedTemplate);
          } else {
            console.log('模板不存在，设置为自定义');
            currentTemplate.value = null;
            updateConfigState('custom');
          }
        } else {
          console.log('没有模板和来源标记，设置为自定义');
          updateConfigState('custom');
        }
        return true;
      }

      // 4. 如枟没有保存的用户设置，检查全局默认模板
      const globalTemplateId = globalPlaybackService.getGlobalTemplateId();
      if (globalTemplateId) {
        console.log('检查全局默认模板:', globalTemplateId);
        const globalTemplate = templateStore.templateById(globalTemplateId);
        if (globalTemplate && serverConfig.value?.sections) {
          console.log('应用全局默认模板:', globalTemplate.name);
          settings.value = applyTemplateToServerConfig(serverConfig.value, globalTemplate);
          updateConfigState('global', globalTemplate);
          return true;
        }
      }
      
      // 5. 最后使用服务器原始配置
      if (
        serverConfig.value &&
        serverConfig.value.sections &&
        serverConfig.value.sections.length > 0
      ) {
        console.log("没有全局模板，使用服务器原始配置");
        settings.value = JSON.parse(JSON.stringify(serverConfig.value));
        updateConfigState('server');
      } else {
        console.log("服务器配置未准备好，跳过配置加载");
        return false;
      }
      return true;
    } catch (error) {
      console.error("智能加载配置失败:", error);
      return false;
    }
  };

  /**
   * 更新配置状态
   */
  const updateConfigState = (type, template = null) => {
    configState.value = {
      type,
      source: template,
      name: template ? template.name : ''
    };
    console.log('配置状态已更新:', configState.value);
  };

  /**
   * 验证和优化配置
   */
  const validateAndOptimizeConfig = (config) => {
    const validation = validateConfig(config);
    if (!validation.valid) {
      console.warn("配置验证失败:", validation.errors);
      return null;
    }
    return config;
  };

  /**
   * 应用模板到服务器配置
   */
  const applyTemplateToServerConfig = (serverConfig, template) => {
    if (!template?.config?.sections) {
      throw new Error('无效的模板');
    }

    const templateSections = template.config.sections;
    const serverSections = serverConfig?.sections || [];
    const resultSections = [];

    // 按模板环节数量生成配置
    templateSections.forEach((templateSection, index) => {
      const serverSection = serverSections[index];
      
      if (serverSection) {
        // 有对应的服务器环节，保留服务器字段
        resultSections.push({
          id: serverSection.id,
          title: serverSection.title,
          description: serverSection.description,
          processingMode: serverSection.processingMode,
          userEditable: serverSection.userEditable,
          sourceIndex: serverSection.sourceIndex,
          sourceNodeIds: serverSection.sourceNodeIds,
          pauseDuration: templateSection.pauseDuration,
          repeatCount: templateSection.repeatCount,
          repeatSpeeds: templateSection.repeatSpeeds,
          repeatPauses: templateSection.repeatPauses,
          enableTranslation: templateSection.enableTranslation,
          translationLanguage: templateSection.translationLanguage,
          translationPosition: templateSection.translationPosition,
          enableKeywords: templateSection.enableKeywords,
          keywordPosition: templateSection.keywordPosition,
          keywordRepeatCount: templateSection.keywordRepeatCount
        });
      } else {
        // 没有对应的服务器环节，创建新环节
        resultSections.push({
          id: `section_template_${Date.now()}_${index}`,
          title: `环节 ${index + 1}`,
          description: '模板生成的环节',
          processingMode: 'sequence',
          userEditable: true,
          sourceIndex: 0,
          pauseDuration: templateSection.pauseDuration,
          repeatCount: templateSection.repeatCount,
          repeatSpeeds: templateSection.repeatSpeeds,
          repeatPauses: templateSection.repeatPauses,
          enableTranslation: templateSection.enableTranslation,
          translationLanguage: templateSection.translationLanguage,
          translationPosition: templateSection.translationPosition,
          enableKeywords: templateSection.enableKeywords,
          keywordPosition: templateSection.keywordPosition,
          keywordRepeatCount: templateSection.keywordRepeatCount
        });
      }
    });

    return { sections: resultSections };
  };

  /**
   * 统一的保存逻辑（配置和模板状态同步保存）
   */
  const saveConfigAndTemplate = () => {
    const keys = storageKeys.value;
    const { type } = configState.value;

    // 保存配置
    setStorageItem(keys.settings, settings.value);

    // 根据状态类型保存对应标记
    if (currentTemplate.value) {
      setStorageItem(keys.template, currentTemplate.value.id);
      setStorageItem(keys.source, null);
    } else if (type === 'server') {
      setStorageItem(keys.source, 'server');
      setStorageItem(keys.template, null);
    } else {
      setStorageItem(keys.template, null);
      setStorageItem(keys.source, null);
    }

    console.log('配置和模板状态已保存');
  };

  /**
   * 统一的加载逻辑（配置和模板状态同步加载）
   */
  const loadConfigAndTemplate = async () => {
    const savedData = loadFromStorage();

    if (savedData.settings) {
      settings.value = savedData.settings;

      // 验证模板状态
      if (savedData.templateId) {
        console.log('loadConfigAndTemplate: 检查模板ID:', savedData.templateId);
        const template = templateStore.templateById(savedData.templateId);
        console.log('loadConfigAndTemplate: 查找模板结果:', template ? template.name : 'null');
        
        if (template && isSettingsMatchTemplate(template, savedData.settings)) {
          currentTemplate.value = template;
          console.log('loadConfigAndTemplate: 恢复模板状态:', template.name);
        } else {
          currentTemplate.value = null;
          localStorage.removeItem(storageKeys.value.template);
          console.log('loadConfigAndTemplate: 清除模板状态');
        }
      }
    } else {
      settings.value = JSON.parse(JSON.stringify(serverConfig.value));
    }
  };

  /**
   * 重置到服务器端原始配置
   */
  const resetToServerConfig = () => {
    if (!serverConfig.value?.sections) {
      console.error('无法重置：服务器配置不可用');
      return false;
    }

    // 重置状态
    settings.value = JSON.parse(JSON.stringify(serverConfig.value));
    currentTemplate.value = null;
    updateConfigState('server');
    
    // 保存状态
    const keys = storageKeys.value;
    setStorageItem(keys.settings, settings.value);
    setStorageItem(keys.source, 'server');
    setStorageItem(keys.template, null);
    
    console.log('已重置为服务器端原始配置');
    return true;
  };

  return {
    // 状态
    settings,
    currentTemplate,
    serverConfig,
    configState,
    storageKeys,

    // 方法
    saveToStorage,
    loadFromStorage,
    clearStorage,
    isSettingsMatchTemplate,
    updateTemplateState,
    updateConfigState,
    applyTemplate,
    smartLoadConfig,
    validateAndOptimizeConfig,
    applyTemplateToServerConfig,
    saveConfigAndTemplate,
    loadConfigAndTemplate,
    resetToServerConfig,
  };
}
