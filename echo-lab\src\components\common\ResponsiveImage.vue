<template>
  <div ref="containerRef" :class="['responsive-image-container', containerClass]">
    <img 
      :src="imageUrl" 
      :alt="alt"
      :loading="loading"
      @load="$emit('load', $event)"
      @error="$emit('error', $event)"
      :class="imageClass"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useResponsiveImage } from '@/composables/useResponsiveImage'

const props = defineProps({
  src: String,
  alt: String,
  loading: { type: String, default: 'lazy' },
  imageClass: String,
  containerClass: String
})

defineEmits(['load', 'error'])

const containerRef = ref(null)
const { imageUrl } = useResponsiveImage(props.src, containerRef)
</script>

<style scoped>
.responsive-image-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
}

.responsive-image-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}
</style>