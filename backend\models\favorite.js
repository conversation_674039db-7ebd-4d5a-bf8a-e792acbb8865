/**
 * 收藏模型
 * 用于存储用户收藏的内容
 */
module.exports = (sequelize, DataTypes) => {
  const Favorite = sequelize.define(
    "Favorite",
    {
      // 主键ID，使用nanoid
      id: {
        type: DataTypes.STRING(21),
        primaryKey: true,
        allowNull: false,
      },

      // 用户ID
      userId: {
        type: DataTypes.STRING(21),
        allowNull: false,
        field: "user_id",
      },

      // 内容ID
      contentId: {
        type: DataTypes.STRING(21),
        allowNull: false,
        field: "content_id",
      },
    },
    {
      // 表名
      tableName: "favorites",
      // 时间戳
      timestamps: true,
      underscored: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      // 索引
      indexes: [
        {
          unique: true,
          fields: ["user_id", "content_id"],
          name: "favorites_user_content_unique",
        },
        {
          fields: ["user_id"],
          name: "favorites_user_id_index",
        },
        {
          fields: ["content_id"],
          name: "favorites_content_id_index",
        },
      ],
    }
  );

  Favorite.associate = function (models) {
    // 与用户表关联
    Favorite.belongsTo(models.User, {
      foreignKey: "userId",
      as: "user",
    });

    // 与内容表关联
    Favorite.belongsTo(models.Content, {
      foreignKey: "contentId",
      as: "content",
    });
  };

  return Favorite;
};
