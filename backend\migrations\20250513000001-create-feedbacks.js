'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 获取当前数据库中的表
    const tables = await queryInterface.showAllTables();
    
    // 如果表已存在，则跳过创建
    if (!tables.includes('feedbacks')) {
      // 创建用户反馈表
      await queryInterface.createTable('feedbacks', {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER
        },
        user_id: {
          type: Sequelize.STRING(21),
          allowNull: true,
          comment: '用户ID（可选，未登录用户也可以提交反馈）'
        },
        type: {
          type: Sequelize.ENUM('suggestion', 'bug', 'feature', 'other'),
          allowNull: false,
          defaultValue: 'suggestion',
          comment: '反馈类型：建议、问题、功能请求、其他'
        },
        content: {
          type: Sequelize.TEXT,
          allowNull: false,
          comment: '反馈内容'
        },
        contact: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: '联系方式（邮箱或其他）'
        },
        device_info: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: '设备信息'
        },
        browser_info: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: '浏览器信息'
        },
        page_url: {
          type: Sequelize.STRING(255),
          allowNull: true,
          comment: '页面URL'
        },
        status: {
          type: Sequelize.ENUM('pending', 'processing', 'resolved', 'rejected'),
          allowNull: false,
          defaultValue: 'pending',
          comment: '状态：待处理、处理中、已解决、已拒绝'
        },
        admin_reply: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: '管理员回复'
        },
        processed_by: {
          type: Sequelize.STRING(21),
          allowNull: true,
          comment: '处理人ID'
        },
        processed_at: {
          type: Sequelize.DATE,
          allowNull: true,
          comment: '处理时间'
        },
        created_at: {
          allowNull: false,
          type: Sequelize.DATE
        },
        updated_at: {
          allowNull: false,
          type: Sequelize.DATE
        }
      });

      // 添加索引
      await queryInterface.addIndex('feedbacks', ['user_id']);
      await queryInterface.addIndex('feedbacks', ['type']);
      await queryInterface.addIndex('feedbacks', ['status']);
      await queryInterface.addIndex('feedbacks', ['created_at']);
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('feedbacks');
  }
};
