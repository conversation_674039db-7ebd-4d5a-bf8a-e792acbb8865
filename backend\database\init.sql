-- 创建数据库
CREATE DATABASE IF NOT EXISTS `echo-lab` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE `echo-lab`;

-- 创建音频表
CREATE TABLE IF NOT EXISTS `audios` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `text` text NOT NULL,
  `language` varchar(10) NOT NULL,
  `speaker` varchar(50) NOT NULL,
  `speed` float DEFAULT 1.0,
  `oss_url` varchar(255) NOT NULL,
  `oss_key` varchar(255) NOT NULL,
  `duration` float NOT NULL,
  `md5_hash` varchar(32) NOT NULL,
  `audio_source` enum('tts','manual','upload') NOT NULL DEFAULT 'tts' COMMENT '音频来源：tts-TTS生成，manual-手动切割，upload-直接上传',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_md5_hash` (`md5_hash`),
  KEY `idx_language` (`language`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_audio_source` (`audio_source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建素材内容表
CREATE TABLE IF NOT EXISTS `contents` (
  `id` varchar(21) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `config_json` json NOT NULL,
  `thumbnail_url` varchar(255) DEFAULT NULL,
  `user_id` varchar(50) DEFAULT NULL,
  `tags` varchar(255) DEFAULT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'draft',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建用户表（如果需要）
-- CREATE TABLE IF NOT EXISTS `users` (
--   `id` int(11) NOT NULL AUTO_INCREMENT,
--   `username` varchar(50) NOT NULL,
--   `password` varchar(255) NOT NULL,
--   `email` varchar(100) DEFAULT NULL,
--   `role` varchar(20) NOT NULL DEFAULT 'user',
--   `created_at` datetime NOT NULL,
--   `updated_at` datetime NOT NULL,
--   PRIMARY KEY (`id`),
--   UNIQUE KEY `idx_username` (`username`),
--   UNIQUE KEY `idx_email` (`email`)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
