/**
 * 节点工厂
 * 负责创建和管理节点类型，以及节点的创建和处理
 */

import { v4 as uuidv4 } from 'uuid';

class NodeFactory {
  constructor() {
    this.nodeTypes = {};
    this.nodeProcessors = {};
  }

  /**
   * 注册节点类型
   * @param {string} type - 节点类型
   * @param {Object} config - 节点配置
   * @param {Function} processor - 节点处理器
   */
  registerNodeType(type, config, processor) {
    this.nodeTypes[type] = {
      ...config,
      type
    };
    
    this.nodeProcessors[type] = processor;
  }

  /**
   * 获取所有节点类型
   * @returns {Object} 所有节点类型
   */
  getAllNodeTypes() {
    return this.nodeTypes;
  }

  /**
   * 获取节点类型配置
   * @param {string} type - 节点类型
   * @returns {Object|null} 节点类型配置
   */
  getNodeTypeConfig(type) {
    return this.nodeTypes[type] || null;
  }

  /**
   * 创建节点
   * @param {string} type - 节点类型
   * @param {Object} params - 节点参数
   * @param {Array} sourceIds - 源节点ID数组
   * @param {Object} position - 节点位置
   * @param {string} customName - 自定义节点名称
   * @returns {Object} 创建的节点
   */
  createNode(type, params = {}, sourceIds = [], position = { x: 0, y: 0 }, customName = '') {
    const config = this.getNodeTypeConfig(type);
    if (!config) {
      throw new Error(`未知的节点类型: ${type}`);
    }

    // 生成唯一ID
    const id = `${type}_${uuidv4()}`;

    // 合并默认参数和自定义参数
    const mergedParams = {
      ...JSON.parse(JSON.stringify(config.defaultParams || {})),
      ...params
    };

    // 创建节点
    return {
      id,
      type,
      params: mergedParams,
      sourceIds: [...sourceIds],
      position: { ...position },
      customName: customName.trim(),
      _timestamp: Date.now()
    };
  }

  /**
   * 处理节点
   * @param {Object} node - 节点
   * @param {Array} sourceResults - 源节点结果数组
   * @returns {*} 处理结果
   */
  processNode(node, sourceResults = []) {
    const processor = this.nodeProcessors[node.type];
    if (!processor) {
      throw new Error(`未找到节点处理器: ${node.type}`);
    }

    return processor(node, sourceResults);
  }

  /**
   * 检查连接是否有效
   * @param {string} sourceType - 源节点类型
   * @param {string} targetType - 目标节点类型
   * @returns {boolean} 连接是否有效
   */
  isValidConnection(sourceType, targetType) {
    const sourceConfig = this.getNodeTypeConfig(sourceType);
    const targetConfig = this.getNodeTypeConfig(targetType);

    if (!sourceConfig || !targetConfig) {
      return false;
    }

    // 检查目标节点是否接受源节点类型
    if (targetConfig.acceptSourceTypes && 
        !targetConfig.acceptSourceTypes.includes(sourceType)) {
      return false;
    }

    // 检查源节点是否接受目标节点类型
    if (sourceConfig.acceptTargetTypes && 
        !sourceConfig.acceptTargetTypes.includes(targetType)) {
      return false;
    }

    return true;
  }
}

// 创建单例实例
const nodeFactory = new NodeFactory();

export default nodeFactory;
