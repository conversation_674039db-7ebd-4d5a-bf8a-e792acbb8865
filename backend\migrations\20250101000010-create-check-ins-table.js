'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('check_ins', {
      id: {
        type: Sequelize.STRING(21),
        primaryKey: true,
        allowNull: false,
      },
      user_id: {
        type: Sequelize.STRING(21),
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      check_in_date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
        comment: '签到日期'
      },
      rewards: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '获得的奖励（预留字段）'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 创建索引
    await queryInterface.addIndex('check_ins', ['user_id']);
    await queryInterface.addIndex('check_ins', ['check_in_date']);
    await queryInterface.addIndex('check_ins', ['user_id', 'check_in_date'], {
      unique: true,
      name: 'unique_user_date'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('check_ins');
  }
};