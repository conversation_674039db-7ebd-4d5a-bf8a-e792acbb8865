/**
 * WebCodecs API 功能测试工具
 * 用于验证浏览器对WebCodecs的支持情况
 */

/**
 * 检测WebCodecs API支持
 */
export function checkWebCodecsSupport() {
  const support = {
    VideoEncoder: "VideoEncoder" in window,
    VideoFrame: "VideoFrame" in window,
    AudioEncoder: "AudioEncoder" in window,
    AudioData: "AudioData" in window,
    overall: false,
  };

  support.overall =
    support.VideoEncoder &&
    support.VideoFrame &&
    support.AudioEncoder &&
    support.AudioData;

  return support;
}

/**
 * 测试视频编码器配置
 */
export async function testVideoEncoderConfig() {
  if (!("VideoEncoder" in window)) {
    throw new Error("VideoEncoder not supported");
  }

  const configs = [
    {
      codec: "avc1.42E01E", // H.264 Baseline Level 3.0 - 640x480
      width: 640,
      height: 480,
      bitrate: 1000000,
      framerate: 30,
    },
    {
      codec: "avc1.42E01F", // H.264 Baseline Level 3.1 - 1280x720
      width: 1280,
      height: 720,
      bitrate: 2500000,
      framerate: 30,
    },
    {
      codec: "avc1.42E028", // H.264 Baseline Level 4.0 - 1920x1080
      width: 1920,
      height: 1080,
      bitrate: 5000000,
      framerate: 30,
    },
  ];

  const results = [];

  for (const config of configs) {
    try {
      const support = await VideoEncoder.isConfigSupported(config);
      results.push({
        config,
        supported: support.supported,
        supportedConfig: support.config,
      });
    } catch (error) {
      results.push({
        config,
        supported: false,
        error: error.message,
      });
    }
  }

  return results;
}

/**
 * 测试音频编码器配置
 */
export async function testAudioEncoderConfig() {
  if (!("AudioEncoder" in window)) {
    throw new Error("AudioEncoder not supported");
  }

  const configs = [
    {
      codec: "mp4a.40.2", // AAC-LC
      sampleRate: 48000,
      numberOfChannels: 2,
      bitrate: 128000,
    },
    {
      codec: "mp4a.40.2", // AAC-LC
      sampleRate: 44100,
      numberOfChannels: 1,
      bitrate: 64000,
    },
  ];

  const results = [];

  for (const config of configs) {
    try {
      const support = await AudioEncoder.isConfigSupported(config);
      results.push({
        config,
        supported: support.supported,
        supportedConfig: support.config,
      });
    } catch (error) {
      results.push({
        config,
        supported: false,
        error: error.message,
      });
    }
  }

  return results;
}

/**
 * 测试MP4Muxer库加载
 */
export async function testMP4MuxerLoad() {
  try {
    const mp4Muxer = await import("mp4-muxer");
    return {
      loaded: true,
      exports: Object.keys(mp4Muxer),
      version: mp4Muxer.default?.version || "unknown",
    };
  } catch (error) {
    return {
      loaded: false,
      error: error.message,
    };
  }
}

/**
 * 创建测试用的Canvas帧
 */
export function createTestCanvas(
  width = 1280,
  height = 720,
  text = "Test Frame"
) {
  const canvas = document.createElement("canvas");
  canvas.width = width;
  canvas.height = height;

  const ctx = canvas.getContext("2d");

  // 绘制背景
  ctx.fillStyle = "#000000";
  ctx.fillRect(0, 0, width, height);

  // 绘制文本
  ctx.fillStyle = "#FFFFFF";
  ctx.font = "48px Arial";
  ctx.textAlign = "center";
  ctx.textBaseline = "middle";
  ctx.fillText(text, width / 2, height / 2);

  return canvas;
}

/**
 * 测试VideoFrame创建
 */
export function testVideoFrameCreation() {
  if (!("VideoFrame" in window)) {
    throw new Error("VideoFrame not supported");
  }

  try {
    const canvas = createTestCanvas(640, 480, "VideoFrame Test");
    const videoFrame = new VideoFrame(canvas, {
      timestamp: 0,
      duration: 33333, // 30fps
    });

    const result = {
      success: true,
      width: videoFrame.displayWidth,
      height: videoFrame.displayHeight,
      timestamp: videoFrame.timestamp,
      duration: videoFrame.duration,
    };

    videoFrame.close();
    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * 运行完整的WebCodecs兼容性测试
 */
export async function runFullCompatibilityTest() {
  console.log("开始WebCodecs兼容性测试...");

  const results = {
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    support: {},
    videoEncoder: {},
    audioEncoder: {},
    mp4Muxer: {},
    videoFrame: {},
    overall: false,
  };

  try {
    // 基础支持检测
    results.support = checkWebCodecsSupport();
    console.log("基础支持检测:", results.support);

    if (results.support.overall) {
      // 视频编码器测试
      try {
        results.videoEncoder = await testVideoEncoderConfig();
        console.log("视频编码器测试:", results.videoEncoder);
      } catch (error) {
        results.videoEncoder = { error: error.message };
      }

      // 音频编码器测试
      try {
        results.audioEncoder = await testAudioEncoderConfig();
        console.log("音频编码器测试:", results.audioEncoder);
      } catch (error) {
        results.audioEncoder = { error: error.message };
      }

      // VideoFrame测试
      try {
        results.videoFrame = testVideoFrameCreation();
        console.log("VideoFrame测试:", results.videoFrame);
      } catch (error) {
        results.videoFrame = { error: error.message };
      }
    }

    // MP4Muxer库测试
    try {
      results.mp4Muxer = await testMP4MuxerLoad();
      console.log("MP4Muxer测试:", results.mp4Muxer);
    } catch (error) {
      results.mp4Muxer = { error: error.message };
    }

    // 综合评估
    results.overall =
      results.support.overall &&
      results.mp4Muxer.loaded &&
      results.videoFrame.success &&
      Array.isArray(results.videoEncoder) &&
      results.videoEncoder.some((r) => r.supported) &&
      Array.isArray(results.audioEncoder) &&
      results.audioEncoder.some((r) => r.supported);

    console.log(
      "WebCodecs兼容性测试完成:",
      results.overall ? "✅ 支持" : "❌ 不支持"
    );
  } catch (error) {
    console.error("兼容性测试失败:", error);
    results.error = error.message;
  }

  return results;
}

/**
 * 生成兼容性报告
 */
export function generateCompatibilityReport(results) {
  const lines = [
    "# WebCodecs 兼容性测试报告",
    "",
    `**测试时间**: ${results.timestamp}`,
    `**用户代理**: ${results.userAgent}`,
    `**总体支持**: ${results.overall ? "✅ 支持" : "❌ 不支持"}`,
    "",
    "## 基础API支持",
    `- VideoEncoder: ${results.support.VideoEncoder ? "✅" : "❌"}`,
    `- VideoFrame: ${results.support.VideoFrame ? "✅" : "❌"}`,
    `- AudioEncoder: ${results.support.AudioEncoder ? "✅" : "❌"}`,
    `- AudioData: ${results.support.AudioData ? "✅" : "❌"}`,
    "",
    "## MP4Muxer库",
    `- 加载状态: ${results.mp4Muxer.loaded ? "✅ 成功" : "❌ 失败"}`,
    results.mp4Muxer.error ? `- 错误: ${results.mp4Muxer.error}` : "",
    "",
    "## VideoFrame测试",
    `- 创建状态: ${results.videoFrame.success ? "✅ 成功" : "❌ 失败"}`,
    results.videoFrame.error ? `- 错误: ${results.videoFrame.error}` : "",
    "",
  ];

  if (Array.isArray(results.videoEncoder)) {
    lines.push("## 视频编码器支持");
    results.videoEncoder.forEach((result, index) => {
      lines.push(`### 配置 ${index + 1}`);
      lines.push(`- 编解码器: ${result.config.codec}`);
      lines.push(`- 分辨率: ${result.config.width}x${result.config.height}`);
      lines.push(`- 支持状态: ${result.supported ? "✅ 支持" : "❌ 不支持"}`);
      if (result.error) lines.push(`- 错误: ${result.error}`);
      lines.push("");
    });
  }

  return lines.filter((line) => line !== "").join("\n");
}
