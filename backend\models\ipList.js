/**
 * IP名单模型
 * 用于存储黑名单和白名单IP地址
 */
module.exports = (sequelize, DataTypes) => {
  const IpList = sequelize.define(
    "IpList",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      ip: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: "IP地址或CIDR格式",
      },
      type: {
        type: DataTypes.ENUM('blacklist', 'whitelist'),
        allowNull: false,
        comment: "IP类型：黑名单或白名单",
      },
      reason: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: "原因或备注",
      },
      expires_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: "过期时间，null表示永久",
      },
      created_by: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: "创建者",
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: "创建时间",
      },
    },
    {
      tableName: "ip_list",
      timestamps: true,
      updatedAt: false, // 不需要更新时间
      indexes: [
        {
          name: "idx_ip_list_ip_type",
          fields: ["ip", "type"],
          unique: true,
        },
        {
          name: "idx_ip_list_type",
          fields: ["type"],
        },
        {
          name: "idx_ip_list_expires_at",
          fields: ["expires_at"],
        },
      ],
    }
  );

  return IpList;
};
