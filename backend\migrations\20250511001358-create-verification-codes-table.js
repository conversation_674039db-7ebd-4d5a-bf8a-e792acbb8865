"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("verification_codes", {
      id: {
        type: Sequelize.STRING(21),
        primaryKey: true,
        allowNull: false,
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      code: {
        type: Sequelize.STRING(6),
        allowNull: false,
      },
      type: {
        type: Sequelize.ENUM("login", "register", "reset_password"),
        defaultValue: "login",
      },
      expires_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      used: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // 添加索引
    await queryInterface.addIndex("verification_codes", ["email"]);
    await queryInterface.addIndex("verification_codes", ["expires_at"]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("verification_codes");
  },
};
