/**
 * 重新创建数据库表结构
 * 警告：这将删除所有现有数据！
 */
const db = require('../models');

async function recreateTables() {
  try {
    console.log('开始重新创建数据库表...');
    
    // 强制同步模型到数据库
    // 这会删除现有的表并重新创建
    await db.sequelize.sync({ force: true });
    
    console.log('数据库表重新创建完成！');
    
    return true;
  } catch (error) {
    console.error('重新创建数据库表失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本，则执行重新创建
if (require.main === module) {
  recreateTables()
    .then(() => {
      console.log('重新创建成功，退出程序');
      process.exit(0);
    })
    .catch(err => {
      console.error('重新创建失败:', err);
      process.exit(1);
    });
}

module.exports = recreateTables;
