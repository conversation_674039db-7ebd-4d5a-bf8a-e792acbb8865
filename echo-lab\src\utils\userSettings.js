/**
 * 统一的用户设置管理
 * 整合语言、等级、引导状态等所有用户相关设置
 */

// 统一的存储键名
const STORAGE_KEY = 'echolab_user_settings';

// 设置版本（用于数据迁移）
const SETTINGS_VERSION = '1.0.0';

// 默认设置
const DEFAULT_SETTINGS = {
  version: SETTINGS_VERSION,
  language: {
    learning: 'ja', // 学习语言
    interface: 'zh-CN' // 界面语言（预留）
  },
  level: {
    current: null, // 当前等级
    setAt: null // 设置时间
  },
  onboarding: {
    completed: false,
    completedAt: null,
    currentStep: 1
  },
  preferences: {
    // 其他用户偏好设置
    theme: 'light',
    autoPlay: true,
    showSubtitles: true
  },
  updatedAt: null
};

/**
 * 获取完整的用户设置
 * @returns {Object} 用户设置对象
 */
export function getUserSettings() {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) {
      return { ...DEFAULT_SETTINGS };
    }

    const settings = JSON.parse(stored);
    
    // 检查版本并进行数据迁移
    if (!settings.version || settings.version !== SETTINGS_VERSION) {
      return migrateSettings(settings);
    }

    // 合并默认设置，确保新增字段有默认值
    return mergeWithDefaults(settings);
  } catch (error) {
    console.error('获取用户设置失败:', error);
    return { ...DEFAULT_SETTINGS };
  }
}

/**
 * 保存用户设置
 * @param {Object} updates 要更新的设置
 * @returns {boolean} 是否保存成功
 */
export function saveUserSettings(updates) {
  try {
    const current = getUserSettings();
    const updated = deepMerge(current, updates);
    updated.updatedAt = new Date().toISOString();

    localStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
    console.log('用户设置已保存:', updates);
    return true;
  } catch (error) {
    console.error('保存用户设置失败:', error);
    return false;
  }
}

/**
 * 获取学习语言
 * @returns {string} 语言代码
 */
export function getLearningLanguage() {
  const settings = getUserSettings();
  return settings.language.learning;
}

/**
 * 设置学习语言
 * @param {string} languageCode 语言代码
 * @returns {boolean} 是否设置成功
 */
export function setLearningLanguage(languageCode) {
  return saveUserSettings({
    language: {
      learning: languageCode
    }
  });
}

/**
 * 获取用户等级
 * @returns {string|null} 等级代码
 */
export function getUserLevel() {
  const settings = getUserSettings();
  return settings.level.current;
}

/**
 * 设置用户等级
 * @param {string} level 等级代码
 * @returns {boolean} 是否设置成功
 */
export function setUserLevel(level) {
  return saveUserSettings({
    level: {
      current: level,
      setAt: new Date().toISOString()
    }
  });
}

/**
 * 获取引导状态
 * @returns {Object} 引导状态
 */
export function getOnboardingStatus() {
  const settings = getUserSettings();
  return settings.onboarding;
}

/**
 * 设置引导完成
 * @param {boolean} completed 是否完成
 * @returns {boolean} 是否设置成功
 */
export function setOnboardingCompleted(completed = true) {
  return saveUserSettings({
    onboarding: {
      completed,
      completedAt: completed ? new Date().toISOString() : null
    }
  });
}

/**
 * 重置引导状态
 * @returns {boolean} 是否重置成功
 */
export function resetOnboarding() {
  return saveUserSettings({
    onboarding: {
      completed: false,
      completedAt: null,
      currentStep: 1
    }
  });
}

/**
 * 获取用户偏好设置
 * @returns {Object} 偏好设置
 */
export function getUserPreferences() {
  const settings = getUserSettings();
  return settings.preferences;
}

/**
 * 设置用户偏好
 * @param {Object} preferences 偏好设置
 * @returns {boolean} 是否设置成功
 */
export function setUserPreferences(preferences) {
  return saveUserSettings({
    preferences
  });
}

/**
 * 清除所有用户设置
 * @returns {boolean} 是否清除成功
 */
export function clearUserSettings() {
  try {
    localStorage.removeItem(STORAGE_KEY);
    console.log('用户设置已清除');
    return true;
  } catch (error) {
    console.error('清除用户设置失败:', error);
    return false;
  }
}

/**
 * 导出用户设置（用于备份）
 * @returns {string} JSON字符串
 */
export function exportUserSettings() {
  const settings = getUserSettings();
  return JSON.stringify(settings, null, 2);
}

/**
 * 导入用户设置（用于恢复）
 * @param {string} settingsJson JSON字符串
 * @returns {boolean} 是否导入成功
 */
export function importUserSettings(settingsJson) {
  try {
    const settings = JSON.parse(settingsJson);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
    console.log('用户设置已导入');
    return true;
  } catch (error) {
    console.error('导入用户设置失败:', error);
    return false;
  }
}

// ========== 内部辅助函数 ==========

/**
 * 深度合并对象
 */
function deepMerge(target, source) {
  const result = { ...target };
  
  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = deepMerge(result[key] || {}, source[key]);
    } else {
      result[key] = source[key];
    }
  }
  
  return result;
}

/**
 * 与默认设置合并
 */
function mergeWithDefaults(settings) {
  return deepMerge(DEFAULT_SETTINGS, settings);
}

/**
 * 数据迁移（从旧版本迁移到新版本）
 */
function migrateSettings(oldSettings) {
  console.log('开始迁移用户设置...');
  
  const newSettings = { ...DEFAULT_SETTINGS };
  
  try {
    // 迁移旧的localStorage数据
    const oldLanguage = localStorage.getItem('echolab_learning_language');
    const oldOnboardingCompleted = localStorage.getItem('echolab_onboarding_completed');
    const oldOnboardingCompletedAt = localStorage.getItem('echolab_onboarding_completed_at');
    const oldUserPreferences = localStorage.getItem('user_preferences');
    
    // 迁移语言设置
    if (oldLanguage) {
      newSettings.language.learning = oldLanguage;
    }
    
    // 迁移引导状态
    if (oldOnboardingCompleted === 'true') {
      newSettings.onboarding.completed = true;
      newSettings.onboarding.completedAt = oldOnboardingCompletedAt || new Date().toISOString();
    }
    
    // 迁移用户偏好和等级
    if (oldUserPreferences) {
      const preferences = JSON.parse(oldUserPreferences);
      if (preferences.level) {
        newSettings.level.current = preferences.level;
        newSettings.level.setAt = new Date().toISOString();
      }
      // 保留其他偏好设置
      newSettings.preferences = { ...newSettings.preferences, ...preferences };
    }
    
    // 保存迁移后的设置
    localStorage.setItem(STORAGE_KEY, JSON.stringify(newSettings));
    
    // 清理旧的localStorage项
    localStorage.removeItem('echolab_learning_language');
    localStorage.removeItem('echolab_onboarding_completed');
    localStorage.removeItem('echolab_onboarding_completed_at');
    localStorage.removeItem('user_preferences');
    
    console.log('用户设置迁移完成');
    return newSettings;
  } catch (error) {
    console.error('用户设置迁移失败:', error);
    return newSettings;
  }
}

export default {
  getUserSettings,
  saveUserSettings,
  getLearningLanguage,
  setLearningLanguage,
  getUserLevel,
  setUserLevel,
  getOnboardingStatus,
  setOnboardingCompleted,
  resetOnboarding,
  getUserPreferences,
  setUserPreferences,
  clearUserSettings,
  exportUserSettings,
  importUserSettings
};
