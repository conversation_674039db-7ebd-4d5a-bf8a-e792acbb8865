/**
 * 管理员路由
 * 处理管理员相关的请求
 */
const express = require("express");
const router = express.Router();
const adminController = require("../controllers/adminController");
const { adminAuth, logAdminAction } = require("../middleware/adminMiddleware");
const adminRoutes = require("./admin/index");

/**
 * 获取系统概览数据
 * GET /api/admin/dashboard
 */
router.get("/dashboard", adminAuth, adminController.getDashboardStats);

/**
 * 获取用户列表
 * GET /api/admin/users
 */
router.get("/users", adminAuth, adminController.getUsers);

/**
 * 获取用户详情
 * GET /api/admin/users/:id
 */
router.get("/users/:id", adminAuth, adminController.getUserDetail);

/**
 * 更新用户状态
 * PUT /api/admin/users/:id/status
 */
router.put(
  "/users/:id/status",
  adminAuth,
  logAdminAction("update_user_status", {
    targetType: "user",
    targetIdParam: "id",
    getBeforeData: async (req) => {
      const user = await req.app.get("db").User.findByPk(req.params.id);
      return user ? { status: user.status } : null;
    },
    getAfterData: (req) => ({ status: req.body.status }),
    getDetails: (req) => ({ status: req.body.status }),
  }),
  adminController.updateUserStatus
);

/**
 * 更新用户角色
 * PUT /api/admin/users/:id/role
 */
router.put(
  "/users/:id/role",
  adminAuth,
  logAdminAction("update_user_role", {
    targetType: "user",
    targetIdParam: "id",
    getBeforeData: async (req) => {
      const user = await req.app.get("db").User.findByPk(req.params.id);
      return user ? { role: user.role } : null;
    },
    getAfterData: (req) => ({ role: req.body.role }),
    getDetails: (req) => ({ role: req.body.role }),
  }),
  adminController.updateUserRole
);

/**
 * 获取管理员操作日志
 * GET /api/admin/logs
 */
router.get("/logs", adminAuth, adminController.getAdminLogs);

// 注册管理员子路由
router.use("/", adminRoutes);

module.exports = router;
