# 全局资源管理系统

## 1. 概述

全局资源管理系统用于管理编辑页面中的标注、翻译和音频资源。这些资源不再作为节点链中的一部分，而是作为全局数据存在，可以被需要它们的地方访问。最终，这些资源会与节点配置一起导出为完整的JSON，供播放系统使用。

### 1.1 数据源

全局资源管理系统的数据来源于以下几个方面：

1. **节点系统**：
   - 文本节点提供原始文本内容
   - 分句节点提供分段后的文本内容
   - 这些内容作为全局资源的基础数据

2. **API调用**：
   - 标注API：将原文转换为带标注的内容
   - 翻译API：将原文翻译为目标语言
   - TTS API：将文本转换为音频

3. **用户输入**：
   - 用户可以直接输入带标注的文本
   - 用户可以手动添加或修改替换项
   - 用户可以修改自动生成的标注或翻译

4. **本地存储**：
   - 替换库保存在本地存储中
   - 用户的偏好设置也可能保存在本地存储中

全局资源管理系统将这些来源的数据整合起来，形成一个统一的资源池，供编辑页面和播放系统使用。

## 2. 核心功能

### 2.1 标注管理

- **功能**：调用接口将原文转换为带标注的内容
- **目的一**：生成视频内容时，将标注显示在汉字上方
- **目的二**：解决某些单词发音不准的问题，通过指定使用假名替代原文
- **处理流程**：
  1. 接收原文文本
  2. 调用标注API将原文转换为带标注的内容（如：友達（ともだち））
  3. 解析带标注的文本，提取出原文和对应的假名
  4. 存储标注数据，供显示和音频生成使用

### 2.2 替换管理

- **功能**：管理哪些标注项需要在音频生成时替换原文
- **目的**：解决发音不准的问题，通过使用假名替代原文
- **处理流程**：
  1. 维护替换库，存储需要替换的项
  2. 支持从标注中添加或手动添加替换项
  3. 根据替换库生成用于音频生成的文本
  4. 只替换替换库中指定的项，其他标注保持原文

### 2.3 音频生成

- **功能**：将文本转换为音频
- **特点**：音频生成不关心文本来源，只接收文本并生成音频
- **处理流程**：
  1. 接收文本（可能是原文，也可能是经过替换处理的文本）
  2. 调用TTS API生成音频
  3. 存储音频URL和元数据

### 2.4 翻译管理

- **功能**：存储和管理文本的翻译
- **特点**：支持多个目标语言，一个原文可以有多种语言的翻译
- **处理流程**：
  1. 接收原文和目标语言（可以是多个）
  2. 调用翻译API生成各种语言的翻译
  3. 存储原文、目标语言和翻译的对应关系

## 3. 数据结构

### 3.1 标注数据

```javascript
{
  "id": "annotation_1",
  "originalText": "友達（ともだち）と一緒（いっしょ）に旅行（りょこう）する",
  "language": "ja",
  "annotations": [
    {
      "original": "友達",
      "reading": "ともだち",
      "startPos": 0,
      "endPos": 2
    },
    // 更多标注项...
  ]
}
```

### 3.2 替换配置

```javascript
{
  "友達": {
    "original": "友達",
    "reading": "ともだち",
    "addedAt": 1621234567890
  },
  // 更多替换项...
}
```

### 3.3 音频数据

```javascript
{
  "id": "audio_1",
  "originalTextId": "text_1",
  "originalText": "友達（ともだち）と一緒（いっしょ）に旅行（りょこう）する",
  "processedText": "ともだちと一緒に旅行する",
  "replacementId": "replacement_1",
  "language": "ja",
  "url": "https://example.com/audio/audio_1.mp3",
  "duration": 3000,
  "createdAt": 1621234567890
}
```

### 3.4 翻译数据

```javascript
{
  "id": "translation_1",
  "originalText": {
    "seg_959bb330-95d3-4fcb-b954-760f4b1dd104": "今日はいい天気ですね。",
    "seg_cca294a2-350f-4e1c-92d7-3e0a68174652": "日本語を勉強するのは楽しいです。",
    "seg_04e1dd34-07b4-4a64-af61-8b4496aeff65": "一緒に頑張りましょう。"
  },
  "sourceLanguage": "ja",
  "translations": {
    "zh-TW": {
      "seg_959bb330-95d3-4fcb-b954-760f4b1dd104": "今天天氣很好。",
      "seg_cca294a2-350f-4e1c-92d7-3e0a68174652": "學習日語很有趣。",
      "seg_04e1dd34-07b4-4a64-af61-8b4496aeff65": "我們一起努力吧。"
    },
    "en": {
      "seg_959bb330-95d3-4fcb-b954-760f4b1dd104": "It's nice weather today.",
      "seg_cca294a2-350f-4e1c-92d7-3e0a68174652": "Studying Japanese is fun.",
      "seg_04e1dd34-07b4-4a64-af61-8b4496aeff65": "Let's work hard together."
    }
  },
  "createdAt": 1621234567890
}
```

## 4. 系统设计

### 4.1 资源生成流程

全局资源的生成遵循以下流程：

1. **文本获取**：
   - 从文本节点或分句节点获取原始文本
   - 或者用户直接输入带标注的文本

2. **标注处理**：
   - 调用标注API将原文转换为带标注的内容
   - 或者解析用户输入的带标注文本
   - 存储标注资源

3. **替换处理**：
   - 根据替换库和标注资源生成处理后的文本
   - 只替换替换库中指定的项，其他标注保持原文
   - 移除所有标注括号和假名

4. **音频生成**：
   - 使用处理后的文本调用TTS API生成音频
   - 存储音频资源，包括原始文本、处理后文本和音频URL

5. **翻译生成**：
   - 调用翻译API将原文翻译为目标语言
   - 存储翻译资源，按段落和目标语言组织

6. **资源关联**：
   - 建立资源之间的关联关系
   - 确保能够从原始文本找到对应的标注、音频和翻译资源

这个流程确保了资源的生成和管理是有序的，同时保持了资源之间的关联关系。

### 4.2 资源与节点的关系

- **独立性**：资源系统独立于节点系统
- **节点系统**：节点只处理自己的逻辑和数据流，节点之间通过连接传递数据
- **资源系统**：资源作为全局数据存在，可以被任何需要它们的地方访问
- **不直接引用**：节点不直接引用资源，资源也不直接引用节点

### 4.3 导出与播放

- **导出JSON**：导出时，将节点配置和全局资源合并为一个完整的JSON
- **JSON结构**：包含两部分：节点配置和资源数据
- **资源选择**：根据节点配置，只导出被引用的资源，避免冗余
- **播放系统**：使用完整JSON进行解析和播放
- **资源使用**：在播放时，根据需要从资源部分获取相应的资源

#### 导出流程

1. **收集节点配置**：
   - 获取所有节点的配置信息
   - 分析节点之间的连接关系

2. **收集资源引用**：
   - 分析节点配置，找出所有被引用的资源ID
   - 这可能包括文本、标注、音频和翻译资源

3. **筛选资源**：
   - 只导出被引用的资源，避免冗余
   - 确保所有引用都有对应的资源

4. **合并数据**：
   - 将节点配置和筛选后的资源合并为一个完整的JSON
   - 确保资源之间的关联关系正确

5. **导出JSON**：
   - 将完整的JSON导出为文件或发送到服务器
   - 这个JSON可以被播放系统直接使用

### 4.4 完整JSON示例

```javascript
{
  "version": "1.0",
  "nodes": [
    // 节点配置...
  ],
  "resources": {
    "annotations": {
      "annotation_1": {
        "originalText": "友達（ともだち）と一緒（いっしょ）に旅行（りょこう）する",
        "annotations": [
          {
            "original": "友達",
            "reading": "ともだち"
          },
          // 更多标注项...
        ]
      }
    },
    "audios": {
      "audio_1": {
        "originalTextId": "text_1",
        "originalText": "友達（ともだち）と一緒（いっしょ）に旅行（りょこう）する",
        "processedText": "ともだちと一緒に旅行する",
        "replacementId": "replacement_1",
        "url": "https://example.com/audio/audio_1.mp3",
        "duration": 3000
      }
    },
    "translations": {
      "translation_1": {
        "sourceLanguage": "ja",
        "originalText": {
          "seg_1": "友達（ともだち）と一緒（いっしょ）に",
          "seg_2": "旅行（りょこう）する"
        },
        "translations": {
          "zh-CN": {
            "seg_1": "和朋友一起",
            "seg_2": "旅行"
          },
          "en": {
            "seg_1": "With friends",
            "seg_2": "travel"
          },
          "zh-TW": {
            "seg_1": "和朋友一起",
            "seg_2": "旅行"
          }
        }
      }
    }
  }
}
```

## 5. 实现考虑

### 5.1 翻译资源设计

- **分段翻译**：翻译按段落/句子ID进行组织，而不是简单地翻译整个文本
- **数据结构**：
  - `originalText`：原文的段落映射，键为段落ID，值为原文内容
  - `translations`：翻译内容，按目标语言和段落ID组织
- **优势**：
  - 细粒度控制：可以对每个段落单独进行翻译和管理
  - 部分更新：可以只更新特定段落的翻译
  - 一致性：确保相同段落在不同语言中的对应关系

### 5.2 标注处理

- **标注格式**：支持全角和半角括号，如 `友達（ともだち）` 和 `友達(ともだち)`
- **标注解析**：使用正则表达式解析标注格式
- **标注显示**：提供将标注转换为显示格式的功能，将假名显示在汉字上方

### 5.3 替换处理

- **全匹配替换**：只替换完全匹配的项，不进行部分匹配
- **本地存储**：将替换库保存在本地存储，方便下次使用
- **手动添加**：支持手动添加替换项，即使原文中没有对应的标注

### 5.4 音频生成

- **文本处理分离**：文本处理（包括替换）和音频生成是分离的
- **音频缓存**：使用处理后的文本（替换后的文本）作为缓存键，而不是原始文本。这确保了不同替换方式生成的不同音频有各自的缓存。
- **服务选择**：根据语言自动选择不同的TTS服务

### 5.5 资源管理

- **唯一标识**：使用内容哈希作为资源的唯一标识符
- **资源缓存**：缓存资源，避免重复生成
- **资源导出**：支持将资源导出为JSON，与节点配置一起导出

### 5.6 资源引用与解析

- **完整引用链**：JSON中存储完整的引用链，确保能够从原始文本找到对应的音频
- **资源ID引用**：节点通过ID引用文本、音频和替换配置资源
- **一致性验证**：解析时验证资源之间的一致性，确保引用关系正确
- **解析流程**：
  1. 加载所有资源，建立ID到资源的映射
  2. 解析节点，获取引用的资源ID
  3. 通过ID查找对应的资源
  4. 验证资源之间的一致性
  5. 使用资源进行播放或显示

#### 音频资源的查找与匹配

- **双向映射**：音频资源同时存储原始文本和处理后的文本，建立双向映射关系
- **查找流程**：
  1. 首先尝试通过原始文本和替换配置找到对应的音频资源
  2. 如果找不到，则生成处理后的文本，并用它查找音频资源
- **索引优化**：建立原始文本索引和处理后文本索引，提高查找效率

## 6. 总结

全局资源管理系统将标注、翻译和音频生成功能从节点链中分离出来，作为全局数据存在。这种设计简化了节点图，提高了资源复用率，并提供了更直观的工作流。最终，这些资源会与节点配置一起导出为完整的JSON，供播放系统使用。