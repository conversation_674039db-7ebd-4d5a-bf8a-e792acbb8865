# 音频采样率处理方案

本文档说明了Echo Lab前端视频导出功能中音频采样率的处理方案，解决WebCodecs AudioEncoder采样率限制问题。

## 🎯 问题背景

### 项目音频配置
Echo Lab项目使用24kHz采样率进行音频处理：
```javascript
const VOICE_SAMPLE_RATE = 24000; // 24kHz for voice audio
```

### WebCodecs限制
WebCodecs AudioEncoder只支持两种采样率：
- **44.1kHz** (44100Hz) - CD音质标准
- **48kHz** (48000Hz) - 数字音频标准

### 错误示例
```
NotSupportedError: Unsupported sample rate. Supported values: 44100, 48000
```

## 🔧 解决方案

### 1. 智能采样率转换

```javascript
// 配置音频编码器 - 确保使用支持的采样率
let targetSampleRate = audioBuffer.sampleRate;

// WebCodecs AudioEncoder只支持44100和48000Hz
if (targetSampleRate !== 44100 && targetSampleRate !== 48000) {
  // 如果是24kHz，升采样到48kHz（2倍关系，质量更好）
  if (targetSampleRate === 24000) {
    targetSampleRate = 48000;
  } else {
    // 其他采样率默认使用44.1kHz
    targetSampleRate = 44100;
  }
  console.log(`音频采样率从 ${audioBuffer.sampleRate}Hz 转换为 ${targetSampleRate}Hz`);
}
```

### 2. 高质量重采样

```javascript
async _resampleAudio(audioBuffer, targetSampleRate) {
  const numChannels = audioBuffer.numberOfChannels;
  const duration = audioBuffer.duration;
  const newLength = Math.round(duration * targetSampleRate);

  // 创建离线音频上下文进行重采样
  const offlineContext = new OfflineAudioContext(
    numChannels,
    newLength,
    targetSampleRate
  );

  // 创建音频源
  const bufferSource = offlineContext.createBufferSource();
  bufferSource.buffer = audioBuffer;
  bufferSource.connect(offlineContext.destination);
  bufferSource.start();

  // 执行重采样
  return await offlineContext.startRendering();
}
```

### 3. 自动检测和处理

```javascript
// 检查是否需要重采样
const needsResampling = audioBuffer.sampleRate !== audioConfig.sampleRate;
let processedAudioBuffer = audioBuffer;

if (needsResampling) {
  console.log(`重采样音频: ${audioBuffer.sampleRate}Hz -> ${audioConfig.sampleRate}Hz`);
  processedAudioBuffer = await this._resampleAudio(audioBuffer, audioConfig.sampleRate);
}
```

## 📊 采样率对应表

| 原始采样率 | 目标采样率 | 转换比例 | 质量影响 | 文件大小变化 |
|-----------|-----------|----------|----------|-------------|
| 24kHz | 48kHz | 2:1 | 🟢 优秀 | +100% |
| 22.05kHz | 44.1kHz | 2:1 | 🟢 优秀 | +100% |
| 16kHz | 48kHz | 3:1 | 🟡 良好 | +200% |
| 8kHz | 44.1kHz | 5.5:1 | 🟡 良好 | +450% |
| 其他 | 44.1kHz | 变化 | 🟡 良好 | 变化 |

## 🎵 音质分析

### 24kHz → 48kHz 转换优势

1. **完美的2倍关系**
   - 数学上的整数倍关系
   - 避免复杂的插值计算
   - 保持原始音频的频率特性

2. **频率响应保持**
   - 24kHz原始：0-12kHz频率范围
   - 48kHz目标：0-24kHz频率范围
   - 原始频率完全保留

3. **数字音频标准**
   - 48kHz是专业数字音频标准
   - 广泛的硬件和软件支持
   - 更好的编码器兼容性

### 质量对比

```javascript
// 原始24kHz音频特性
const original = {
  sampleRate: 24000,
  nyquistFreq: 12000,  // 最高可表示频率
  fileSize: '1x',
  quality: '语音优化'
};

// 升采样到48kHz后
const upsampled = {
  sampleRate: 48000,
  nyquistFreq: 24000,  // 最高可表示频率
  fileSize: '2x',      // 文件大小翻倍
  quality: '高保真'     // 质量提升
};
```

## 🧪 测试验证

### 兼容性测试

```javascript
// 测试不同采样率的AudioEncoder支持
const testSampleRates = [8000, 16000, 22050, 24000, 44100, 48000];

for (const sampleRate of testSampleRates) {
  try {
    const support = await AudioEncoder.isConfigSupported({
      codec: 'mp4a.40.2',
      sampleRate: sampleRate,
      numberOfChannels: 1,
      bitrate: 128000
    });
    console.log(`${sampleRate}Hz:`, support.supported ? '✅' : '❌');
  } catch (error) {
    console.log(`${sampleRate}Hz: ❌ ${error.message}`);
  }
}
```

### 重采样质量测试

```javascript
// 测试重采样质量
async function testResamplingQuality() {
  // 创建24kHz测试音频
  const originalBuffer = createTestAudio(24000, 5); // 5秒
  
  // 重采样到48kHz
  const resampledBuffer = await resampleAudio(originalBuffer, 48000);
  
  // 比较质量指标
  const metrics = {
    originalDuration: originalBuffer.duration,
    resampledDuration: resampledBuffer.duration,
    durationDiff: Math.abs(originalBuffer.duration - resampledBuffer.duration),
    sizeRatio: resampledBuffer.length / originalBuffer.length
  };
  
  console.log('重采样质量指标:', metrics);
}
```

## 🔧 配置优化

### 编码器配置

```javascript
const audioConfig = {
  codec: 'mp4a.40.2',        // AAC-LC编码
  sampleRate: 48000,         // 使用48kHz标准采样率
  numberOfChannels: 1,       // 单声道（语音优化）
  bitrate: 128000           // 128kbps码率
};
```

### 性能优化

```javascript
// 缓存重采样结果
const resampleCache = new Map();

async function cachedResample(audioBuffer, targetSampleRate) {
  const cacheKey = `${audioBuffer.sampleRate}->${targetSampleRate}`;
  
  if (resampleCache.has(cacheKey)) {
    return resampleCache.get(cacheKey);
  }
  
  const result = await resampleAudio(audioBuffer, targetSampleRate);
  resampleCache.set(cacheKey, result);
  
  return result;
}
```

## 📈 性能影响

### 处理时间

| 音频时长 | 24kHz→48kHz | 内存占用 | 处理时间 |
|---------|-------------|----------|----------|
| 30秒 | ~1秒 | +50MB | 可接受 |
| 1分钟 | ~2秒 | +100MB | 良好 |
| 5分钟 | ~10秒 | +500MB | 需要优化 |

### 内存管理

```javascript
// 及时释放原始AudioBuffer
async function processAudioWithCleanup(audioBuffer, targetSampleRate) {
  const resampled = await resampleAudio(audioBuffer, targetSampleRate);
  
  // 清理原始buffer（如果可能）
  if (audioBuffer !== resampled) {
    // 原始buffer可以被垃圾回收
    audioBuffer = null;
  }
  
  return resampled;
}
```

## 🎯 最佳实践

### 1. 预处理策略
- 在视频导出开始时进行重采样
- 缓存重采样结果避免重复计算
- 监控内存使用情况

### 2. 质量保证
- 优先选择48kHz作为目标采样率
- 保持原始音频的时长精度
- 验证重采样后的音频质量

### 3. 错误处理
- 检测不支持的采样率
- 提供降级方案
- 记录转换过程的日志

### 4. 用户体验
- 显示重采样进度
- 说明质量提升效果
- 提供采样率选择选项

## 🔍 故障排除

### 常见问题

1. **重采样失败**
   ```
   错误: Failed to create OfflineAudioContext
   解决: 检查浏览器支持和内存限制
   ```

2. **音频时长不匹配**
   ```
   症状: 重采样后时长略有差异
   解决: 使用精确的时长计算
   ```

3. **内存不足**
   ```
   症状: 长音频重采样时浏览器卡顿
   解决: 分段处理或降低质量
   ```

### 调试命令

```javascript
// 检查音频信息
function analyzeAudioBuffer(buffer) {
  console.log('音频分析:', {
    sampleRate: buffer.sampleRate,
    channels: buffer.numberOfChannels,
    duration: buffer.duration,
    length: buffer.length,
    size: `${(buffer.length * buffer.numberOfChannels * 4 / 1024 / 1024).toFixed(2)}MB`
  });
}

// 测试重采样
webCodecsQuickTest.runQuickTest();
```

## 📚 参考资料

- [Web Audio API - OfflineAudioContext](https://developer.mozilla.org/en-US/docs/Web/API/OfflineAudioContext)
- [WebCodecs AudioEncoder](https://developer.mozilla.org/en-US/docs/Web/API/AudioEncoder)
- [数字音频采样率标准](https://en.wikipedia.org/wiki/Sampling_rate)
