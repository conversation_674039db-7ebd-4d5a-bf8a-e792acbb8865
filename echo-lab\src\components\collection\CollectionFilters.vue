<!--
  合集筛选组件
  提供高级筛选功能
-->
<template>
  <div class="collection-filters">
    <!-- 搜索栏 -->
    <div class="search-section">
      <el-input 
        v-model="localFilters.search" 
        placeholder="搜索合集名称、描述或标签..." 
        clearable
        size="large"
        class="search-input"
        @input="handleSearchChange">
        <template #prefix>
          <el-icon><i-ep-search /></el-icon>
        </template>
      </el-input>
    </div>

    <!-- 快速筛选标签 -->
    <div class="quick-filters">
      <el-tag 
        v-for="tag in popularTags" 
        :key="tag"
        :type="localFilters.selectedTags.includes(tag) ? 'primary' : 'info'"
        :effect="localFilters.selectedTags.includes(tag) ? 'dark' : 'plain'"
        class="filter-tag"
        @click="toggleTag(tag)">
        {{ tag }}
      </el-tag>
    </div>

    <!-- 高级筛选 -->
    <div class="advanced-filters" v-if="showAdvanced">
      <div class="filter-row">
        <div class="filter-item">
          <label>状态</label>
          <el-select v-model="localFilters.status" @change="emitChange">
            <el-option label="全部" value="all" />
            <el-option label="已发布" value="published" />
            <el-option label="草稿" value="draft" />
          </el-select>
        </div>

        <div class="filter-item">
          <label>可见性</label>
          <el-select v-model="localFilters.visibility" @change="emitChange">
            <el-option label="全部" value="all" />
            <el-option label="公开" value="public" />
            <el-option label="私有" value="private" />
          </el-select>
        </div>

        <div class="filter-item">
          <label>排序方式</label>
          <el-select v-model="localFilters.sortBy" @change="emitChange">
            <el-option label="更新时间" value="updated_at" />
            <el-option label="创建时间" value="created_at" />
            <el-option label="名称" value="name" />
            <el-option label="浏览量" value="view_count" />
            <el-option label="收藏量" value="favorite_count" />
          </el-select>
        </div>

        <div class="filter-item">
          <label>排序顺序</label>
          <el-select v-model="localFilters.sortOrder" @change="emitChange">
            <el-option label="降序" value="DESC" />
            <el-option label="升序" value="ASC" />
          </el-select>
        </div>
      </div>

      <div class="filter-row">
        <div class="filter-item">
          <label>内容数量</label>
          <el-slider
            v-model="localFilters.itemCountRange"
            range
            :min="0"
            :max="50"
            :step="1"
            @change="emitChange"
            class="count-slider" />
          <span class="range-text">
            {{ localFilters.itemCountRange[0] }} - {{ localFilters.itemCountRange[1] }} 个内容
          </span>
        </div>
      </div>
    </div>

    <!-- 筛选操作 -->
    <div class="filter-actions">
      <el-button @click="toggleAdvanced" link>
        {{ showAdvanced ? '收起高级筛选' : '展开高级筛选' }}
        <el-icon>
          <i-ep-arrow-down v-if="!showAdvanced" />
          <i-ep-arrow-up v-else />
        </el-icon>
      </el-button>
      
      <el-button @click="resetFilters" link>
        重置筛选
      </el-button>
      
      <el-button @click="saveFilters" link v-if="showSaveOption">
        保存筛选
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';

// Props
const props = defineProps({
  filters: {
    type: Object,
    default: () => ({})
  },
  showSaveOption: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['filters-change', 'filters-save']);

// 响应式数据
const showAdvanced = ref(false);
const localFilters = reactive({
  search: '',
  selectedTags: [],
  status: 'all',
  visibility: 'all',
  sortBy: 'updated_at',
  sortOrder: 'DESC',
  itemCountRange: [0, 50],
  ...props.filters
});

// 热门标签
const popularTags = ref([
  '日语学习', '听力练习', '初级', '中级', '高级', 
  '商务日语', '旅游日语', '考试准备', '日常对话', '文化学习'
]);

// 切换标签选择
const toggleTag = (tag) => {
  const index = localFilters.selectedTags.indexOf(tag);
  if (index > -1) {
    localFilters.selectedTags.splice(index, 1);
  } else {
    localFilters.selectedTags.push(tag);
  }
  emitChange();
};

// 处理搜索变化
const handleSearchChange = () => {
  emitChange();
};

// 切换高级筛选
const toggleAdvanced = () => {
  showAdvanced.value = !showAdvanced.value;
};

// 重置筛选
const resetFilters = () => {
  Object.assign(localFilters, {
    search: '',
    selectedTags: [],
    status: 'all',
    visibility: 'all',
    sortBy: 'updated_at',
    sortOrder: 'DESC',
    itemCountRange: [0, 50]
  });
  emitChange();
};

// 保存筛选
const saveFilters = () => {
  emit('filters-save', { ...localFilters });
};

// 发出变化事件
const emitChange = () => {
  emit('filters-change', { ...localFilters });
};

// 监听外部筛选变化
watch(() => props.filters, (newFilters) => {
  Object.assign(localFilters, newFilters);
}, { deep: true });
</script>

<style scoped>
.collection-filters {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.search-section {
  margin-bottom: 1rem;
}

.search-input {
  width: 100%;
}

.quick-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.filter-tag {
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-tag:hover {
  transform: scale(1.05);
}

.advanced-filters {
  border-top: 1px solid #f0f0f0;
  padding-top: 1rem;
  margin-bottom: 1rem;
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #606266;
}

.count-slider {
  margin: 0.5rem 0;
}

.range-text {
  font-size: 0.75rem;
  color: #909399;
  text-align: center;
}

.filter-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #f0f0f0;
  padding-top: 1rem;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .filter-row {
    grid-template-columns: 1fr;
  }
  
  .filter-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>