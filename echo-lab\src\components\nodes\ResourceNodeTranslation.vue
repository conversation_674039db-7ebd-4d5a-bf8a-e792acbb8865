<!--
  资源节点翻译组件
  处理文本翻译功能
-->
<template>
  <div class="translation-component">
    <div class="preview-section">
      <div class="preview-header">
        <div>翻译：</div>
        <el-button-group>
          <el-button size="small" type="primary" @click="showDialog" :disabled="!hasSourceNode">
            <el-icon class="el-icon--left">
              <i-ep-edit />
            </el-icon>
            编辑翻译
          </el-button>
        </el-button-group>
      </div>
      <div class="preview-info"
        :class="{ 'success-bg': getTranslationPercentage() === 100 && totalTranslationCount > 0, 'warning-bg': getTranslationPercentage() < 100 && totalTranslationCount > 0 }">
        <div class="preview-progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: getTranslationPercentage() + '%' }"></div>
          </div>
          <div class="progress-text">
            已翻译: {{ translationCount }}/{{ totalTranslationCount }}
            <span v-if="getTranslationPercentage() === 100" class="status-tag success">完成</span>
            <span v-else-if="totalTranslationCount > 0" class="status-tag warning">未完成</span>
            <span v-else class="status-tag info">无需翻译</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 翻译编辑对话框 -->
    <standard-dialog v-model="dialogVisible" title="编辑翻译" width="80%">
      <div class="dialog-content">
        <div class="dialog-toolbar">
          <div class="toolbar-main">
            <div class="targets-section">
              <span class="label">目标语言：</span>
              <el-select v-model="params.targets" multiple @change="handleTargetsChange" size="large"
                class="language-select">
                <el-option v-for="lang in translationLanguages" :key="lang.value" :label="lang.label"
                  :value="lang.value" />
              </el-select>
            </div>
            <div class="dialog-actions">
              <el-input v-model="searchText" placeholder="搜索内容" clearable class="search-input">
                <template #prefix>
                  <el-icon>
                    <i-ep-search />
                  </el-icon>
                </template>
              </el-input>
            </div>
          </div>
        </div>

        <div class="translations-table-container" v-if="params.targets.length > 0">
          <el-table-v2 ref="translationTableRef" :data="filteredTranslations" :columns="translationTableColumns"
            :width="1200" :height="500" :estimated-row-height="120" row-key="id" border>
            <template #cell="{ column, rowData, rowIndex }">
              <!-- 序号列 -->
              <div v-if="column.key === 'index'" class="cell-content center">
                {{ rowIndex + 1 }}
              </div>

              <!-- 原文列 -->
              <div v-else-if="column.key === 'original'" class="cell-content">
                <div class="original-text-cell">{{ rowData.content }}</div>
                <div class="language-detection">
                  <span class="language-label">语言:</span>
                  <el-tag size="small" type="info">{{ getLanguageLabel(rowData.detectedLanguage) }}</el-tag>
                </div>
              </div>

              <!-- 翻译列 -->
              <div v-else-if="column.key.startsWith('translation_')" class="cell-content">
                <div class="translation-cell">
                  <el-input v-model="rowData.translations[column.targetLang]" type="textarea" :rows="3"
                    :class="{ 'empty-translation': !rowData.translations[column.targetLang] || !rowData.translations[column.targetLang].trim() }"
                    :placeholder="`请输入${getLanguageLabel(column.targetLang)}翻译`" />
                  <el-button type="primary" size="small" @click="translateItem(rowData, column.targetLang)"
                    :loading="rowData.translatingStates && rowData.translatingStates[column.targetLang]"
                    class="translate-btn">
                    <el-icon style="margin-right: 0.25rem;">
                      <i-ep-edit />
                    </el-icon>
                    翻译
                  </el-button>
                </div>
              </div>
            </template>
          </el-table-v2>
        </div>
        <div class="empty-state" v-else>
          <el-empty description="请选择目标语言" />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveTranslations">保存修改</el-button>
          <el-dropdown>
            <el-button type="success" :loading="isTranslating">
              <el-icon style="margin-right: 0.25rem;">
                <i-ep-edit />
              </el-icon>
              批量翻译
              <el-icon class="el-icon--right">
                <i-ep-arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="translateAllLanguages(false)">重新翻译(全部)</el-dropdown-item>
                <el-dropdown-item @click="translateAllLanguages(true)">补全翻译</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>
    </standard-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入
import StandardDialog from '../common/StandardDialog.vue';
import { getLanguageLabel, SUPPORTED_LANGUAGES } from '@/config/languages';
import httpClient from '@/utils/httpClient';
import { API_ENDPOINTS } from '@/config/api';
import { TRANSLATION_TIMEOUT } from '@/config/translation';

const props = defineProps({
  nodeId: {
    type: String,
    required: true
  },
  processedResult: {
    type: Object,
    default: () => null
  },
  params: {
    type: Object,
    required: true
  },
  hasSourceNode: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:params', 'process-node']);

// 对话框状态
const dialogVisible = ref(false);
const isTranslating = ref(false);
const searchText = ref('');

// 翻译项
const translationItems = ref([]);

// 表格引用
const translationTableRef = ref(null);

// el-table-v2 动态列配置
const translationTableColumns = computed(() => {
  const columns = [
    {
      key: 'index',
      title: '序号',
      dataKey: 'index',
      width: 80,
      align: 'center'
    },
    {
      key: 'original',
      title: '原文',
      dataKey: 'original',
      width: 200
    }
  ];

  // 为每个目标语言添加一列
  props.params.targets.forEach(target => {
    columns.push({
      key: `translation_${target}`,
      title: getLanguageLabel(target),
      dataKey: `translation_${target}`,
      width: 250,
      targetLang: target // 自定义属性，用于在模板中识别目标语言
    });
  });

  return columns;
});

// 监听对话框显示状态，延时刷新表格
watch(dialogVisible, (newVal) => {
  if (newVal) {
    // 对话框打开时，延时刷新表格以确保正确渲染
    nextTick(() => {
      setTimeout(() => {
        // 触发resize事件让表格重新计算尺寸
        window.dispatchEvent(new Event('resize'));

        // 如果表格有scrollToTop方法，调用它来触发重新渲染
        if (translationTableRef.value && typeof translationTableRef.value.scrollToTop === 'function') {
          translationTableRef.value.scrollToTop();
        }
      }, 200);
    });
  }
});

// 翻译相关计算属性
const translationCount = computed(() => {
  if (!props.processedResult) return 0;

  // 优先使用 sourceSegments 作为数据源，确保一致性
  if (!props.processedResult.sourceSegments || props.processedResult.sourceSegments.length === 0) {
    return 0;
  }

  // 获取目标语言列表
  const targets = props.params.targets || [];
  if (targets.length === 0) return 0;

  // 获取所有非关键词的分句
  const validSegments = props.processedResult.sourceSegments.filter(seg =>
    !seg.isKeyword && seg.type !== 'keyword'
  );

  // 调试信息
  console.log('翻译计算调试信息:', {
    totalSegments: props.processedResult.sourceSegments.length,
    validSegments: validSegments.length,
    targets: targets,
    translations: props.processedResult.translations
  });

  // 计算已翻译的项目数量
  let count = 0;
  validSegments.forEach(segment => {
    targets.forEach(target => {
      // 检查翻译数据
      const hasTranslation = props.processedResult.translations &&
        props.processedResult.translations[target] &&
        props.processedResult.translations[target][segment.id] &&
        props.processedResult.translations[target][segment.id].trim();

      if (hasTranslation) {
        count++;
        console.log(`已翻译: ${segment.id} -> ${target}: "${props.processedResult.translations[target][segment.id].substring(0, 20)}..."`);
      } else {
        console.log(`未翻译: ${segment.id} -> ${target}`);
      }
    });
  });

  console.log(`翻译统计: ${count}/${targets.length * validSegments.length}`);
  return count;
});

const totalTranslationCount = computed(() => {
  if (!props.processedResult) return 0;

  // 优先使用 sourceSegments 作为数据源，确保一致性
  if (!props.processedResult.sourceSegments || props.processedResult.sourceSegments.length === 0) {
    return 0;
  }

  // 获取目标语言列表
  const targets = props.params.targets || [];
  if (targets.length === 0) return 0;

  // 获取所有非关键词的分句数量
  const validSegmentCount = props.processedResult.sourceSegments.filter(seg =>
    !seg.isKeyword && seg.type !== 'keyword'
  ).length;

  // 计算所有目标语言的翻译项总数（排除关键词）
  return targets.length * validSegmentCount;
});

// 可用于翻译的语言列表
const translationLanguages = computed(() => {
  // 获取当前源语言
  const sourceLanguages = new Set();

  // 从源段落中收集所有语言
  if (props.processedResult && props.processedResult.sourceSegments) {
    props.processedResult.sourceSegments.forEach(segment => {
      if (segment.language) {
        sourceLanguages.add(segment.language);
      }
    });
  }

  // 如果没有找到源语言，返回所有支持的语言
  if (sourceLanguages.size === 0) {
    return SUPPORTED_LANGUAGES;
  }

  // 返回所有不在源语言集合中的语言
  return SUPPORTED_LANGUAGES.filter(lang => !sourceLanguages.has(lang.value));
});

// 过滤翻译 - 排除关键词并应用搜索
const filteredTranslations = computed(() => {
  // 首先过滤掉关键词
  const nonKeywordItems = translationItems.value.filter(item =>
    !item.isKeyword && item.type !== 'keyword'
  );

  // 如果没有搜索文本，返回所有非关键词项
  if (!searchText.value) return nonKeywordItems;

  // 应用搜索过滤 - 搜索原文或任何目标语言的翻译
  return nonKeywordItems.filter(item => {
    // 搜索原文
    if (item.content.toLowerCase().includes(searchText.value.toLowerCase())) {
      return true;
    }

    // 搜索所有目标语言的翻译
    return props.params.targets.some(target => {
      const translation = item.translations[target];
      return translation && translation.toLowerCase().includes(searchText.value.toLowerCase());
    });
  });
});

// 获取翻译完成百分比
function getTranslationPercentage() {
  if (!props.processedResult || !props.processedResult.translations || totalTranslationCount.value === 0) {
    return 0;
  }
  return Math.round((translationCount.value / totalTranslationCount.value) * 100);
}

// 处理目标语言变化
function handleTargetsChange() {
  emit('update:params', props.params);
  emit('process-node');
}

// 显示对话框
function showDialog() {
  // 强制重新处理节点
  emit('process-node');

  // 准备翻译项
  prepareTranslationItems();

  // 显示对话框
  dialogVisible.value = true;
}

// 准备翻译项
function prepareTranslationItems() {
  translationItems.value = [];

  // 如果没有处理结果，但有参数中的翻译数据，直接使用参数中的数据
  if (!props.processedResult || !props.processedResult.sourceSegments) {
    if (props.params.translations && Object.keys(props.params.translations).length > 0) {
      // 获取所有翻译项的ID
      const allIds = new Set();
      Object.values(props.params.translations).forEach(langTranslations => {
        Object.keys(langTranslations).forEach(id => allIds.add(id));
      });

      // 为每个ID创建翻译项
      allIds.forEach(id => {
        // 尝试从音频项中获取内容
        let content = '';
        let detectedLanguage = 'auto';

        // 如果有音频项，尝试从中获取内容
        if (props.params.audioItems) {
          const audioItem = props.params.audioItems.find(item => item.id === id);
          if (audioItem) {
            content = audioItem.text || '';
            detectedLanguage = audioItem.language || 'auto';
          }
        }

        const translations = {};
        props.params.targets.forEach(target => {
          if (props.params.translations[target]) {
            translations[target] = props.params.translations[target][id] || '';
          } else {
            translations[target] = '';
          }
        });

        // 初始化每个语言的翻译状态
        const translatingStates = {};
        props.params.targets.forEach(target => {
          translatingStates[target] = false;
        });

        translationItems.value.push({
          id: id,
          content: content || '未找到原文',
          translations,
          language: detectedLanguage,
          detectedLanguage: detectedLanguage,
          translatingStates
        });
      });
      return;
    }
  }

  // 正常处理流程：从处理结果中获取数据
  if (props.processedResult && props.processedResult.sourceSegments && props.processedResult.sourceSegments.length > 0) {
    props.processedResult.sourceSegments.forEach(segment => {
      const translations = {};

      // 为每个目标语言准备翻译
      props.params.targets.forEach(target => {
        // 优先使用处理结果中的翻译，如果没有则使用参数中的翻译
        if (props.processedResult.translations && props.processedResult.translations[target]) {
          translations[target] = props.processedResult.translations[target][segment.id] || '';
        } else if (props.params.translations && props.params.translations[target]) {
          translations[target] = props.params.translations[target][segment.id] || '';
        } else {
          translations[target] = '';
        }
      });

      // 使用源节点提供的语言信息
      const detectedLanguage = segment.language || 'auto';

      // 初始化每个语言的翻译状态
      const translatingStates = {};
      props.params.targets.forEach(target => {
        translatingStates[target] = false;
      });

      // 添加普通分句
      translationItems.value.push({
        id: segment.id,
        content: segment.content,
        translations,
        language: detectedLanguage,
        detectedLanguage: detectedLanguage,
        translatingStates,
        type: segment.type || 'normal',
        isKeyword: segment.isKeyword || segment.type === 'keyword'
      });

      // 处理关键词（如果有）
      if (segment.keywords && segment.keywords.length > 0) {
        segment.keywords.forEach(keyword => {
          const keywordTranslations = {};

          // 关键词不需要翻译，但需要创建空的翻译对象
          props.params.targets.forEach(target => {
            keywordTranslations[target] = '';
          });

          // 关键词的语言通常与其所属分句相同
          const keywordLanguage = keyword.language || segment.language || 'auto';

          // 初始化关键词的翻译状态
          const keywordTranslatingStates = {};
          props.params.targets.forEach(target => {
            keywordTranslatingStates[target] = false;
          });

          // 添加关键词翻译项
          translationItems.value.push({
            id: keyword.id,
            content: keyword.text,
            translations: keywordTranslations,
            language: keywordLanguage,
            detectedLanguage: keywordLanguage,
            translatingStates: keywordTranslatingStates,
            type: 'keyword',
            isKeyword: true,
            sourceSegmentId: segment.id
          });
        });
      }
    });
  }
}



// 翻译所有语言
async function translateAllLanguages(onlyUntranslated = false) {
  if (translationItems.value.length === 0) {
    ElMessage.warning('没有可翻译的内容');
    return;
  }

  if (props.params.targets.length === 0) {
    ElMessage.warning('请选择至少一种目标语言');
    return;
  }

  try {
    isTranslating.value = true;

    // 筛选需要翻译的项目
    let itemsToTranslate = translationItems.value;

    // 如果只翻译未翻译的部分，筛选出至少有一种语言未翻译的项目
    if (onlyUntranslated) {
      itemsToTranslate = translationItems.value.filter(item => {
        // 检查是否至少有一种目标语言未翻译
        return props.params.targets.some(target =>
          !item.translations[target] || !item.translations[target].trim()
        );
      });

      if (itemsToTranslate.length === 0) {
        ElMessage.info('(所有语言)的内容已全部翻译');
        isTranslating.value = false;
        return;
      }
    }

    // 显示加载提示
    const loadingText = onlyUntranslated
      ? '正在使用DeepSeek补全翻译，请耐心等待...'
      : '正在使用DeepSeek重新翻译，请耐心等待...';

    ElLoading.service({
      text: loadingText,
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 准备翻译请求，过滤掉关键词
    const textsToTranslate = itemsToTranslate
      .filter(item => !item.isKeyword && item.type !== 'keyword') // 过滤掉关键词
      .map(item => ({
        id: item.id,
        content: item.content,
        language: item.detectedLanguage
      }));

    // 调用翻译API
    const result = await httpClient.post(API_ENDPOINTS.TEXT_PROCESSING.TRANSLATE, {
      texts: textsToTranslate,
      targetLanguages: props.params.targets
    }, {
      timeout: TRANSLATION_TIMEOUT.FRONTEND_REQUEST, // 使用配置的翻译超时时间
    });

    if (result.success && result.translations) {
      // 更新翻译结果
      let updatedCount = 0;

      result.translations.forEach(translation => {
        const item = translationItems.value.find(i => i.id === translation.id);
        if (item) {
          Object.keys(translation.translations).forEach(lang => {
            // 如果是只翻译未翻译部分，且该语言已有翻译，则不更新
            if (!onlyUntranslated || !item.translations[lang] || !item.translations[lang].trim()) {
              item.translations[lang] = translation.translations[lang];
              updatedCount++;
            }
          });
        }
      });

      const successMessage = onlyUntranslated
        ? `已补全 ${updatedCount} 个(所有语言)的内容`
        : '重新翻译(全部)完成';

      ElMessage.success(successMessage);
    } else {
      throw new Error(result.error || '翻译失败');
    }
  } catch (error) {
    console.error('批量翻译失败:', error);
    ElMessage.error('批量翻译失败: ' + error.message);
  } finally {
    // 关闭加载提示
    ElLoading.service().close();
    isTranslating.value = false;
  }
}

// 翻译单个项
async function translateItem(item, targetLang) {
  if (!item || !item.content || !targetLang) {
    return;
  }

  // 如果是关键词，不进行翻译
  if (item.isKeyword || item.type === 'keyword') {
    ElMessage.info('关键词不需要翻译');
    return;
  }

  try {
    // 设置特定语言的翻译状态
    if (!item.translatingStates) {
      item.translatingStates = {};
    }
    item.translatingStates[targetLang] = true;

    // 调用批量翻译API（单个项也使用批量接口）
    const result = await httpClient.post(API_ENDPOINTS.TEXT_PROCESSING.TRANSLATE, {
      texts: [{
        id: item.id,
        content: item.content,
        language: item.language
      }],
      targetLanguages: [targetLang]
    }, {
      timeout: TRANSLATION_TIMEOUT.FRONTEND_REQUEST, // 使用配置的翻译超时时间
    });

    if (result.success && result.translations && result.translations.length > 0) {
      const translation = result.translations[0];
      if (translation.translations[targetLang]) {
        item.translations[targetLang] = translation.translations[targetLang];
      }
    } else {
      throw new Error(result.error || '翻译失败');
    }
  } catch (error) {
    console.error('翻译失败:', error);
    ElMessage.error('翻译失败: ' + error.message);
  } finally {
    // 清除特定语言的翻译状态
    if (item.translatingStates) {
      item.translatingStates[targetLang] = false;
    }
  }
}

// 保存翻译
function saveTranslations() {
  if (translationItems.value.length === 0) {
    dialogVisible.value = false;
    return;
  }

  // 更新翻译
  const translations = {};

  // 为每个目标语言创建翻译对象
  props.params.targets.forEach(target => {
    translations[target] = {};

    // 添加每个项的翻译
    translationItems.value.forEach(item => {
      if (item.translations[target]) {
        translations[target][item.id] = item.translations[target];
      }
    });
  });

  // 更新节点参数
  const updatedParams = { ...props.params, translations };
  emit('update:params', updatedParams);

  // 重新处理节点
  emit('process-node');

  // 关闭对话框
  dialogVisible.value = false;

  ElMessage.success('翻译已保存');
}
</script>

<style scoped>
/* 预览区域样式 */
.preview-section {
  margin-top: 0.75rem;
  padding: 0.625rem;
  background-color: #f8f9fa;
  border-radius: 0.25rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-weight: bold;
  color: #606266;
}

.preview-info {
  text-align: center;
  font-size: 0.875rem;
  padding: 0.625rem;
  border-radius: 0.25rem;
  margin-top: 0.75rem;
}

/* 统一的预览背景颜色 */
.success-bg {
  color: #67c23a;
  background-color: #f0f9eb;
}

.warning-bg {
  color: #e6a23c;
  background-color: #fdf6ec;
}

.preview-progress {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.progress-bar {
  height: 0.5rem;
  background-color: #e4e7ed;
  border-radius: 0.25rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #67C23A;
  border-radius: 0.25rem;
  transition: width 0.3s ease;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.status-tag {
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

.status-tag.success {
  background-color: #f0f9eb;
  color: #67c23a;
  border: 0.0625rem solid #e1f3d8;
}

.status-tag.warning {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 0.0625rem solid #faecd8;
}

.status-tag.info {
  background-color: #f4f4f5;
  color: #909399;
  border: 0.0625rem solid #e9e9eb;
}

/* 对话框样式 */
.dialog-content {
  max-height: 100%;
  overflow-y: auto;
}

.dialog-toolbar {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
  gap: 1rem;
}

.toolbar-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  width: 100%;
}

/* 使用rem单位，移除媒体查询 */

.dialog-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.translations-table-container {
  max-height: 60vh;
  overflow-y: auto;
}

.targets-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.label {
  white-space: nowrap;
  font-size: 0.9rem;
}

/* 自定义下拉选择框样式 */
.language-select {
  width: 25rem;
  max-width: 100%;
}

.search-input {
  width: 15.625rem;
  max-width: 100%;
}

:deep(.el-select) {
  flex: 1;
}

:deep(.el-select .el-input__wrapper) {
  padding: 0 0.75rem;
  min-height: 2.5rem;
}

:deep(.el-select .el-input__suffix) {
  padding-left: 0.75rem;
}

:deep(.el-select .el-select__tags) {
  padding: 0.25rem 0;
}

.original-text-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  max-height: 6.25rem;
  overflow-y: auto;
  word-break: break-all;
  white-space: pre-wrap;
  padding: 0.5rem;
  border-radius: 0.25rem;
  line-height: 1.5;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
}

.language-detection {
  display: flex;
  align-items: center;
  margin-top: 0.5rem;
  font-size: 0.75rem;
}

.language-label {
  margin-right: 0.5rem;
}

.empty-translation {
  border-color: #E6A23C;
  background-color: rgba(230, 162, 60, 0.05);
}

/* 翻译单元格样式 */
.translation-cell {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.translate-btn {
  align-self: flex-start;
  margin-top: 0.25rem;
}

/* el-table-v2 样式 */
.translations-table-container .cell-content {
  padding: 0.5rem;
  display: flex;
  align-items: flex-start;
  word-break: break-word;
  flex-direction: column;
  justify-content: center;
  height: auto;
  min-height: auto;
}

.translations-table-container .cell-content.center {
  align-items: center;
  justify-content: center;
  flex-direction: row;
}

/* 翻译表格特殊样式 */
.translations-table-container .original-text-cell {
  max-height: none;
  overflow: visible;
  white-space: normal;
  word-wrap: break-word;
}

.translations-table-container .translation-cell {
  width: 100%;
  height: auto;
}
</style>
