'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('collections', {
      id: {
        type: Sequelize.STRING(21),
        primaryKey: true,
        allowNull: false
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: '合集名称'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '合集描述'
      },
      cover_image_url: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: '封面图片URL'
      },
      user_id: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '创建者ID'
      },
      is_public: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否公开'
      },
      tags: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: '标签，逗号分隔'
      },
      status: {
        type: Sequelize.ENUM('draft', 'published'),
        allowNull: false,
        defaultValue: 'draft',
        comment: '合集状态'
      },
      view_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '浏览次数'
      },
      favorite_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '收藏次数'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // 添加索引
    await queryInterface.addIndex('collections', ['user_id']);
    await queryInterface.addIndex('collections', ['status']);
    await queryInterface.addIndex('collections', ['is_public']);
    await queryInterface.addIndex('collections', ['created_at']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('collections');
  }
};
