/**
 * 用户等级服务
 * 提供用户等级和订阅管理功能
 */
const db = require("../models");
const { Op } = require("sequelize");

/**
 * 获取所有用户等级
 * @returns {Promise<Array>} 用户等级列表
 */
async function getAllLevels() {
  try {
    const levels = await db.UserLevel.findAll({
      order: [["level", "ASC"]],
    });

    return levels;
  } catch (error) {
    console.error("获取用户等级失败:", error);
    throw error;
  }
}

/**
 * 获取用户当前等级信息
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 用户等级信息
 */
async function getUserLevelInfo(userId) {
  try {
    // 获取用户信息
    const user = await db.User.findByPk(userId);

    if (!user) {
      throw new Error("用户不存在");
    }

    // 获取用户等级信息
    let levelInfo = await db.UserLevel.findOne({
      where: { level: user.level },
    });

    // 如果找不到对应的等级信息，使用默认值
    if (!levelInfo) {
      console.warn(
        `用户 ${userId} 的等级 ${user.level} 在数据库中不存在，使用默认值`
      );
      levelInfo = {
        level: user.level,
        name: user.level === 0 ? "免费用户" : `等级 ${user.level}`,
        description: "等级信息未配置",
      };
    }

    // 获取用户当前订阅信息
    const activeSubscription = await db.UserSubscription.findOne({
      where: {
        userId,
        status: "active",
        [Op.or]: [
          { endDate: null }, // 永久订阅
          { endDate: { [Op.gt]: new Date() } }, // 未过期
        ],
      },
      order: [["endDate", "DESC"]], // 优先选择结束日期最晚的
    });

    return {
      level: user.level,
      levelName: levelInfo.name,
      levelDescription: levelInfo.description,
      subscription: activeSubscription
        ? {
            id: activeSubscription.id,
            startDate: activeSubscription.startDate,
            endDate: activeSubscription.endDate,
            status: activeSubscription.status,
            isLifetime: activeSubscription.endDate === null,
          }
        : null,
    };
  } catch (error) {
    console.error("获取用户等级信息失败:", error);
    throw error;
  }
}

/**
 * 更新用户等级
 * @param {string} userId - 用户ID
 * @param {number} level - 新等级
 * @param {Object} options - 选项
 * @param {string} options.reason - 更新原因
 * @param {string} options.adminId - 管理员ID（如果是管理员操作）
 * @returns {Promise<Object>} 更新结果
 */
async function updateUserLevel(userId, level, options = {}) {
  const transaction = await db.sequelize.transaction();

  try {
    // 获取用户信息
    const user = await db.User.findByPk(userId, { transaction });

    if (!user) {
      await transaction.rollback();
      throw new Error("用户不存在");
    }

    // 获取等级信息
    const levelInfo = await db.UserLevel.findOne({
      where: { level },
      transaction,
    });

    if (!levelInfo) {
      await transaction.rollback();
      throw new Error("等级信息不存在");
    }

    // 更新用户等级
    await user.update({ level }, { transaction });

    // 如果有活跃订阅，将其标记为过期
    if (options.expireCurrentSubscription) {
      await db.UserSubscription.update(
        {
          status: "expired",
          endDate: new Date(),
        },
        {
          where: {
            userId,
            status: "active",
          },
          transaction,
        }
      );
    }

    // 提交事务
    await transaction.commit();

    return {
      success: true,
      user: {
        id: user.id,
        email: user.email,
        level: level,
        levelName: levelInfo.name,
      },
    };
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error("更新用户等级失败:", error);
    throw error;
  }
}

/**
 * 创建用户订阅
 * @param {string} userId - 用户ID
 * @param {number} level - 订阅等级
 * @param {Object} options - 订阅选项
 * @param {Date} options.startDate - 开始日期
 * @param {Date} options.endDate - 结束日期（null表示永久）
 * @param {string} options.paymentId - 支付ID
 * @returns {Promise<Object>} 创建结果
 */
async function createSubscription(userId, level, options = {}) {
  const transaction = await db.sequelize.transaction();

  try {
    // 获取用户信息
    const user = await db.User.findByPk(userId, { transaction });

    if (!user) {
      await transaction.rollback();
      throw new Error("用户不存在");
    }

    // 获取等级信息
    const levelInfo = await db.UserLevel.findOne({
      where: { level },
      transaction,
    });

    if (!levelInfo) {
      await transaction.rollback();
      throw new Error("等级信息不存在");
    }

    // 设置默认值
    const startDate = options.startDate || new Date();
    const endDate = options.endDate || null;

    // 创建订阅
    const subscription = await db.UserSubscription.create(
      {
        userId,
        level,
        startDate,
        endDate,
        status: "active",
        paymentId: options.paymentId,
      },
      { transaction }
    );

    // 更新用户等级
    await user.update({ level }, { transaction });

    // 提交事务
    await transaction.commit();

    return {
      success: true,
      subscription: {
        id: subscription.id,
        userId: subscription.userId,
        level: subscription.level,
        startDate: subscription.startDate,
        endDate: subscription.endDate,
        status: subscription.status,
      },
    };
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    console.error("创建订阅失败:", error);
    throw error;
  }
}

module.exports = {
  getAllLevels,
  getUserLevelInfo,
  updateUserLevel,
  createSubscription,
};
