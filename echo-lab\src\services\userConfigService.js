/**
 * 用户配置服务
 * 管理用户界面配置的保存和加载
 */

// 存储键前缀
const STORAGE_KEY_PREFIX = "echo_lab_user_config_";

// 默认配置
const DEFAULT_CONFIG = {
  // 播放器配置
  player: {
    // 是否显示播放列表
    showPlaylist: true,
    // 文本样式
    textStyle: {
      // 字体大小百分比
      fontSizePercent: 100, // 默认为100%
      // 字体颜色
      color: "#FFFFFF",
      // 文本阴影
      textShadow: "0.0625rem 0.0625rem 0.125rem rgba(0, 0, 0, 0.7)",
    },
    // 定时关闭设置
    autoShutdown: {
      // 是否启用定时关闭
      enabled: false,
      // 定时关闭时间（分钟）
      minutes: 15,
    },
  },
};

/**
 * 保存用户配置
 * @param {Object} config 用户配置对象
 * @returns {boolean} 是否保存成功
 */
export function saveUserConfig(config) {
  try {
    // 合并默认配置和用户配置
    const mergedConfig = {
      ...DEFAULT_CONFIG,
      ...config,
      // 确保嵌套对象也被正确合并
      player: {
        ...DEFAULT_CONFIG.player,
        ...(config.player || {}),
        textStyle: {
          ...DEFAULT_CONFIG.player.textStyle,
          ...(config.player?.textStyle || {}),
        },
        autoShutdown: {
          ...DEFAULT_CONFIG.player.autoShutdown,
          ...(config.player?.autoShutdown || {}),
        },
      },
    };

    // 保存到localStorage
    localStorage.setItem(
      `${STORAGE_KEY_PREFIX}global`,
      JSON.stringify(mergedConfig)
    );
    console.log("用户配置已保存:", mergedConfig);
    return true;
  } catch (error) {
    console.error("保存用户配置失败:", error);
    return false;
  }
}

/**
 * 加载用户配置
 * @returns {Object} 用户配置对象
 */
export function loadUserConfig() {
  try {
    // 从localStorage加载
    const savedConfig = localStorage.getItem(`${STORAGE_KEY_PREFIX}global`);

    if (!savedConfig) {
      console.log("未找到保存的用户配置，使用默认配置");
      return { ...DEFAULT_CONFIG };
    }

    // 解析保存的配置
    const parsedConfig = JSON.parse(savedConfig);

    // 合并默认配置和保存的配置，确保所有必要的字段都存在
    const mergedConfig = {
      ...DEFAULT_CONFIG,
      ...parsedConfig,
      // 确保嵌套对象也被正确合并
      player: {
        ...DEFAULT_CONFIG.player,
        ...(parsedConfig.player || {}),
        textStyle: {
          ...DEFAULT_CONFIG.player.textStyle,
          ...(parsedConfig.player?.textStyle || {}),
        },
        autoShutdown: {
          ...DEFAULT_CONFIG.player.autoShutdown,
          ...(parsedConfig.player?.autoShutdown || {}),
        },
      },
    };

    console.log("已加载用户配置:", mergedConfig);
    return mergedConfig;
  } catch (error) {
    console.error("加载用户配置失败:", error);
    return { ...DEFAULT_CONFIG };
  }
}

/**
 * 更新用户配置的特定部分
 * @param {string} path 配置路径，例如 'player.textStyle.fontSize'
 * @param {any} value 新值
 * @returns {boolean} 是否更新成功
 */
export function updateUserConfig(path, value) {
  try {
    // 加载当前配置
    const currentConfig = loadUserConfig();

    // 解析路径
    const pathParts = path.split(".");

    // 创建一个新的配置对象
    const newConfig = { ...currentConfig };

    // 递归设置值
    let current = newConfig;
    for (let i = 0; i < pathParts.length - 1; i++) {
      const part = pathParts[i];
      if (!current[part]) {
        current[part] = {};
      }
      current = current[part];
    }

    // 设置最终值
    current[pathParts[pathParts.length - 1]] = value;

    // 保存更新后的配置
    return saveUserConfig(newConfig);
  } catch (error) {
    console.error("更新用户配置失败:", error);
    return false;
  }
}

/**
 * 获取用户配置的特定部分
 * @param {string} path 配置路径，例如 'player.textStyle.fontSize'
 * @param {any} defaultValue 默认值（如果路径不存在）
 * @returns {any} 配置值
 */
export function getUserConfigValue(path, defaultValue) {
  try {
    // 加载当前配置
    const currentConfig = loadUserConfig();

    // 解析路径
    const pathParts = path.split(".");

    // 递归获取值
    let current = currentConfig;
    for (const part of pathParts) {
      if (current[part] === undefined) {
        return defaultValue;
      }
      current = current[part];
    }

    return current;
  } catch (error) {
    console.error("获取用户配置失败:", error);
    return defaultValue;
  }
}

/**
 * 重置用户配置为默认值
 * @returns {boolean} 是否重置成功
 */
export function resetUserConfig() {
  try {
    localStorage.removeItem(`${STORAGE_KEY_PREFIX}global`);
    console.log("用户配置已重置为默认值");
    return true;
  } catch (error) {
    console.error("重置用户配置失败:", error);
    return false;
  }
}

export default {
  saveUserConfig,
  loadUserConfig,
  updateUserConfig,
  getUserConfigValue,
  resetUserConfig,
  DEFAULT_CONFIG,
};
