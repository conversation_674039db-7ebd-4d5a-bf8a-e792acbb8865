<!--
  签到卡片组件
  显示签到状态和统计信息
-->
<template>
  <div class="check-in-card">
    <div class="check-in-header">
      <div class="consecutive-days">连续{{ consecutiveDays }}天</div>
    </div>
    
    <div class="check-in-content">
      <el-button 
        v-if="!todayChecked" 
        type="primary" 
        @click="handleCheckIn"
        class="check-in-btn"
        :loading="loading">
        立即签到
      </el-button>
      <div v-else class="checked-status">
        <el-icon><i-ep-check /></el-icon>
        今日已签到
      </div>
    </div>
    
    <div class="check-in-stats">
      <div class="stat-item">
        <span class="stat-value">{{ totalDays }}</span>
        <span class="stat-label">累计天数</span>
      </div>
      <div class="stat-item">
        <span class="stat-value">{{ maxConsecutiveDays }}</span>
        <span class="stat-label">最长连续</span>
      </div>
      <div class="stat-item">
        <span class="stat-value">{{ monthlyCount }}</span>
        <span class="stat-label">本月签到</span>
      </div>
    </div>
    
    <!-- 预留奖励区域 -->
    <div class="rewards-section" v-if="hasRewards">
      <!-- 未来奖励功能在此扩展 -->
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import { useCheckInStore } from '@/stores/checkInStore';
import { ElMessage } from 'element-plus';

const checkInStore = useCheckInStore();

// 计算属性
const consecutiveDays = computed(() => checkInStore.consecutiveDays);
const totalDays = computed(() => checkInStore.totalDays);
const monthlyCount = computed(() => checkInStore.monthlyCount);
const maxConsecutiveDays = computed(() => checkInStore.maxConsecutiveDays);
const todayChecked = computed(() => checkInStore.todayChecked);
const loading = computed(() => checkInStore.loading);
const hasRewards = computed(() => checkInStore.rewards.length > 0);

// 处理签到
const handleCheckIn = async () => {
  const result = await checkInStore.checkIn();
  if (result.success) {
    ElMessage.success(result.message);
  } else {
    ElMessage.warning(result.message);
  }
};

// 组件挂载时初始化数据
onMounted(async () => {
  await checkInStore.initCheckIn();
  // 同时加载当前月份的签到记录
  const now = new Date();
  await checkInStore.getCalendarData(now.getFullYear(), now.getMonth() + 1);
});
</script>

<style scoped>
.check-in-card {
  background: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.1);
  margin-bottom: 1.5rem;
  border: 1px solid #e4e7ed;
}

.check-in-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1.5rem;
}

.consecutive-days {
  font-size: 1.125rem;
  color: #007bff;
  font-weight: 600;
}

.check-in-content {
  margin-bottom: 1.5rem;
}

.check-in-btn {
  width: 100%;
  height: 2.5rem;
  font-size: 1rem;
}

.checked-status {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 2.5rem;
  background: #f0f9ff;
  border-radius: 0.25rem;
  color: #28a745;
  font-weight: 500;
}

.checked-status .el-icon {
  margin-right: 0.5rem;
  font-size: 1.125rem;
}

.check-in-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.stat-item {
  text-align: center;
  padding: 0.75rem 0.5rem;
  background: #f8f9fa;
  border-radius: 0.5rem;
}

.stat-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 600;
  color: #007bff;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: #666;
}

.rewards-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}
</style>