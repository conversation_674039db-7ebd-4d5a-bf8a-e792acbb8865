/**
 * 视频配置节点处理器
 * 配置视频输出参数
 */

/**
 * 处理视频配置节点
 * @param {Object} node - 节点对象
 * @param {Array} sourceResults - 源节点结果数组
 * @returns {Object} 处理结果
 */
function videoConfigNodeProcessor(node, sourceResults = []) {
  // 检查源节点结果
  if (!sourceResults || sourceResults.length === 0) {
    return {
      config: { ...node.params },
      content: [],
      isEmpty: true,
    };
  }

  // 创建内容数组
  const content = [];

  // 处理源节点结果 - 只处理文本序列节点
  sourceResults.forEach((sourceResult) => {
    // 如果源节点是文本序列节点
    if (sourceResult.sequence) {
      content.push({
        type: "sequence",
        sequence: [...sourceResult.sequence],
        sections: sourceResult.sections || [],
      });
    }
  });

  return {
    config: { ...node.params },
    content,
    isEmpty: content.length === 0,
  };
}

export default videoConfigNodeProcessor;
