/**
 * 前端视频导出统一接口
 * 集成WebCodecs和现有导出逻辑，完全替代后端处理
 */

import { webCodecsExporter } from "./webCodecsVideoExporter.js";
import { VIDEO_PHASES } from "@/config/video.js";

/**
 * 前端视频导出器
 * 使用WebCodecs API进行完全的前端视频合成
 */
export class FrontendVideoExporter {
  constructor() {
    this.isSupported = webCodecsExporter.isSupported;
  }

  /**
   * 从时间轴导出视频（Web Audio API模式）
   * @param {Array} timeline - 时间线数据
   * @param {Function} renderContent - 内容渲染函数
   * @param {Object} options - 导出选项
   */
  async exportVideoFromTimeline(timeline, renderContent, options = {}) {
    if (!this.isSupported) {
      throw new Error("当前浏览器不支持前端视频合成，请使用Chrome 94+");
    }

    const {
      videoQuality = "medium",
      fileName = "echo_lab_video",
      generateSubtitles = false,
      subtitleLanguages = [],
      configJson = null,
      containerWidth = null,
      containerHeight = null,
      onProgress = null,
      signal = null,
    } = options;

    console.log("开始从Timeline导出视频:", {
      timeline: timeline?.length,
      quality: videoQuality,
    });

    // 质量预设映射
    const qualityPresets = {
      low: { width: 640, height: 480 }, // H.264 Level 3.0
      medium: { width: 1280, height: 720 }, // H.264 Level 3.1
      high: { width: 1920, height: 1080 }, // H.264 Level 4.0
    };

    const preset = qualityPresets[videoQuality] || qualityPresets.medium;

    try {
      // 从时间轴获取音频数据
      const audioBuffer = await this._loadAudioFromTimeline(timeline, onProgress);

      // 调用通用导出方法
      return await this._exportVideoCommon(
        timeline,
        audioBuffer,
        renderContent,
        options
      );
    } catch (error) {
      if (error.name === "AbortError") {
        console.log("Timeline视频导出被取消");
        throw error;
      }

      console.error("Timeline视频导出失败:", error);
      throw new Error(`视频导出失败: ${error.message}`);
    }
  }

  /**
   * 导出视频（完全前端处理）- 保留原有方法以兼容
   * @param {Array} timeline - 时间线数据
   * @param {AudioBuffer} audioBuffer - 音频缓冲区
   * @param {Function} renderContent - 内容渲染函数
   * @param {Object} options - 导出选项
   */
  async exportVideo(timeline, audioBuffer, renderContent, options = {}) {
    try {
      return await this._exportVideoCommon(
        timeline,
        audioBuffer,
        renderContent,
        options
      );
    } catch (error) {
      if (error.name === "AbortError") {
        console.log("前端视频导出被取消");
        throw error;
      }

      console.error("前端视频导出失败:", error);
      throw new Error(`视频导出失败: ${error.message}`);
    }
  }

  /**
   * 通用视频导出逻辑（减少重复代码）
   * @param {Array} timeline - 时间线数据
   * @param {AudioBuffer} audioBuffer - 音频缓冲区
   * @param {Function} renderContent - 内容渲染函数
   * @param {Object} options - 导出选项
   */
  async _exportVideoCommon(timeline, audioBuffer, renderContent, options = {}) {
    if (!this.isSupported) {
      throw new Error("当前浏览器不支持前端视频合成，请使用Chrome 94+");
    }

    const {
      videoQuality = "medium",
      fileName = "echo_lab_video",
      generateSubtitles = false,
      subtitleLanguages = [],
      configJson = null,
      containerWidth = null,
      containerHeight = null,
      onProgress = null,
      signal = null,
    } = options;

    // 质量预设映射 - 与AVC Level兼容
    const qualityPresets = {
      low: { width: 640, height: 480 }, // H.264 Level 3.0
      medium: { width: 1280, height: 720 }, // H.264 Level 3.1
      high: { width: 1920, height: 1080 }, // H.264 Level 4.0
    };

    const preset = qualityPresets[videoQuality] || qualityPresets.medium;

    // 使用WebCodecs导出器
    return await webCodecsExporter.exportVideo(
      timeline,
      audioBuffer,
      renderContent,
      {
        width: preset.width,
        height: preset.height,
        frameRate: 30,
        quality: videoQuality,
        fileName,
        generateSubtitles,
        subtitleLanguages,
        configJson,
        // 传递容器尺寸信息
        containerWidth,
        containerHeight,
        onProgress: (progress) => {
          // 转换进度格式以兼容现有UI
          if (onProgress) {
            onProgress({
              phase: this._mapPhase(progress.phase),
              progress: progress.progress,
            });
          }
        },
        signal,
      }
    );
  }

  /**
   * 从时间轴加载音频数据（Web Audio API模式）
   * @param {Array} timeline - 时间轴数据
   * @param {Function} onProgress - 进度回调
   * @returns {Promise<AudioBuffer>} 音频缓冲区
   */
  async _loadAudioFromTimeline(timeline, onProgress) {
    try {
      if (onProgress) {
        onProgress({ phase: "preparing", progress: 1 });
      }

      // 创建音频上下文 - 使用24kHz采样率
      const audioContext = new AudioContext({
        sampleRate: 24000
      });

      // 收集所有音频URL - 与播放器逻辑保持一致
      const audioSegments = [];
      for (const item of timeline) {
        if (item.audioUrl) {
          audioSegments.push({
            url: item.audioUrl,
            duration: item.duration,
            startTime: item.startTime
          });
        } else if (!item.content && !item.imageUrl) {
          // 为静音片段创建空的AudioBuffer
          audioSegments.push({
            url: null, // 标记为静音
            duration: item.duration,
            startTime: item.startTime,
            speed: 1.0
          });
        } else {
          // 静音片段（停顿、封面等）
          audioSegments.push({
            url: null,
            duration: item.duration,
            startTime: item.startTime
          });
        }
      }

      console.log("从时间轴解析到音频片段:", audioSegments.length);

      if (onProgress) {
        onProgress({ phase: "preparing", progress: 3 });
      }

      // 加载并合并音频分片
      const audioBuffer = await this._loadAndMergeTimelineSegments(
        audioSegments,
        audioContext,
        onProgress
      );
      return audioBuffer;
    } catch (error) {
      console.error("从时间轴加载音频失败:", error);
      throw new Error(`音频加载失败: ${error.message}`);
    }
  }



  /**
   * 加载并合并时间轴音频片段
   * @param {Array} segments - 时间轴音频片段信息数组
   * @param {AudioContext} audioContext - 音频上下文
   * @param {Function} onProgress - 进度回调
   * @returns {Promise<AudioBuffer>} 合并后的音频缓冲区
   */
  async _loadAndMergeTimelineSegments(segments, audioContext, onProgress) {
    try {
      // 计算总时长
      const totalDuration = segments.reduce((sum, segment) => sum + segment.duration, 0);

      console.log(`🎵 音频合并开始: 总时长=${totalDuration.toFixed(3)}s, 采样率=${audioContext.sampleRate}Hz, 片段数=${segments.length}`);

      // 创建合并后的音频缓冲区 - 使用单声道
      const mergedBuffer = audioContext.createBuffer(
        1, // 单声道，与原音频保持一致
        Math.ceil(audioContext.sampleRate * totalDuration), // 确保有足够的空间
        audioContext.sampleRate
      );

      let currentOffset = 0;
      const targetData = mergedBuffer.getChannelData(0); // 目标是单声道

      // 逐个处理音频片段
      for (let i = 0; i < segments.length; i++) {
        const segment = segments[i];
        const expectedSamples = Math.ceil(audioContext.sampleRate * segment.duration);

        console.log(`🎵 处理片段${i}: 预期时长=${segment.duration.toFixed(3)}s, 预期样本=${expectedSamples}, 当前偏移=${currentOffset}`);

        if (segment.url) {
          // 音频片段：加载并复制数据
          try {
            const response = await fetch(segment.url);
            if (!response.ok) {
              throw new Error(`获取音频失败: ${response.status}`);
            }

            const arrayBuffer = await response.arrayBuffer();
            let audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

            console.log(`  ✅ 片段${i}加载成功: ${audioBuffer.numberOfChannels}声道, ${audioBuffer.sampleRate}Hz, 实际时长=${audioBuffer.duration.toFixed(3)}s, 实际样本=${audioBuffer.length}`);

            // 如果采样率不匹配，需要重采样
            if (audioBuffer.sampleRate !== audioContext.sampleRate) {
              console.log(`  🔄 片段${i}需要重采样: ${audioBuffer.sampleRate}Hz -> ${audioContext.sampleRate}Hz`);
              audioBuffer = await this._resampleAudioBuffer(audioBuffer, audioContext.sampleRate);
              console.log(`  ✅ 片段${i}重采样完成: 新时长=${audioBuffer.duration.toFixed(3)}s, 新样本=${audioBuffer.length}`);
            }

            // 获取源数据（总是使用第一个声道）
            const sourceData = audioBuffer.getChannelData(0);

            // 计算实际要复制的样本数：使用实际音频长度和预期长度的较小值
            const actualSamples = Math.min(sourceData.length, expectedSamples);

            // 确保不会超出目标缓冲区边界
            const availableSpace = targetData.length - currentOffset;
            const samplesToCopy = Math.min(actualSamples, availableSpace);

            console.log(`  📋 片段${i}复制信息: 实际样本=${actualSamples}, 可用空间=${availableSpace}, 将复制=${samplesToCopy}`);

            // 复制音频数据
            for (let j = 0; j < samplesToCopy; j++) {
              targetData[currentOffset + j] = sourceData[j];
            }

            // 如果实际音频比预期短，剩余部分保持静音
            if (samplesToCopy < expectedSamples) {
              const silenceSamples = Math.min(expectedSamples - samplesToCopy, availableSpace - samplesToCopy);
              console.log(`  🔇 片段${i}补充静音: ${silenceSamples} 样本`);
              // 静音部分已经是0，无需额外处理
            }

          } catch (error) {
            console.warn(`❌ 加载音频片段 ${i} 失败，使用静音替代:`, error);
            console.log(`  🔇 片段${i}使用静音: ${expectedSamples} 样本`);
            // 音频加载失败时，该片段保持静音（默认为0）
          }
        } else {
          // 静音片段
          console.log(`  🔇 片段${i}为静音片段: ${expectedSamples} 样本`);
          // 静音片段，保持默认的0值，无需额外处理
        }

        // 🔧 关键修复：始终按照预期的duration更新偏移量，确保时间轴一致性
        currentOffset += expectedSamples;

        // 更新进度
        if (onProgress) {
          const progress = 3 + ((i + 1) / segments.length) * 7; // 从3%到10%
          onProgress({ phase: "preparing", progress });
        }
      }

      console.log(`✅ 音频合并完成: 总时长=${totalDuration.toFixed(3)}s, ${segments.length} 片段, 最终偏移=${currentOffset}, 缓冲区长度=${mergedBuffer.length}`);
      return mergedBuffer;
    } catch (error) {
      console.error("❌ 合并时间轴音频失败:", error);
      throw error;
    }
  }

  /**
   * 重采样音频缓冲区
   * @param {AudioBuffer} audioBuffer - 原始音频缓冲区
   * @param {number} targetSampleRate - 目标采样率
   * @returns {Promise<AudioBuffer>} 重采样后的音频缓冲区
   */
  async _resampleAudioBuffer(audioBuffer, targetSampleRate) {
    if (audioBuffer.sampleRate === targetSampleRate) {
      return audioBuffer;
    }

    const duration = audioBuffer.duration;
    const newLength = Math.round(duration * targetSampleRate);

    // 创建离线音频上下文进行重采样
    const offlineContext = new OfflineAudioContext(
      audioBuffer.numberOfChannels,
      newLength,
      targetSampleRate
    );

    const bufferSource = offlineContext.createBufferSource();
    bufferSource.buffer = audioBuffer;
    bufferSource.connect(offlineContext.destination);
    bufferSource.start();

    return await offlineContext.startRendering();
  }




  /**
   * 映射进度阶段到现有的VIDEO_PHASES
   */
  _mapPhase(phase) {
    const phaseMap = {
      "generating-frames": VIDEO_PHASES.GENERATING_FRAMES,
      "initializing-encoder": VIDEO_PHASES.PREPARING_UPLOAD,
      "encoding-video": VIDEO_PHASES.PROCESSING_VIDEO,
      "encoding-audio": VIDEO_PHASES.PROCESSING_VIDEO,
      finalizing: VIDEO_PHASES.DOWNLOADING,
      "generating-subtitles": VIDEO_PHASES.DOWNLOADING,
      complete: VIDEO_PHASES.COMPLETE,
    };

    return phaseMap[phase] || phase;
  }

  /**
   * 下载导出的视频和字幕
   */
  async downloadResult(result) {
    const { videoBlob, fileName, subtitleFiles = [] } = result;

    if (subtitleFiles.length > 0) {
      // 如果有字幕文件，打包下载
      await webCodecsExporter.downloadVideoWithSubtitles(
        videoBlob,
        fileName,
        subtitleFiles
      );
    } else {
      // 只下载视频文件
      webCodecsExporter.downloadVideo(videoBlob, fileName);
    }
  }

  /**
   * 检查浏览器支持
   */
  checkSupport() {
    return {
      isSupported: this.isSupported,
      message: this.isSupported
        ? "支持前端视频合成"
        : "当前浏览器不支持WebCodecs API，请使用Chrome 94+",
    };
  }
}

// 创建单例实例
export const frontendVideoExporter = new FrontendVideoExporter();

/**
 * 检查是否应该使用前端导出
 * 根据浏览器支持情况决定
 */
export function shouldUseFrontendExport() {
  return frontendVideoExporter.isSupported;
}

/**
 * 获取导出方式信息
 */
export function getExportInfo() {
  if (frontendVideoExporter.isSupported) {
    return {
      method: "frontend",
      description: "前端视频合成（WebCodecs）",
      advantages: [
        "无需上传大量数据",
        "节省流量费用",
        "处理速度更快",
        "完全本地处理",
      ],
    };
  } else {
    return {
      method: "unsupported",
      description: "不支持前端合成",
      requirements: "Chrome 94+ 浏览器",
    };
  }
}
