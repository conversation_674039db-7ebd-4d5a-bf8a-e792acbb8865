/**
 * 内容模型
 * 用于存储用户创建的素材内容配置
 */
module.exports = (sequelize, DataTypes) => {
  const Content = sequelize.define(
    "Content",
    {
      // 主键ID，字符串类型
      id: {
        type: DataTypes.STRING(21),
        primaryKey: true,
        allowNull: false,
      },

      // 素材名称
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },

      // 素材描述
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },

      // 配置JSON数据
      configJson: {
        type: DataTypes.JSON,
        allowNull: false,
        field: "config_json",
      },

      // 缩略图URL(可选)
      thumbnailUrl: {
        type: DataTypes.STRING(255),
        allowNull: true,
        field: "thumbnail_url",
      },

      // 用户ID(必填)
      userId: {
        type: DataTypes.STRING(50),
        allowNull: false, // 修改为必填
        field: "user_id",
      },

      // 标签，逗号分隔(可选)
      tags: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },

      // 状态：draft(草稿), published(已发布)
      status: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: "draft",
      },
    },
    {
      tableName: "contents",
      underscored: true,
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      indexes: [
        {
          name: "idx_name",
          fields: ["name"],
        },
        {
          name: "idx_user_id",
          fields: ["user_id"],
        },
        {
          name: "idx_created_at",
          fields: ["created_at"],
        },
        {
          name: "idx_status",
          fields: ["status"],
        },
      ],
    }
  );

  Content.associate = function (models) {
    // 与用户表关联（暂时移除外键约束，避免数据类型不匹配问题）
    Content.belongsTo(models.User, {
      foreignKey: "userId",
      as: "creator",
      constraints: false, // 禁用外键约束
    });

    // 与合集内容关联表关联
    Content.hasMany(models.CollectionItem, {
      foreignKey: "contentId",
      as: "collectionItems",
      onDelete: "CASCADE",
    });
  };

  return Content;
};
