/**
 * 帮助功能组合式函数
 * 提供统一的帮助功能管理
 */
import { ref } from 'vue';
import { getHelpConfig } from '@/config/helpContent';

// 全局帮助状态
const helpVisible = ref(false);
const currentHelpConfig = ref({});

export function useHelp() {
  /**
   * 显示指定页面的帮助
   * @param {string} pageType 页面类型 (player, editor, home等)
   */
  const showHelp = (pageType) => {
    const config = getHelpConfig(pageType);
    currentHelpConfig.value = config;
    helpVisible.value = true;
  };

  /**
   * 隐藏帮助对话框
   */
  const hideHelp = () => {
    helpVisible.value = false;
  };

  /**
   * 切换帮助对话框显示状态
   * @param {string} pageType 页面类型
   */
  const toggleHelp = (pageType) => {
    if (helpVisible.value) {
      hideHelp();
    } else {
      showHelp(pageType);
    }
  };

  return {
    helpVisible,
    currentHelpConfig,
    showHelp,
    hideHelp,
    toggleHelp
  };
}
