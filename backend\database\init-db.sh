#!/bin/bash

# 数据库初始化脚本
# 用于创建数据库和表结构

# 数据库配置
DB_USER="root"
DB_PASSWORD=""
DB_HOST="localhost"
DB_NAME="echo-lab"

# 提示用户输入MySQL密码
read -sp "请输入MySQL root密码: " DB_PASSWORD
echo ""

# 创建数据库
echo "正在创建数据库..."
mysql -u$DB_USER -p$DB_PASSWORD -h$DB_HOST -e "CREATE DATABASE IF NOT EXISTS \`$DB_NAME\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

if [ $? -ne 0 ]; then
  echo "创建数据库失败，请检查MySQL连接信息"
  exit 1
fi

# 导入SQL文件
echo "正在创建表结构..."
# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
mysql -u$DB_USER -p$DB_PASSWORD -h$DB_HOST $DB_NAME < "$SCRIPT_DIR/init.sql"

if [ $? -ne 0 ]; then
  echo "创建表结构失败，请检查SQL文件"
  exit 1
fi

echo "数据库初始化完成！"
echo "现在可以运行 npm run db:create 来初始化Sequelize模型"
