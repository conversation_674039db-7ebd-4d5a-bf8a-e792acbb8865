/**
 * 统一的HTTP客户端
 * 提供项目中所有网络请求的统一入口
 */
import axios from "axios";
import { ElMessage } from "element-plus";
import { getToken, clearAuth } from "@/services/authService";
import router from "@/router";
import { handleNetworkError } from "@/utils/errorHandler";

// 错误消息映射
const ERROR_MESSAGES = {
  NETWORK_ERROR: "网络连接失败，请检查网络设置",
  TIMEOUT_ERROR: "请求超时，请稍后重试",
  SERVER_ERROR: "服务器错误，请稍后重试",
  UNAUTHORIZED: "登录已过期，请重新登录",
  FORBIDDEN: "权限不足，无法访问该功能",
  NOT_FOUND: "请求的资源不存在",
  VALIDATION_ERROR: "请求参数错误",
  RATE_LIMIT: "请求过于频繁，请稍后重试",
  UNKNOWN_ERROR: "未知错误，请稍后重试",
};

// 系统内部接口列表（失败时不显示用户消息）
const SILENT_API_PATTERNS = [
  '/api/errors',           // 错误上报
];

/**
 * 判断是否为系统内部接口（失败时不显示用户消息）
 * @param {string} url - 请求URL
 * @returns {boolean} - 是否为静默接口
 */
function shouldSilentError(url) {
  if (!url) return false;
  return SILENT_API_PATTERNS.some(pattern => url.includes(pattern));
}

// 创建axios实例
const httpClient = axios.create({
  baseURL: "", // 使用相对路径，让 Vite 代理处理
  timeout: 60000, // 60秒超时
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器 - 自动添加认证令牌和用户语言信息
httpClient.interceptors.request.use(
  (config) => {
    // 添加认证令牌
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 动态导入用户设置（避免循环依赖）
    try {
      // 添加用户语言和等级信息到请求头
      const userSettings = JSON.parse(localStorage.getItem('echolab_user_settings') || '{}');

      if (userSettings.language?.learning) {
        config.headers['X-User-Language'] = userSettings.language.learning;
      }

      if (userSettings.level?.current) {
        config.headers['X-Language-Level'] = userSettings.level.current;
      }
    } catch (error) {
      // 忽略解析错误，不影响请求
      console.debug('解析用户设置失败:', error);
    }

    return config;
  },
  (error) => {
    console.error("请求拦截器错误:", error);
    return Promise.reject(error);
  }
);

// 响应拦截器 - 统一错误处理
httpClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error("HTTP请求错误:", error);

    // 处理网络错误
    if (!error.response) {
      const errorType = error.code === "ECONNABORTED" ? "timeout" : "network";
      const errorMessage =
        error.code === "ECONNABORTED"
          ? ERROR_MESSAGES.TIMEOUT_ERROR
          : ERROR_MESSAGES.NETWORK_ERROR;

      // 上报网络错误
      handleNetworkError(error.config?.url || "unknown", null, errorMessage, {
        errorType,
        method: error.config?.method,
      });

      // 系统内部接口失败时不显示用户消息
      const isSilentAPI = shouldSilentError(error.config?.url);
      if (!isSilentAPI) {
        ElMessage.error(errorMessage);
      }
      return Promise.reject(error);
    }

    const { status, data } = error.response;
    let errorMessage = ERROR_MESSAGES.UNKNOWN_ERROR;

    // 根据状态码处理错误
    switch (status) {
      case 400:
        errorMessage = data?.error || ERROR_MESSAGES.VALIDATION_ERROR;
        break;
      case 401:
        errorMessage = ERROR_MESSAGES.UNAUTHORIZED;
        // 清除认证信息并跳转到登录页
        clearAuth();
        if (router.currentRoute.value.path !== "/") {
          router.push("/");
        }
        break;
      case 403:
        errorMessage = ERROR_MESSAGES.FORBIDDEN;
        break;
      case 404:
        errorMessage = ERROR_MESSAGES.NOT_FOUND;
        break;
      case 429:
        errorMessage = ERROR_MESSAGES.RATE_LIMIT;
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        errorMessage = ERROR_MESSAGES.SERVER_ERROR;
        break;
      default:
        errorMessage = data?.error || ERROR_MESSAGES.UNKNOWN_ERROR;
    }

    // 上报API错误（排除401认证错误，避免过多上报）
    if (status !== 401) {
      handleNetworkError(error.config?.url || "unknown", status, errorMessage, {
        method: error.config?.method,
        requestData: error.config?.data ? "present" : "none",
      });
    }

    // 显示错误消息（排除系统内部接口和401认证错误）
    const isSilentAPI = shouldSilentError(error.config?.url);
    if (status !== 401 && !isSilentAPI) {
      ElMessage.error(errorMessage);
    }

    return Promise.reject(error);
  }
);

/**
 * GET请求
 * @param {string} url 请求URL
 * @param {Object} params 查询参数
 * @param {Object} config 额外配置
 * @returns {Promise} 请求结果
 */
export const get = async (url, params = {}, config = {}) => {
  try {
    const response = await httpClient.get(url, {
      params,
      ...config,
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * POST请求
 * @param {string} url 请求URL
 * @param {Object} data 请求数据
 * @param {Object} config 额外配置
 * @returns {Promise} 请求结果
 */
export const post = async (url, data = {}, config = {}) => {
  try {
    const response = await httpClient.post(url, data, config);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * PUT请求
 * @param {string} url 请求URL
 * @param {Object} data 请求数据
 * @param {Object} config 额外配置
 * @returns {Promise} 请求结果
 */
export const put = async (url, data = {}, config = {}) => {
  try {
    const response = await httpClient.put(url, data, config);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * DELETE请求
 * @param {string} url 请求URL
 * @param {Object} config 额外配置
 * @returns {Promise} 请求结果
 */
export const del = async (url, config = {}) => {
  try {
    const response = await httpClient.delete(url, config);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * 文件上传请求
 * @param {string} url 请求URL
 * @param {FormData} formData 表单数据
 * @param {Object} options 上传选项
 * @returns {Promise} 请求结果
 */
export const upload = async (url, formData, options = {}) => {
  const { onProgress, signal, timeout, ...restOptions } = options;

  try {
    const response = await httpClient.post(url, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      signal,
      timeout, // 传递超时配置
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total && typeof onProgress === "function") {
          const percentComplete = Math.round(
            (progressEvent.loaded / progressEvent.total) * 100
          );
          onProgress(percentComplete);
        }
      },
      ...restOptions, // 传递其他配置选项
    });
    return response.data;
  } catch (error) {
    // 处理取消上传
    if (axios.isCancel(error)) {
      throw new DOMException("上传已取消", "AbortError");
    }
    throw error;
  }
};

/**
 * 原始axios实例（用于特殊需求）
 */
export const rawClient = httpClient;

/**
 * 默认导出
 */
export default {
  get,
  post,
  put,
  delete: del,
  upload,
  rawClient,
};
