# 音频倍速处理功能

## 功能概述

音频倍速处理功能提供了一个透明的音频倍速代理服务，可以对任何音频文件进行倍速处理并自动缓存结果。

## 使用方法

### API 端点

```
GET /api/audio/speed?path={音频路径}&speed={倍速值}
```

### 参数说明

- `path`: 音频文件在 OSS 中的路径（不包含域名）
- `speed`: 倍速值，范围 0.5 - 2.0

### 使用示例

#### 原始音频
```
http://localhost:3001/oss-resources/audio/ja/1749390197378_f5a71f15-68ea-4bdf-9860-8f3ce97bdbb7.mp3
```

#### 1.2倍速音频
```
http://localhost:3001/oss-resources/audio/ja/1749390197378_f5a71f15-68ea-4bdf-9860-8f3ce97bdbb7.mp3?speed=1.2
```

#### 0.8倍速音频
```
http://localhost:3001/oss-resources/audio/ja/1749390197378_f5a71f15-68ea-4bdf-9860-8f3ce97bdbb7.mp3?speed=0.8
```

## 工作原理

1. **请求拦截**: Nginx 检测到带有 `speed` 参数的音频请求
2. **缓存检查**: 后端首先检查是否已有对应倍速的缓存文件
3. **缓存命中**: 如果缓存存在，直接返回缓存文件
4. **缓存未命中**: 如果缓存不存在，执行以下步骤：
   - 从 OSS 下载原始音频
   - 使用 FFmpeg 处理倍速
   - 保存处理后的音频到缓存
   - 返回处理后的音频
5. **降级处理**: 如果处理失败，返回原始音频

## 缓存机制

- **缓存位置**: `/var/cache/audio-speed/`
- **缓存结构**: 保持原有目录结构，文件名格式为 `{hash}_{speed}x.mp3`
- **缓存策略**: 永久缓存，不自动清理
- **缓存优势**: 
  - 首次处理后永久可用
  - 大幅提升后续访问速度
  - 减少服务器计算负担

## 前端集成

前端只需要在音频 URL 后添加 speed 参数即可：

```javascript
// 时间轴生成时
const audioUrl = item.audioUrl + (speed !== 1.0 ? `?speed=${speed}` : '');

// WebAudioPlayer 使用（无需修改）
await this.loadAudioBuffer(audioUrl);
```

## 性能特点

- **首次请求**: 需要处理时间（通常几秒钟）
- **后续请求**: 直接返回缓存，毫秒级响应
- **并发处理**: 支持多个音频同时处理
- **内存优化**: 使用流式传输，不占用大量内存

## 错误处理

- **参数错误**: 返回 400 错误和错误信息
- **音频不存在**: 返回 404 错误
- **处理失败**: 自动降级返回原始音频
- **网络错误**: 返回 500 错误

## 技术栈

- **音频处理**: FFmpeg + fluent-ffmpeg
- **缓存存储**: 本地文件系统
- **流式传输**: Node.js streams
- **代理服务**: Nginx + Express

## 监控和日志

所有处理过程都有详细的日志记录：
- 缓存命中/未命中
- 音频下载状态
- 处理进度和结果
- 错误信息和降级处理

## 测试

运行测试脚本验证功能：

```bash
node scripts/testAudioSpeed.js
```
