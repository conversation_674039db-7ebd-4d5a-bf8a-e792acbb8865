module.exports = (sequelize, DataTypes) => {
  const ErrorLog = sequelize.define(
    "ErrorLog",
    {
      id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      error_hash: {
        type: DataTypes.STRING(32),
        allowNull: false,
        comment: "错误唯一标识(基于message+stack的MD5)",
      },
      error_type: {
        type: DataTypes.ENUM(
          "javascript",
          "vue",
          "network",
          "resource",
          "promise"
        ),
        allowNull: false,
        comment: "错误类型",
      },
      message: {
        type: DataTypes.TEXT,
        allowNull: false,
        comment: "错误消息",
      },
      stack: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "错误堆栈",
      },
      url: {
        type: DataTypes.STRING(500),
        allowNull: false,
        comment: "发生错误的页面URL",
      },
      line_number: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "错误行号",
      },
      column_number: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "错误列号",
      },
      filename: {
        type: DataTypes.STRING(500),
        allowNull: true,
        comment: "错误文件名",
      },
      user_agent: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "用户代理字符串",
      },
      user_id: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: "用户ID（如果已登录）",
      },
      session_id: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: "会话ID",
      },
      ip_address: {
        type: DataTypes.STRING(45),
        allowNull: true,
        comment: "IP地址",
      },
      browser_info: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "浏览器信息",
      },
      device_info: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "设备信息",
      },
      context_data: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "错误上下文数据",
      },
      enhanced_context: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: "增强的错误上下文信息（性能、环境、用户行为等）",
      },
      severity_level: {
        type: DataTypes.ENUM("low", "medium", "high", "critical"),
        allowNull: true,
        comment: "错误严重程度等级",
      },
      severity_score: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: "错误严重程度分数(0-100)",
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: "error_logs",
      timestamps: false,
      indexes: [
        { fields: ["error_hash"] },
        { fields: ["error_type"] },
        { fields: ["url"] },
        { fields: ["user_id"] },
        { fields: ["created_at"] },
        { fields: ["severity_level"] },
        { fields: ["severity_score"] },
      ],
    }
  );

  // 定义关联关系
  ErrorLog.associate = function (models) {
    // 多个错误日志属于一个错误汇总
    ErrorLog.belongsTo(models.ErrorSummary, {
      foreignKey: "error_hash",
      targetKey: "error_hash",
      as: "summary",
    });
  };

  return ErrorLog;
};
