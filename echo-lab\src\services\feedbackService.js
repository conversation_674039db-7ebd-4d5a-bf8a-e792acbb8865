/**
 * 用户反馈服务
 * 提供用户反馈的提交和查询功能
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

/**
 * 提交用户反馈
 * @param {Object} feedbackData 反馈数据
 * @param {string} feedbackData.type 反馈类型
 * @param {string} feedbackData.content 反馈内容
 * @param {string} [feedbackData.contact] 联系方式（可选）
 * @param {string} [feedbackData.pageUrl] 页面URL（可选）
 * @returns {Promise<Object>} 提交结果
 */
export async function submitFeedback(feedbackData) {
  try {
    const response = await httpClient.post(
      API_ENDPOINTS.FEEDBACK.BASE,
      feedbackData
    );
    return response;
  } catch (error) {
    console.error("提交反馈失败:", error);
    throw error.response?.data || { success: false, error: "提交反馈失败" };
  }
}

/**
 * 获取当前用户的反馈列表
 * @param {Object} options 查询选项
 * @param {number} [options.page=1] 页码
 * @param {number} [options.pageSize=10] 每页数量
 * @param {string} [options.status] 状态（可选）
 * @param {string} [options.type] 类型（可选）
 * @returns {Promise<Object>} 查询结果
 */
export async function getUserFeedbacks(options = {}) {
  try {
    const params = {
      page: options.page || 1,
      pageSize: options.pageSize || 10,
      status: options.status,
      type: options.type,
    };

    const response = await httpClient.get(API_ENDPOINTS.FEEDBACK.USER, params);
    return response;
  } catch (error) {
    console.error("获取用户反馈列表失败:", error);
    throw (
      error.response?.data || { success: false, error: "获取用户反馈列表失败" }
    );
  }
}

/**
 * 获取所有反馈（管理员）
 * @param {Object} options 查询选项
 * @param {number} [options.page=1] 页码
 * @param {number} [options.pageSize=10] 每页数量
 * @param {string} [options.status] 状态（可选）
 * @param {string} [options.type] 类型（可选）
 * @param {string} [options.userId] 用户ID（可选）
 * @returns {Promise<Object>} 查询结果
 */
export async function getAllFeedbacks(options = {}) {
  try {
    const params = {
      page: options.page || 1,
      pageSize: options.pageSize || 10,
      status: options.status,
      type: options.type,
      userId: options.userId,
    };

    const response = await httpClient.get(API_ENDPOINTS.FEEDBACK.ADMIN, params);
    return response;
  } catch (error) {
    console.error("获取所有反馈失败:", error);
    throw error.response?.data || { success: false, error: "获取所有反馈失败" };
  }
}

/**
 * 更新反馈状态（管理员）
 * @param {number} id 反馈ID
 * @param {Object} updateData 更新数据
 * @param {string} updateData.status 状态
 * @param {string} [updateData.adminReply] 管理员回复（可选）
 * @returns {Promise<Object>} 更新结果
 */
export async function updateFeedbackStatus(id, updateData) {
  try {
    const response = await httpClient.put(
      `${API_ENDPOINTS.FEEDBACK.ADMIN}/${id}`,
      updateData
    );
    return response;
  } catch (error) {
    console.error("更新反馈状态失败:", error);
    throw error.response?.data || { success: false, error: "更新反馈状态失败" };
  }
}

export default {
  submitFeedback,
  getUserFeedbacks,
  getAllFeedbacks,
  updateFeedbackStatus,
};
