/**
 * 验证码模型
 * 用于存储邮箱验证码
 */
module.exports = (sequelize, DataTypes) => {
  const VerificationCode = sequelize.define(
    "VerificationCode",
    {
      // 主键ID，使用nanoid
      id: {
        type: DataTypes.STRING(21),
        primaryKey: true,
        allowNull: false,
      },

      // 邮箱
      email: {
        type: DataTypes.STRING(255),
        allowNull: false,
        validate: {
          isEmail: true,
        },
      },

      // 验证码
      code: {
        type: DataTypes.STRING(6),
        allowNull: false,
      },

      // 验证码类型：login, register, reset_password
      type: {
        type: DataTypes.ENUM("login", "register", "reset_password"),
        defaultValue: "login",
      },

      // 过期时间
      expiresAt: {
        type: DataTypes.DATE,
        allowNull: false,
        field: "expires_at",
      },

      // 是否已使用
      used: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      // 表名
      tableName: "verification_codes",
      // 时间戳
      timestamps: true,
      underscored: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      // 索引
      indexes: [
        {
          fields: ["email"],
        },
        {
          fields: ["expires_at"],
        },
      ],
    }
  );

  return VerificationCode;
};
