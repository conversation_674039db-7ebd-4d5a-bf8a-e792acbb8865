<!--
  签到日历组件
  使用el-calendar展示签到记录
-->
<template>
  <div class="check-in-calendar">
    <el-config-provider :locale="zhCn">
      <el-calendar v-model="selectedDate">
        <template #date-cell="{ data }">
          <div class="calendar-cell" :class="getCellClass(data.day)">
            <span class="date-number">{{ data.day.split('-')[2] }}</span>
            <span v-if="isCheckedIn(data.day)" class="check-text">已签</span>
          </div>
        </template>
      </el-calendar>
    </el-config-provider>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useCheckInStore } from '@/stores/checkInStore';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';

const checkInStore = useCheckInStore();
const selectedDate = ref(new Date());

// 计算属性
const checkInRecords = computed(() => checkInStore.checkInRecords);

// 检查指定日期是否已签到
const isCheckedIn = (date) => {
  return checkInRecords.value.some(record => record.date === date);
};

// 获取日期单元格的样式类
const getCellClass = (date) => {
  const classes = [];
  
  if (isCheckedIn(date)) {
    classes.push('checked-in');
  }
  
  const today = new Date().toISOString().split('T')[0];
  if (date === today) {
    classes.push('today');
  }
  
  return classes;
};

// 加载日历数据
const loadCalendarData = async (year, month) => {
  try {
    await checkInStore.getCalendarData(year, month);
  } catch (error) {
    console.error('加载日历数据失败:', error);
  }
};

// 组件挂载时加载当前月份数据
onMounted(async () => {
  const now = new Date();
  await loadCalendarData(now.getFullYear(), now.getMonth() + 1);
});

// 监听日期变化，加载对应月份的签到数据（用户切换月份时）
watch(selectedDate, async (newDate) => {
  const year = newDate.getFullYear();
  const month = newDate.getMonth() + 1;
  await loadCalendarData(year, month);
});
</script>

<style scoped>
.check-in-calendar {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
}

.calendar-cell {
  position: relative;
  width: 100%;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.calendar-cell:hover {
  background: #f8f9fa;
  transform: scale(1.05);
}

.calendar-cell.today {
  border: 2px solid #409eff;
  color: #409eff;
  font-weight: 600;
}

.calendar-cell.checked-in {
  background: #67c23a;
  color: white;
  flex-direction: column;
  gap: 0.125rem;
}

.date-number {
  font-size: 0.875rem;
  font-weight: 500;
}

.check-text {
  font-size: 0.625rem;
  font-weight: 500;
}

/* 覆盖element-plus日历样式 */
:deep(.el-calendar) {
  border: none;
  box-shadow: none;
}

:deep(.el-calendar-table) {
  border: none;
}

:deep(.el-calendar-table .el-calendar-day) {
  padding: 0;
  border: none;
  height: 100%;
}

:deep(.el-calendar-table td) {
  border: none;
  padding: 0;
  height: 3rem;
  vertical-align: top;
}

:deep(.el-calendar-table th) {
  border: none;
  padding: 0.75rem 0.25rem;
  background: #f8f9fa;
  color: #666;
  font-weight: 600;
  font-size: 0.875rem;
}

:deep(.el-calendar__header) {
  padding: 1rem;
  border-bottom: 2px solid #f0f2f5;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 0.75rem 0.75rem 0 0;
  margin: -1.5rem -1.5rem 1rem -1.5rem;
}

:deep(.el-calendar__title) {
  color: #333;
  font-weight: 600;
  font-size: 1.125rem;
}

:deep(.el-calendar__button-group) {
  gap: 0.5rem;
}

:deep(.el-calendar__button-group .el-button) {
  border-radius: 0.5rem;
  font-weight: 500;
}
</style>