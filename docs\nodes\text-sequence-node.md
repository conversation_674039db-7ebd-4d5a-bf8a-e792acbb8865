# 文本序列节点

本文档详细说明了 Echo Lab 中文本序列节点的功能、参数和使用方法。

## 概述

文本序列节点是 Echo Lab 中的核心节点类型，用于处理文本序列和环节设置。它接收来自文本内容节点的输入，生成序列数据，并配置播放环节。

## 节点参数

文本序列节点具有以下参数：

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `sequence` | 序列数据数组 | `[]` |
| `sections` | 环节设置数组 | `[]` |

### 序列数据结构

序列数据是一个数组，每个元素表示一个内容项：

```javascript
{
  id: "seq_abc123",       // 序列项ID
  sourceId: "seg_abc123", // 源分句ID
  content: "今日は良い天気ですね。", // 文本内容
  language: "ja",         // 语言代码
  type: "text"            // 内容类型
}
```

### 环节设置结构

环节设置是一个数组，每个元素表示一个播放环节：

```javascript
{
  id: "section_read_123",      // 环节ID
  title: "通读环节: 基于序列",   // 环节标题
  type: "readthrough",         // 环节类型: readthrough(通读) 或 repeat(重复)
  description: "默认通读环节",  // 环节描述
  processingMode: "sequence",  // 处理方式: sequence(基于序列) 或 source(基于源节点)
  sourceNodeId: null,          // 源节点ID(仅当processingMode为source时有效)
  userEditable: true,          // 是否允许用户编辑
  enabled: true,               // 是否启用
  config: {                    // 环节配置
    speed: 1.0,                // 播放速度
    interval: 1000             // 内容项之间的间隔时长(毫秒)
  }
}
```

## 环节类型

### 通读环节 (readthrough)

通读环节按顺序播放内容，每个内容项播放一次。

#### 配置参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `speed` | 播放速度，范围: 0.5-2.0 | 1.0 |
| `interval` | 内容项之间的间隔时长(毫秒) | 1000 |

### 重复环节 (repeat)

重复环节重复播放每个内容项，可以在最后一次重复后插入翻译。

#### 配置参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `speed` | 播放速度，范围: 0.5-2.0 | 1.0 |
| `interval` | 内容项之间的间隔时长(毫秒) | 1000 |
| `repeatCount` | 重复次数 | 3 |
| `enableTranslation` | 是否启用翻译 | `false` |
| `translationLanguage` | 翻译语言代码，如 `zh-CN`, `en-US` | `""` (空字符串) |
| `translationPosition` | 翻译插入位置，表示在第几次重复后插入翻译 | `2` |

## 处理方式

### 基于序列 (sequence)

- 使用序列中的所有内容项
- 不需要选择特定的源节点
- 适用于需要处理整个序列的场景

### 基于源节点 (source)

- 只使用特定源节点的内容
- 需要明确选择一个源节点
- 适用于需要处理特定内容的场景

## 节点方法

### 更新序列

根据源节点更新序列数据。

```javascript
// 更新序列方法
function updateSequence() {
  // 获取源节点
  const sourceNodes = this.getSourceNodes();

  // 如果没有源节点，清空序列
  if (sourceNodes.length === 0) {
    this.params.sequence = [];
    return;
  }

  // 合并所有源节点的分句
  const allSegments = [];

  sourceNodes.forEach(node => {
    if (node.type === 'textContent' && node.params.segments) {
      node.params.segments.forEach(segment => {
        allSegments.push({
          id: `seq_${nanoid()}`,
          sourceId: segment.id,
          content: segment.content,
          language: segment.language || 'auto',
          type: 'text'
        });
      });
    }
  });

  // 更新序列
  this.params.sequence = allSegments;
}
```

### 添加环节

添加新的环节设置。

```javascript
// 添加环节方法
function addSection(type, processingMode = 'sequence', sourceNodeId = null) {
  // 创建环节ID
  const sectionId = `section_${type}_${nanoid()}`;

  // 创建环节标题
  let title;
  if (type === 'readthrough') {
    title = processingMode === 'sequence' ? '通读环节: 基于序列' : `通读环节: ${this.getNodeName(sourceNodeId)}`;
  } else {
    title = processingMode === 'sequence' ? '重复环节: 基于序列' : `重复环节: ${this.getNodeName(sourceNodeId)}`;
  }

  // 创建环节配置
  const config = {
    speed: 1.0,
    interval: 1000
  };

  // 如果是重复环节，添加重复相关配置
  if (type === 'repeat') {
    config.repeatCount = 3;
    config.enableTranslation = true;
    config.translationLanguage = 'zh-CN';
    config.translationPosition = 2;
  }

  // 创建环节
  const section = {
    id: sectionId,
    title,
    type,
    description: type === 'readthrough' ? '默认通读环节' : '重复练习环节',
    processingMode,
    sourceNodeId: processingMode === 'source' ? sourceNodeId : null,
    userEditable: true,
    enabled: true,
    config
  };

  // 添加到环节数组
  this.params.sections.push(section);

  return section;
}
```

### 更新环节

更新环节设置。

```javascript
// 更新环节方法
function updateSection(sectionId, updates) {
  // 查找环节
  const sectionIndex = this.params.sections.findIndex(s => s.id === sectionId);

  if (sectionIndex === -1) {
    return;
  }

  // 更新环节
  this.params.sections[sectionIndex] = {
    ...this.params.sections[sectionIndex],
    ...updates
  };
}
```

### 删除环节

删除环节设置。

```javascript
// 删除环节方法
function removeSection(sectionId) {
  // 过滤掉要删除的环节
  this.params.sections = this.params.sections.filter(s => s.id !== sectionId);
}
```

### 移动环节

调整环节顺序。

```javascript
// 移动环节方法
function moveSection(sectionId, direction) {
  // 查找环节索引
  const sectionIndex = this.params.sections.findIndex(s => s.id === sectionId);

  if (sectionIndex === -1) {
    return;
  }

  // 计算新索引
  let newIndex;
  if (direction === 'up') {
    newIndex = Math.max(0, sectionIndex - 1);
  } else {
    newIndex = Math.min(this.params.sections.length - 1, sectionIndex + 1);
  }

  // 如果索引没有变化，不做任何操作
  if (newIndex === sectionIndex) {
    return;
  }

  // 移动环节
  const section = this.params.sections[sectionIndex];
  this.params.sections.splice(sectionIndex, 1);
  this.params.sections.splice(newIndex, 0, section);
}
```

## 节点 UI

文本序列节点的 UI 包括序列列表和环节设置。

```vue
<!-- 文本序列节点 UI -->
<template>
  <div class="text-sequence-node">
    <div class="sequence-list">
      <h3>序列内容</h3>
      <div
        v-for="item in sequence"
        :key="item.id"
        class="sequence-item"
      >
        <div class="item-content">{{ item.content }}</div>
        <div class="item-language">{{ item.language }}</div>
      </div>
    </div>
    <div class="sections-list">
      <h3>环节设置</h3>
      <div class="add-section">
        <button @click="addReadthroughSection">添加通读环节</button>
        <button @click="addRepeatSection">添加重复环节</button>
      </div>
      <div
        v-for="section in sections"
        :key="section.id"
        class="section-item"
        :class="{ 'disabled': !section.enabled }"
      >
        <div class="section-header">
          <div class="section-title">{{ section.title }}</div>
          <div class="section-actions">
            <button @click="toggleSection(section.id)">
              {{ section.enabled ? '禁用' : '启用' }}
            </button>
            <button @click="moveSection(section.id, 'up')">↑</button>
            <button @click="moveSection(section.id, 'down')">↓</button>
            <button @click="removeSection(section.id)">×</button>
          </div>
        </div>
        <div class="section-content">
          <div class="section-type">
            类型: {{ section.type === 'readthrough' ? '通读' : '重复' }}
          </div>
          <div class="section-mode">
            处理方式: {{ section.processingMode === 'sequence' ? '基于序列' : '基于源节点' }}
          </div>
          <div v-if="section.processingMode === 'source'" class="section-source">
            源节点: {{ getNodeName(section.sourceNodeId) }}
          </div>
          <div class="section-config">
            <div class="config-item">
              <label>播放速度:</label>
              <input
                type="range"
                min="0.5"
                max="2"
                step="0.1"
                v-model.number="section.config.speed"
                @input="updateSection(section.id, { config: { ...section.config } })"
              />
              <span>{{ section.config.speed.toFixed(1) }}</span>
            </div>
            <div class="config-item">
              <label>间隔时长(毫秒):</label>
              <input
                type="number"
                min="0"
                max="5000"
                step="100"
                v-model.number="section.config.interval"
                @input="updateSection(section.id, { config: { ...section.config } })"
              />
            </div>
            <div v-if="section.type === 'repeat'" class="config-item">
              <label>重复次数:</label>
              <input
                type="number"
                min="1"
                max="10"
                v-model.number="section.config.repeatCount"
                @input="updateSection(section.id, { config: { ...section.config } })"
              />
            </div>
            <div v-if="section.type === 'repeat'" class="config-item">
              <label>启用翻译:</label>
              <input
                type="checkbox"
                v-model="section.config.enableTranslation"
                @change="updateSection(section.id, { config: { ...section.config } })"
              />
            </div>
            <div v-if="section.type === 'repeat' && section.config.enableTranslation" class="config-item">
              <label>翻译语言:</label>
              <select
                v-model="section.config.translationLanguage"
                @change="updateSection(section.id, { config: { ...section.config } })"
              >
                <option value="zh-CN">中文</option>
                <option value="en-US">英语</option>
                <option value="ko-KR">韩语</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
```

## 节点示例

以下是一个文本序列节点的示例：

```javascript
// 文本序列节点示例
const textSequenceNode = {
  id: "textSequence_456def",
  type: "textSequence",
  customName: "日语序列",
  params: {
    sequence: [
      {
        id: "seq_abc123",
        sourceId: "seg_abc123",
        content: "今日は良い天気ですね。",
        language: "ja",
        type: "text"
      },
      {
        id: "seq_def456",
        sourceId: "seg_def456",
        content: "日本語の勉強は楽しいです。",
        language: "ja",
        type: "text"
      }
    ],
    sections: [
      {
        id: "section_read_123",
        title: "通读环节: 基于序列",
        type: "readthrough",
        description: "默认通读环节",
        processingMode: "sequence",
        userEditable: true,
        enabled: true,
        config: {
          speed: 1.0,
          interval: 1000
        }
      },
      {
        id: "section_repeat_456",
        title: "重复环节: 基于序列",
        type: "repeat",
        description: "重复练习环节",
        processingMode: "sequence",
        userEditable: true,
        enabled: true,
        config: {
          speed: 1.0,
          interval: 1000,
          repeatCount: 3,
          enableTranslation: true,
          translationLanguage: "zh-CN"
        }
      }
    ]
  },
  sourceIds: ["textContent_123abc"],
  position: { x: 300, y: 100 }
};
```

## 最佳实践

1. **环节组合**:
   - 通常先添加通读环节，再添加重复环节
   - 通读环节使用基于序列模式，处理所有内容
   - 重复环节使用基于源节点模式，针对特定内容进行重复练习

2. **翻译设置**:
   - 只在重复环节中启用翻译
   - 确保已在资源管理节点中添加了对应语言的翻译
   - 翻译语言应与目标学习者的母语匹配

3. **播放速度**:
   - 初学者: 使用较慢的速度(0.8-0.9)
   - 中级学习者: 使用正常速度(1.0)
   - 高级学习者: 使用较快的速度(1.1-1.2)

4. **重复次数**:
   - 简单内容: 2-3次重复
   - 复杂内容: 3-5次重复
   - 难度较高的内容: 5次以上重复
