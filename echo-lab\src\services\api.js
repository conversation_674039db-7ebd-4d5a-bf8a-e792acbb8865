/**
 * API服务
 * 提供业务相关的API调用方法
 * 使用统一的HTTP客户端
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";
import { TRANSLATION_TIMEOUT } from "@/config/translation";

// 翻译服务
export const translateService = {
  /**
   * 检测文本语言
   * @param {string} text - 要检测的文本
   * @returns {string} 检测到的语言代码
   */
  detectLanguage(text) {
    // 简单的语言检测逻辑，只支持英语、日语、简体中文和繁体中文
    if (!text) return "auto";

    // 日语特征检测 (假名)
    const japaneseRegex = /[\u3040-\u309F\u30A0-\u30FF]/;
    if (japaneseRegex.test(text)) {
      return "ja";
    }

    // 中文特征检测
    const chineseRegex = /[\u4E00-\u9FFF]/;
    if (chineseRegex.test(text)) {
      // 简体中文和繁体中文的区分 (简单判断，不完全准确)
      // 繁体中文特有字符
      const traditionalChineseChars = /[馬車風電開關燈語國門時東西南北]/;
      if (traditionalChineseChars.test(text)) {
        return "zh-TW";
      }
      return "zh-CN";
    }

    // 默认为英语
    return "en";
  },

  /**
   * 批量翻译文本
   * @param {Array} texts - 要翻译的文本数组，每个元素包含 id, content, language
   * @param {Array} targetLanguages - 目标语言数组
   * @returns {Promise} 翻译结果
   */
  async translateTexts(texts, targetLanguages) {
    try {
      // 确保每个文本都有语言标识
      const textsWithLanguage = texts.map((text) => {
        if (text.language && text.language !== "auto") {
          return text;
        }
        // 如果没有语言标识或为auto，进行语言检测
        return {
          ...text,
          language: this.detectLanguage(text.content),
        };
      });

      console.log("发送翻译请求:", {
        texts: textsWithLanguage,
        targetLanguages,
      });
      const response = await httpClient.post(
        API_ENDPOINTS.TEXT_PROCESSING.TRANSLATE,
        {
          texts: textsWithLanguage,
          targetLanguages,
        },
        {
          timeout: TRANSLATION_TIMEOUT.FRONTEND_REQUEST, // 使用配置的翻译超时时间
        }
      );
      console.log("翻译响应:", response);
      return response;
    } catch (error) {
      console.error("翻译请求失败:", error);
      throw error;
    }
  },
};

// 标注服务
export const annotateService = {
  /**
   * 批量标注文本
   * @param {Array} segments - 要标注的文本段落数组，每个元素包含 id, content, language
   * @param {String} language - 文本语言
   * @returns {Promise} 标注结果
   */
  async annotateTexts(segments, language) {
    try {
      // 确保每个段落都有语言标识
      const segmentsWithLanguage = segments.map((segment) => {
        if (segment.language && segment.language !== "auto") {
          return segment;
        }
        // 如果没有语言标识或为auto，使用传入的language参数或进行语言检测
        return {
          ...segment,
          language:
            language !== "auto"
              ? language
              : translateService.detectLanguage(segment.content),
        };
      });

      console.log("发送标注请求:", {
        segments: segmentsWithLanguage,
        language,
      });
      const response = await httpClient.post(
        API_ENDPOINTS.TEXT_PROCESSING.ANNOTATE,
        {
          segments: segmentsWithLanguage,
          language,
        }
      );
      console.log("标注响应:", response);
      return response;
    } catch (error) {
      console.error("标注请求失败:", error);
      throw error;
    }
  },
};

// 音频服务
export const audioService = {
  /**
   * 批量生成音频
   * @param {Array} items - 要生成的音频项数组，每个元素包含 id, text, language, speaker, speed
   * @returns {Promise} 生成结果
   */
  async generateAudios(items) {
    try {
      console.log("发送音频生成请求:", items);
      const response = await httpClient.post(API_ENDPOINTS.AUDIO.TTS_BATCH, {
        items: items,
      });
      console.log("音频生成响应:", response);
      return response;
    } catch (error) {
      console.error("音频生成请求失败:", error);
      throw error;
    }
  },

  /**
   * 生成单个音频
   * @param {Object} item - 要生成的音频项，包含 id, text, language, speaker, speed
   * @returns {Promise} 生成结果
   */
  async generateAudio(item) {
    try {
      console.log("发送单个音频生成请求:", item);
      const response = await httpClient.post(API_ENDPOINTS.AUDIO.TTS_BATCH, {
        items: [item],
      });
      console.log("单个音频生成响应:", response);
      if (response.success && response.results && response.results.length > 0) {
        return response.results[0];
      }
      throw new Error("音频生成失败");
    } catch (error) {
      console.error("单个音频生成请求失败:", error);
      throw error;
    }
  },

  /**
   * 获取TTS服务信息
   * @returns {Promise} TTS服务信息，包括支持的语言、服务和声音ID
   */
  async getTtsServiceInfo() {
    try {
      const response = await httpClient.get(API_ENDPOINTS.AUDIO.TTS_INFO);
      console.log("获取TTS服务信息响应:", response);
      if (response.success && response.data) {
        return response.data;
      }
      throw new Error("获取TTS服务信息失败");
    } catch (error) {
      console.error("获取TTS服务信息失败:", error);
      throw error;
    }
  },
};

// 导出统一的HTTP客户端和业务服务
export default {
  // HTTP客户端方法
  get: httpClient.get,
  post: httpClient.post,
  put: httpClient.put,
  delete: httpClient.delete,
  upload: httpClient.upload,

  // 业务服务
  translateService,
  annotateService,
  audioService,
};
