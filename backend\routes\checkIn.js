/**
 * 签到路由
 */
const express = require('express');
const router = express.Router();
const { checkIn, getCheckInStats, getCheckInCalendar } = require('../controllers/checkInController');
const { authenticate } = require('../middleware/authMiddleware');

// 所有签到相关路由都需要登录
router.use(authenticate);

// 执行签到
router.post('/', checkIn);

// 获取签到统计
router.get('/stats', getCheckInStats);

// 获取签到日历
router.get('/calendar', getCheckInCalendar);

module.exports = router;