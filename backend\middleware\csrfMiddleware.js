/**
 * CSRF保护中间件
 * 防止跨站请求伪造攻击
 */
const csrf = require('csurf');
const { nanoid } = require('nanoid');

/**
 * 创建CSRF保护中间件
 * @param {Object} options 配置选项
 * @returns {Array} Express中间件数组
 */
function createCsrfProtection(options = {}) {
  const {
    cookie = true, // 使用cookie存储令牌
    ignoreMethods = ['GET', 'HEAD', 'OPTIONS'], // 忽略的HTTP方法
    cookieOptions = {
      httpOnly: true, // 仅HTTP访问
      secure: process.env.NODE_ENV === 'production', // 生产环境使用HTTPS
      sameSite: 'strict', // 严格的同站策略
    },
    value = (req) => {
      // 从请求头或表单中获取令牌
      return (
        req.headers['x-csrf-token'] ||
        req.headers['x-xsrf-token'] ||
        req.body._csrf
      );
    },
  } = options;

  // 创建CSRF中间件
  const csrfProtection = csrf({
    cookie: cookie ? cookieOptions : false,
    ignoreMethods,
    value,
  });

  return [
    // CSRF保护中间件
    csrfProtection,

    // 错误处理中间件
    (err, req, res, next) => {
      if (err.code === 'EBADCSRFTOKEN') {
        // 处理CSRF令牌验证失败
        console.warn(`[CSRF] 令牌验证失败: ${req.method} ${req.originalUrl}, IP: ${req.ip}`);
        return res.status(403).json({
          success: false,
          error: 'CSRF令牌验证失败',
          code: 'INVALID_CSRF_TOKEN',
        });
      }
      next(err);
    },

    // 为API响应添加CSRF令牌
    (req, res, next) => {
      // 将CSRF令牌添加到响应头
      res.cookie('XSRF-TOKEN', req.csrfToken(), {
        ...cookieOptions,
        httpOnly: false, // 允许JavaScript访问
      });
      next();
    },
  ];
}

// 预定义的CSRF保护中间件
const csrfProtection = createCsrfProtection();

// 获取CSRF令牌的路由处理函数
function getCsrfToken(req, res) {
  res.json({
    success: true,
    csrfToken: req.csrfToken(),
  });
}

module.exports = {
  csrfProtection,
  getCsrfToken,
};
