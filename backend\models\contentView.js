/**
 * 内容观看记录模型
 */
module.exports = (sequelize, DataTypes) => {
  const ContentView = sequelize.define(
    "ContentView",
    {
      contentId: {
        type: DataTypes.STRING(21),
        allowNull: false,
        field: "content_id",
      },
      userId: {
        type: DataTypes.STRING(50),
        allowNull: true,
        field: "user_id",
      },
      ipAddress: {
        type: DataTypes.STRING(45),
        allowNull: true,
        field: "ip_address",
      },
      viewDuration: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        field: "view_duration",
      },
    },
    {
      tableName: "content_views",
      underscored: true,
      timestamps: true,
      createdAt: "created_at",
      updatedAt: false,
    }
  );

  ContentView.associate = function (models) {
    ContentView.belongsTo(models.Content, {
      foreignKey: "contentId",
      as: "content",
    });
  };

  return ContentView;
};