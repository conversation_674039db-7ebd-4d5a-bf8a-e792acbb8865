"use strict";

/**
 * 添加用户等级系统相关表
 */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // 1. 创建用户等级表
      await queryInterface.createTable(
        "user_levels",
        {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
          },
          level: {
            type: Sequelize.INTEGER,
            allowNull: false,
            comment: "等级数值",
          },
          name: {
            type: Sequelize.STRING(100),
            allowNull: false,
            comment: "等级名称",
          },
          description: {
            type: Sequelize.TEXT,
            allowNull: true,
            comment: "等级描述",
          },
          is_default: {
            type: Sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: "是否为默认等级",
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updated_at: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction }
      );

      // 添加唯一索引
      await queryInterface.addIndex("user_levels", ["level"], {
        unique: true,
        name: "idx_user_levels_level",
        transaction,
      });

      // 2. 添加用户等级字段到用户表
      await queryInterface.addColumn(
        "users",
        "level",
        {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: "用户等级: 0=免费用户, 1=基础会员, 2=高级会员, 3=专业会员",
        },
        { transaction }
      );

      // 3. 创建用户订阅表
      await queryInterface.createTable(
        "user_subscriptions",
        {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
          },
          user_id: {
            type: Sequelize.STRING(21),
            allowNull: false,
            comment: "用户ID",
          },
          level: {
            type: Sequelize.INTEGER,
            allowNull: false,
            comment: "订阅等级",
          },
          start_date: {
            type: Sequelize.DATE,
            allowNull: false,
            comment: "开始日期",
          },
          end_date: {
            type: Sequelize.DATE,
            allowNull: true,
            comment: "结束日期，NULL表示永久",
          },
          status: {
            type: Sequelize.ENUM("active", "expired", "cancelled"),
            allowNull: false,
            defaultValue: "active",
            comment: "订阅状态",
          },
          payment_id: {
            type: Sequelize.STRING(100),
            allowNull: true,
            comment: "支付ID",
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updated_at: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction }
      );

      // 添加索引
      await queryInterface.addIndex("user_subscriptions", ["user_id"], {
        name: "idx_user_subscriptions_user_id",
        transaction,
      });
      await queryInterface.addIndex("user_subscriptions", ["level"], {
        name: "idx_user_subscriptions_level",
        transaction,
      });
      await queryInterface.addIndex("user_subscriptions", ["status"], {
        name: "idx_user_subscriptions_status",
        transaction,
      });
      await queryInterface.addIndex("user_subscriptions", ["end_date"], {
        name: "idx_user_subscriptions_end_date",
        transaction,
      });

      // 4. 创建等级权限表
      await queryInterface.createTable(
        "level_permissions",
        {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
          },
          level: {
            type: Sequelize.INTEGER,
            allowNull: false,
            comment: "用户等级",
          },
          feature_key: {
            type: Sequelize.STRING(50),
            allowNull: false,
            comment: "功能标识符",
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updated_at: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction }
      );

      // 添加唯一索引
      await queryInterface.addIndex(
        "level_permissions",
        ["level", "feature_key"],
        {
          unique: true,
          name: "idx_level_permissions_level_feature",
          transaction,
        }
      );

      // 5. 创建功能使用限制表
      await queryInterface.createTable(
        "feature_usage_limits",
        {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
          },
          level: {
            type: Sequelize.INTEGER,
            allowNull: false,
            comment: "用户等级",
          },
          feature_key: {
            type: Sequelize.STRING(50),
            allowNull: false,
            comment: "功能标识符",
          },
          daily_limit: {
            type: Sequelize.INTEGER,
            allowNull: true,
            comment: "每日使用限制，NULL表示无限制",
          },
          monthly_limit: {
            type: Sequelize.INTEGER,
            allowNull: true,
            comment: "每月使用限制，NULL表示无限制",
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          updated_at: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction }
      );

      // 添加唯一索引
      await queryInterface.addIndex(
        "feature_usage_limits",
        ["level", "feature_key"],
        {
          unique: true,
          name: "idx_feature_usage_limits_level_feature",
          transaction,
        }
      );

      // 6. 创建功能使用记录表
      await queryInterface.createTable(
        "feature_usage_records",
        {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true,
          },
          user_id: {
            type: Sequelize.STRING(21),
            allowNull: false,
            comment: "用户ID",
          },
          feature_key: {
            type: Sequelize.STRING(50),
            allowNull: false,
            comment: "功能标识符",
          },
          used_at: {
            type: Sequelize.DATE,
            allowNull: false,
            comment: "使用时间",
          },
          created_at: {
            type: Sequelize.DATE,
            allowNull: false,
          },
        },
        { transaction }
      );

      // 添加索引
      await queryInterface.addIndex(
        "feature_usage_records",
        ["user_id", "feature_key"],
        {
          name: "idx_feature_usage_records_user_feature",
          transaction,
        }
      );
      await queryInterface.addIndex("feature_usage_records", ["used_at"], {
        name: "idx_feature_usage_records_used_at",
        transaction,
      });

      // 提交事务
      await transaction.commit();
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // 删除表和字段（按创建的相反顺序）
      await queryInterface.dropTable("feature_usage_records", { transaction });
      await queryInterface.dropTable("feature_usage_limits", { transaction });
      await queryInterface.dropTable("level_permissions", { transaction });
      await queryInterface.dropTable("user_subscriptions", { transaction });
      await queryInterface.removeColumn("users", "level", { transaction });
      await queryInterface.dropTable("user_levels", { transaction });

      // 提交事务
      await transaction.commit();
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  },
};
