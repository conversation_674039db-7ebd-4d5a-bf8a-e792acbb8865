# 视频工厂重构设计文档

## 1. 概述

本文档描述了视频工厂系统的完整重构设计，采用声明式配置和运行时生成的架构。新设计不考虑与旧代码的兼容性，而是专注于创建一个清晰、高效、易于维护的系统。

## 2. 设计原则

1. **声明式配置**：节点只存储配置，不存储生成的数据
2. **运行时生成**：数据在运行时根据配置生成，不预先计算
3. **明确分离**：配置与生成数据完全分离
4. **简化错误处理**：假设输入符合预期，减少防御性代码
5. **清晰的责任边界**：每个组件有明确的职责，避免职责混淆

## 3. 核心架构

### 3.1 节点系统

节点是系统的基本构建块，每个节点都是一个声明式配置对象：

```javascript
{
  "id": "node_123",        // 节点唯一标识符
  "type": "nodeType",      // 节点类型
  "sourceIds": ["node_1"], // 源节点ID数组
  "params": {              // 节点参数
    // 特定于节点类型的参数
  },
  "patches": {             // 用户修改（可选）
    // 特定于节点类型的修改
  }
}
```

### 3.2 节点处理器

每种节点类型都有一个对应的处理器函数，负责处理节点配置并生成结果：

```javascript
function processNode(node, sourceResults) {
  // 根据节点类型和参数处理数据
  // 返回处理结果
}
```

### 3.3 执行引擎

执行引擎负责按照依赖顺序执行节点处理器：

```javascript
function executeNodeGraph(nodes, targetNodeId) {
  // 构建依赖图
  const dependencyGraph = buildDependencyGraph(nodes);
  
  // 拓扑排序，确定执行顺序
  const executionOrder = topologicalSort(dependencyGraph, targetNodeId);
  
  // 按顺序执行节点处理器
  const results = {};
  for (const nodeId of executionOrder) {
    const node = nodes.find(n => n.id === nodeId);
    const sourceData = node.sourceIds.map(id => results[id]);
    results[nodeId] = processNode(node, sourceData);
  }
  
  // 返回目标节点的结果
  return results[targetNodeId];
}
```

### 3.4 资源池

资源池用于存储和管理音频资源，避免重复存储：

```javascript
{
  "resources": {
    "resource_123": {
      "type": "audio",
      "content": "こんにちは",
      "language": "ja",
      "speed": 1.0,
      "url": "https://example.com/audio.mp3",
      "duration": 1.5
    }
  }
}
```

### 3.5 播放配置

播放配置定义了内容的播放顺序和方式：

```javascript
{
  "playbackSteps": [
    {
      "id": "step_1",
      "label": "通读一遍",
      "nodeId": "sequence_1",
      "visible": true
    },
    {
      "id": "step_2",
      "label": "重复练习",
      "nodeId": "sequence_2",
      "visible": true
    }
  ],
  "displayLanguages": ["ja", "zh-CN"],
  "repeatSettings": {
    "count": 3,
    "translationAfter": 1
  }
}
```

## 4. 节点类型定义

### 4.1 文本节点 (TextNode)

**功能**：提供原始文本内容

**配置**：
```javascript
{
  "id": "text_1",
  "type": "text",
  "params": {
    "text": "こんにちは。ありがとう。",
    "language": "ja"
  }
}
```

### 4.2 分句节点 (SentenceNode)

**功能**：将文本分割成句子或段落

**配置**：
```javascript
{
  "id": "sentence_1",
  "type": "sentence",
  "sourceIds": ["text_1"],
  "params": {
    "mode": "paragraph", // 'paragraph' 或 'period'
    "splitBy": "\n"      // 当mode为'paragraph'时使用
  }
}
```

### 4.3 标注节点 (AnnotateNode)

**功能**：为文本添加标注（如假名、拼音）

**配置**：
```javascript
{
  "id": "annotate_1",
  "type": "annotate",
  "sourceIds": ["sentence_1"],
  "params": {
    "method": "furigana",
    "autoAnnotate": true
  },
  "patches": {
    "0": { "reading": "コンニチワ" },
    "1": { "text": "ありがとう", "reading": "アリガトウ" }
  }
}
```

### 4.4 翻译节点 (TranslateNode)

**功能**：将文本翻译为多种目标语言

**配置**：
```javascript
{
  "id": "translate_1",
  "type": "translate",
  "sourceIds": ["sentence_1"],
  "params": {
    "targets": ["zh-CN", "en"],
    "autoTranslate": true
  },
  "patches": {
    "zh-CN": {
      "0": "你好啊",
      "1": "非常感谢"
    },
    "en": {
      "1": "Thank you very much"
    }
  }
}
```

### 4.5 文本序列节点 (TextSequenceNode)

**功能**：生成文本播放序列，支持通读和重复模式

**配置**：
```javascript
{
  "id": "sequence_1",
  "type": "textSequence",
  "sourceIds": ["annotate_1", "translate_1"],
  "params": {
    "mode": "repeat",           // 'repeat' 或 'read-through'
    "repeatCount": 3,           // 重复模式下的重复次数
    "translationAfter": 1,      // 在第几次重复后插入翻译
    "translationLanguage": "zh-CN", // 翻译语言
    "interval": 1.0,            // 重复间隔（秒）
    "sentenceInterval": 2.0     // 句子间隔（秒）
  }
}
```

### 4.6 音频节点 (AudioNode)

**功能**：生成文本的语音音频

**配置**：
```javascript
{
  "id": "audio_1",
  "type": "audio",
  "sourceIds": ["sequence_1"],
  "params": {
    "voice": "ja_female"
  }
}
```

### 4.7 视频配置节点 (VideoConfigNode)

**功能**：配置视频参数

**配置**：
```javascript
{
  "id": "videoConfig_1",
  "type": "videoConfig",
  "params": {
    "background": {
      "color": "rgba(0, 0, 0, 1)"
    },
    "copyright": {
      "text": "© 2023 All Rights Reserved",
      "position": "bottomRight",
      "size": 0.625,
      "color": "#FFFFFF"
    },
    "subtitleStyle": {
      "fontSize": 1.25,
      "color": "#FFFFFF",
      "background": "rgba(0, 0, 0, 0.5)",
      "position": "bottom"
    }
  }
}
```

### 4.8 播放步骤节点 (PlaybackStepsNode)

**功能**：定义播放步骤和顺序

**配置**：
```javascript
{
  "id": "playbackSteps_1",
  "type": "playbackSteps",
  "params": {
    "steps": [
      {
        "id": "step_1",
        "label": "通读一遍",
        "nodeId": "audio_1",
        "visible": true
      },
      {
        "id": "step_2",
        "label": "重复练习",
        "nodeId": "audio_2",
        "visible": true
      }
    ]
  }
}
```

## 5. 用户配置界面

用户配置界面只暴露有限的配置选项，简化用户体验：

### 5.1 播放步骤配置

允许用户调整播放步骤的顺序、可见性和删除步骤。

### 5.2 重复设置配置

允许用户调整重复次数和翻译位置。

### 5.3 语言选择配置

允许用户从预生成的语言中选择显示语言。

## 6. 状态管理

### 6.1 节点存储

使用Pinia管理节点状态，包括节点数据、位置信息和处理结果缓存。

### 6.2 配置存储

使用Pinia管理用户配置，包括基础配置和用户配置。

### 6.3 资源池存储

使用Pinia管理资源池，包括音频资源的添加、获取和删除。

## 7. 数据流程

### 7.1 运营配置流程

1. 运营人员创建完整的节点网络
2. 设置预生成的语言和可用语言
3. 配置默认的播放步骤和顺序
4. 导出完整的JSON配置

### 7.2 用户配置流程

1. 加载运营配置的JSON
2. 用户调整播放步骤、重复设置和语言选择
3. 点击"应用配置"按钮
4. 系统应用用户配置，生成新的播放数据
5. 更新预览

### 7.3 播放流程

1. 根据配置生成播放数据
2. 加载所有需要的音频资源
3. 按照播放步骤顺序播放内容
4. 支持暂停、跳转等操作

## 8. 多语言音频生成

多语言音频生成时机由运营控制，而不是用户：

1. 运营创建内容时，系统自动为预设语言生成音频
2. 这些预生成的音频URL保存在JSON中
3. 用户只能从可用语言中选择已有的语言
4. 用户选择语言不会触发新的音频生成，只是选择显示哪些已生成的内容

```javascript
// 运营配置中添加预生成语言设置
{
  "configuration": {
    "preGeneratedLanguages": ["ja", "zh-CN"], // 预先生成这些语言的音频
    "availableLanguages": ["ja", "zh-CN", "en"], // 用户可选的语言
    // 其他配置...
  }
}
```

## 9. 标注和翻译的错误修正

自动生成内容的手动修正机制：

1. 为每个自动生成内容的节点添加patches字段
2. patches存储用户的手动修改，与自动生成内容分开
3. 最终结果 = 自动内容 + 用户修改

标注节点实现：
```javascript
{
  "id": "annotate1",
  "type": "annotate",
  "sourceIds": ["split1"],
  "params": {
    "method": "furigana"
  },
  "patches": {
    "1": { "text": "ありがとう", "reading": "アリガトウ" },
    "3": { "reading": "コンニチワ" }
  }
}
```

翻译节点实现：
```javascript
{
  "id": "translate1",
  "type": "translate",
  "sourceIds": ["split1"],
  "params": {
    "targets": ["zh-CN", "en"]
  },
  "patches": {
    "zh-CN": { 
      "1": "谢谢你", 
      "2": "你好" 
    },
    "en": { 
      "2": "Hello there" 
    }
  }
}
```

## 10. 用户配置暴露

用户配置界面设计：

1. **暴露的配置项**：
   - **播放步骤**：排序、显示/隐藏、删除
   - **重复设置**：重复次数、是否包含翻译
   - **语言选择**：从预生成的语言中选择显示哪些语言

2. **不暴露的配置项**：
   - **全局速度**：不暴露给用户，因为需要重新生成音频文件

## 11. 实现注意事项

1. 不考虑兼容性，直接按照新设计实现
2. 代码编写时不要过度兼容，出错提示，让人知道错在哪就行
3. 尽量以所有的数据都是符合预期进行处理，避免到处catch和判断
4. 代码应该清晰易读，避免复杂的防御性编程

## 12. 节点系统重构

### 保留的节点

1. **文本节点 (TextNode)**
2. **分句节点 (SentenceNode)**
3. **标注节点 (AnnotationNode)**
4. **翻译节点 (TranslationNode)**
5. **音频节点 (AudioNode)**

### 需要改造的节点

1. **文本序列节点 (TextSequenceNode)**
   - 转为纯配置节点，只存储模式和参数
   - 序列生成逻辑移至运行时执行
   - 分离重复模式和通读模式的配置

2. **视频节点 (VideoNode)**
   - 拆分为"视频配置节点"和"播放配置节点"
   - 视频配置节点只负责视频参数（背景、字幕样式等）
   - 播放配置节点负责播放顺序、步骤等

### 新增的节点

1. **播放步骤节点 (PlaybackStepNode)**
2. **资源池节点 (ResourcePoolNode)**

### 可能移除的节点

1. **富文本节点 (RichTextNode)**
   - 富文本功能可以集成到标注节点和翻译节点中
