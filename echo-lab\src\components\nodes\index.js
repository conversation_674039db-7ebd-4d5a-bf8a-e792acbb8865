/**
 * 节点组件索引
 * 导出所有节点组件
 */

import BaseNode from "./BaseNode.vue";
import TextContentNode from "./TextContentNode.vue";
import TextSequenceNode from "./TextSequenceNode.vue";
import VideoConfigNode from "./VideoConfigNode.vue";
import ResourceNode from "./ResourceNode.vue";

// 节点组件映射
export const nodeComponents = {
  textContent: TextContentNode,
  textSequence: TextSequenceNode,
  videoConfig: VideoConfigNode,
  resource: ResourceNode,
};

// 注册节点组件
export function registerNodeComponents(app) {
  app.component("BaseNode", BaseNode);

  Object.entries(nodeComponents).forEach(([type, component]) => {
    app.component(
      `${type.charAt(0).toUpperCase() + type.slice(1)}Node`,
      component
    );
  });
}

export default nodeComponents;
