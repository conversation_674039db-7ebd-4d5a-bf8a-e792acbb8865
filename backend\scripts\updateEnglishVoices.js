/**
 * 更新英语声音配置
 * 只修改英语声音，不影响其他语言
 */

const db = require("../models");

// 新的英语声音配置
const newEnglishVoices = [
  {
    speaker_id: "en-US-Chirp3-HD-Achird",
    service_id: "google",
    language_code: "en",
    name: "男声A",
    gender: "male",
    is_premium: true,
    disabled: false,
    api_params: { languageCode: "en-US" },
  },
  {
    speaker_id: "en-US-Chirp3-HD-Achernar",
    service_id: "google",
    language_code: "en",
    name: "女声A",
    gender: "female",
    is_premium: true,
    disabled: false,
    api_params: { languageCode: "en-US" },
  },
  {
    speaker_id: "en-US-Chirp3-HD-Algenib",
    service_id: "google",
    language_code: "en",
    name: "男声B",
    gender: "male",
    is_premium: true,
    disabled: false,
    api_params: { languageCode: "en-US" },
  },
  {
    speaker_id: "en-US-Chirp3-HD-Aoede",
    service_id: "google",
    language_code: "en",
    name: "女声B",
    gender: "female",
    is_premium: true,
    disabled: false,
    api_params: { languageCode: "en-US" },
  },
];

/**
 * 更新英语声音配置
 */
async function updateEnglishVoices() {
  try {
    console.log("开始更新英语声音配置...");

    // 1. 查询现有的英语声音记录
    const existingEnglishVoices = await db.Voice.findAll({
      where: { language_code: "en" },
    });

    console.log(`找到 ${existingEnglishVoices.length} 个现有英语声音记录`);

    // 2. 删除现有的英语声音记录
    if (existingEnglishVoices.length > 0) {
      await db.Voice.destroy({
        where: { language_code: "en" },
      });
      console.log("已删除现有英语声音记录");
    }

    // 3. 插入新的英语声音记录
    await db.Voice.bulkCreate(newEnglishVoices);
    console.log(`已创建 ${newEnglishVoices.length} 个新英语声音记录`);

    // 4. 验证更新结果
    const updatedVoices = await db.Voice.findAll({
      where: { language_code: "en" },
      order: [["speaker_id", "ASC"]],
    });

    console.log("\n✅ 更新后的英语声音列表:");
    updatedVoices.forEach((voice, index) => {
      console.log(
        `  ${index + 1}. ${voice.speaker_id} - ${voice.name} (${voice.gender})`
      );
    });

    console.log("\n🎉 英语声音配置更新完成!");
    return true;
  } catch (error) {
    console.error("更新英语声音配置失败:", error);
    return false;
  }
}

/**
 * 查看当前所有声音配置
 */
async function showAllVoices() {
  try {
    const allVoices = await db.Voice.findAll({
      order: [["language_code", "ASC"], ["speaker_id", "ASC"]],
    });

    console.log("\n📋 当前所有声音配置:");
    
    const groupedVoices = {};
    allVoices.forEach(voice => {
      if (!groupedVoices[voice.language_code]) {
        groupedVoices[voice.language_code] = [];
      }
      groupedVoices[voice.language_code].push(voice);
    });

    Object.entries(groupedVoices).forEach(([lang, voices]) => {
      console.log(`\n${lang.toUpperCase()}:`);
      voices.forEach((voice, index) => {
        console.log(
          `  ${index + 1}. ${voice.speaker_id} - ${voice.name} (${voice.gender})`
        );
      });
    });

    console.log(`\n总计: ${allVoices.length} 个声音配置`);
  } catch (error) {
    console.error("查询声音配置失败:", error);
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  try {
    if (command === "show") {
      // 只查看当前配置
      await showAllVoices();
    } else if (command === "update") {
      // 更新英语声音配置
      const success = await updateEnglishVoices();
      if (success) {
        await showAllVoices();
      }
    } else {
      console.log("使用方法:");
      console.log("  node updateEnglishVoices.js show     # 查看当前所有声音配置");
      console.log("  node updateEnglishVoices.js update   # 更新英语声音配置");
      console.log("");
      console.log("新的英语声音配置:");
      newEnglishVoices.forEach((voice, index) => {
        console.log(`  ${index + 1}. ${voice.speaker_id} - ${voice.name} (${voice.gender})`);
      });
    }
  } catch (error) {
    console.error("执行失败:", error);
  } finally {
    // 关闭数据库连接
    await db.sequelize.close();
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  updateEnglishVoices,
  showAllVoices,
  newEnglishVoices,
};
