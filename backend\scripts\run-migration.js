/**
 * 运行数据库迁移脚本
 * 用于创建播放策略模板表
 */
const { Sequelize } = require('sequelize');
const config = require('../config/database');

// 根据环境选择配置
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// 创建数据库连接
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    logging: console.log
  }
);

async function runMigration() {
  try {
    console.log('开始运行播放策略模板表迁移...');
    
    // 创建播放策略模板表
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS playback_templates (
        id VARCHAR(21) PRIMARY KEY COMMENT '模板ID，使用nanoid',
        name VARCHAR(100) NOT NULL COMMENT '模板名称',
        description TEXT COMMENT '模板描述',
        config JSON NOT NULL COMMENT '模板配置，包含sections数组',
        type ENUM('system', 'user') NOT NULL DEFAULT 'user' COMMENT '模板类型',
        user_id VARCHAR(21) COMMENT '创建者ID，系统模板为NULL',
        is_public BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否公开',
        usage_count INT NOT NULL DEFAULT 0 COMMENT '使用次数统计',
        status ENUM('active', 'archived') NOT NULL DEFAULT 'active' COMMENT '模板状态',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        
        INDEX idx_user_id (user_id),
        INDEX idx_type (type),
        INDEX idx_public (is_public),
        INDEX idx_usage (usage_count),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at),
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON UPDATE CASCADE ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);
    
    console.log('✅ 播放策略模板表创建成功');
    
    // 检查表是否创建成功
    const [results] = await sequelize.query(`
      SHOW TABLES LIKE 'playback_templates'
    `);
    
    if (results.length > 0) {
      console.log('✅ 表结构验证成功');
      
      // 显示表结构
      const [columns] = await sequelize.query(`
        DESCRIBE playback_templates
      `);
      
      console.log('📋 表结构:');
      console.table(columns);
    } else {
      console.error('❌ 表创建失败');
    }
    
  } catch (error) {
    console.error('❌ 迁移失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行迁移
runMigration();
