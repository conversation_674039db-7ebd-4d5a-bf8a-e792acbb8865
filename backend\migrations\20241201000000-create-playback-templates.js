'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('playback_templates', {
      id: {
        type: Sequelize.STRING(21),
        primaryKey: true,
        allowNull: false,
        comment: '模板ID，使用nanoid'
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '模板名称'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '模板描述'
      },
      config: {
        type: Sequelize.JSON,
        allowNull: false,
        comment: '模板配置，包含sections数组'
      },
      type: {
        type: Sequelize.ENUM('system', 'user'),
        allowNull: false,
        defaultValue: 'user',
        comment: '模板类型'
      },
      user_id: {
        type: Sequelize.STRING(21),
        allowNull: true,
        comment: '创建者ID，系统模板为NULL',
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      is_public: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否公开'
      },
      usage_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '使用次数统计'
      },
      status: {
        type: Sequelize.ENUM('active', 'archived'),
        allowNull: false,
        defaultValue: 'active',
        comment: '模板状态'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        comment: '创建时间'
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
        comment: '更新时间'
      }
    });

    // 创建索引
    await queryInterface.addIndex('playback_templates', ['user_id'], {
      name: 'idx_user_id'
    });

    await queryInterface.addIndex('playback_templates', ['type'], {
      name: 'idx_type'
    });

    await queryInterface.addIndex('playback_templates', ['is_public'], {
      name: 'idx_public'
    });

    await queryInterface.addIndex('playback_templates', ['usage_count'], {
      name: 'idx_usage'
    });

    await queryInterface.addIndex('playback_templates', ['status'], {
      name: 'idx_status'
    });

    await queryInterface.addIndex('playback_templates', ['created_at'], {
      name: 'idx_created_at'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('playback_templates');
  }
};
