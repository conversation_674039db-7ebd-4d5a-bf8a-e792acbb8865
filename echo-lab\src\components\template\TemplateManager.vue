<template>
  <div class="template-manager">




    <!-- 筛选和搜索 -->
    <div class="filter-bar">
      <div class="filter-left">
        <el-input v-model="searchKeyword" placeholder="搜索模式名称或描述" prefix-icon="Search" clearable style="width: 320px"
          size="default" />
      </div>

      <div class="filter-right">
        <el-select v-model="filterType" placeholder="模式类型" style="width: 130px" size="default">
          <el-option label="全部类型" value="" />
          <el-option v-if="userStore.isLoggedIn" label="我的模式" value="user" />
          <el-option label="系统模式" value="system" />
          <el-option label="公开模式" value="public" />
        </el-select>

        <el-select v-model="sortBy" placeholder="排序方式" style="width: 130px" size="default">
          <el-option label="创建时间" value="createdAt" />
          <el-option label="模板名称" value="name" />
        </el-select>
      </div>
    </div>

      <!-- 桌面端表格 -->
      <el-table v-if="!isMobile" :data="filteredTemplates" v-loading="templateStore.loading" class="templates-table" stripe border size="default">
        <el-table-column prop="name" label="模式名称" min-width="150">
          <template #default="{ row }">
            <div class="template-name-cell">
              <div class="name-content">
                <el-icon v-if="currentLearningModeId === row.id" class="current-icon">
                  <i-ep-star-filled />
                </el-icon>
                <span class="template-name" :class="{ 'is-current': currentLearningModeId === row.id }">{{ row.name }}</span>
                <el-tag v-if="row.type === 'system'" type="primary" size="small" effect="light">系统</el-tag>
                <el-tag v-else-if="row.isPublic" type="success" size="small" effect="light">公开</el-tag>
                <el-tag v-else type="info" size="small" effect="light">私有</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="180" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="description-text">{{ row.description || '暂无描述' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="环节数" width="80" align="center">
          <template #default="{ row }">
            <el-tag type="info" size="small" effect="plain">{{ row.config?.sections?.length || 0 }}个</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建者" width="80" align="center">
          <template #default="{ row }">
            <span class="creator-text" v-if="row.type === 'system'">系统</span>
            <span class="creator-text" v-else-if="row.creator">{{ row.creator.username }}</span>
            <span class="creator-text" v-else>{{ row.userId === userStore.user?.id ? '我' : '未知' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="100" align="center">
          <template #default="{ row }">
            <span class="date-text">{{ formatDate(row.createdAt) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button size="small" :type="currentLearningModeId === row.id ? 'success' : 'primary'" :plain="currentLearningModeId !== row.id" class="default-btn" @click="toggleLearningMode(row)">
                <el-icon><i-ep-check v-if="currentLearningModeId === row.id" /><i-ep-star v-else /></el-icon>
                <span class="btn-text">{{ currentLearningModeId === row.id ? '默认模式' : '设为默认' }}</span>
              </el-button>
              <div class="action-group">
                <el-tooltip content="预览" placement="top">
                  <el-button size="small" type="primary" text @click="handlePreview(row)" class="action-btn"><el-icon><i-ep-view /></el-icon></el-button>
                </el-tooltip>
                <el-tooltip v-if="userStore.isLoggedIn" content="复制" placement="top">
                  <el-button size="small" type="success" text @click="duplicateTemplate(row)" class="action-btn"><el-icon><i-ep-copy-document /></el-icon></el-button>
                </el-tooltip>
                <el-tooltip v-else content="登录后可复制" placement="top">
                  <el-button size="small" type="success" text @click="promptLogin" class="action-btn" disabled><el-icon><i-ep-copy-document /></el-icon></el-button>
                </el-tooltip>
                <template v-if="userStore.isLoggedIn && row.type === 'user' && row.userId === userStore.user?.id">
                  <el-tooltip content="编辑" placement="top">
                    <el-button size="small" type="primary" text @click="editTemplate(row)" class="action-btn"><el-icon><i-ep-edit /></el-icon></el-button>
                  </el-tooltip>
                  <el-tooltip content="删除" placement="top">
                    <el-button size="small" type="danger" text @click="deleteTemplate(row)" class="action-btn"><el-icon><i-ep-delete /></el-icon></el-button>
                  </el-tooltip>
                </template>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 移动端卡片列表 -->
      <div v-else class="mobile-template-list" v-loading="templateStore.loading">
        <div v-for="row in filteredTemplates" :key="row.id" class="template-card" :class="{ 'is-current': currentLearningModeId === row.id }">
          <div class="card-header">
            <div class="template-info">
              <div class="template-title">
                <el-icon v-if="currentLearningModeId === row.id" class="current-icon"><i-ep-star-filled /></el-icon>
                <span class="template-name" :class="{ 'is-current': currentLearningModeId === row.id }">{{ row.name }}</span>
              </div>
              <div class="template-tags">
                <el-tag v-if="row.type === 'system'" type="primary" size="small">系统</el-tag>
                <el-tag v-else-if="row.isPublic" type="success" size="small">公开</el-tag>
                <el-tag v-else type="info" size="small">私有</el-tag>
                <el-tag type="info" size="small">{{ row.config?.sections?.length || 0 }}个环节</el-tag>
              </div>
            </div>
          </div>
          <div v-if="row.description" class="card-description">{{ row.description }}</div>
          <div class="card-meta">
            <span class="creator">{{ row.type === 'system' ? '系统' : (row.creator?.username || (row.userId === userStore.user?.id ? '我' : '未知')) }}</span>
            <span class="date">{{ formatDate(row.createdAt) }}</span>
          </div>
          <div class="card-actions">
            <el-button size="small" :type="currentLearningModeId === row.id ? 'success' : 'primary'" :plain="currentLearningModeId !== row.id" @click="toggleLearningMode(row)">
              <el-icon><i-ep-check v-if="currentLearningModeId === row.id" /><i-ep-star v-else /></el-icon>
              {{ currentLearningModeId === row.id ? '默认模式' : '设为默认' }}
            </el-button>
            <div class="action-group">
              <el-button size="small" type="primary" text @click="handlePreview(row)"><el-icon><i-ep-view /></el-icon></el-button>
              <el-button v-if="userStore.isLoggedIn" size="small" type="success" text @click="handleDuplicate(row)"><el-icon><i-ep-copy-document /></el-icon></el-button>
              <el-button v-else size="small" type="success" text @click="promptLogin" disabled><el-icon><i-ep-copy-document /></el-icon></el-button>
              <template v-if="userStore.isLoggedIn && row.type === 'user' && row.userId === userStore.user?.id">
                <el-button size="small" type="primary" text @click="handleEdit(row)"><el-icon><i-ep-edit /></el-icon></el-button>
                <el-button size="small" type="danger" text @click="handleDelete(row)"><el-icon><i-ep-delete /></el-icon></el-button>
              </template>
            </div>
          </div>
        </div>
      </div>

    <!-- 空状态 -->
    <div v-if="filteredTemplates.length === 0 && !templateStore.loading" class="empty-state">
      <el-empty description="暂无符合条件的学习模式">
        <el-button type="primary" @click="showCreateDialogLocal = true" v-if="userStore.isLoggedIn">
          <el-icon>
            <i-ep-plus />
          </el-icon>
          创建第一个模式
        </el-button>
        <span v-else class="empty-tip">登录后可以创建和管理学习模式</span>
      </el-empty>
    </div>

    <!-- 统一的模板查看器 -->
    <TemplateViewer v-model="showCreateDialogLocal" :template="editingTemplate" :readonly="false"
      @saved="handleTemplateSaved" />

    <TemplateViewer v-model="showPreviewDialog" :template="previewingTemplate" :readonly="true" />
  </div>
</template>

<script setup>
const props = defineProps({
  showCreateDialog: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:showCreateDialog']);

import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useTemplateStore } from '@/stores/templateStore';
import { useUserStore } from '@/stores/userStore';
import TemplateViewer from './TemplateViewer.vue';
import globalPlaybackService from '@/services/globalPlaybackService';
import { isMobileDevice } from '@/utils/deviceDetector';

// Router and Stores
const router = useRouter();
const templateStore = useTemplateStore();
const userStore = useUserStore();

// State
const searchKeyword = ref('');
const filterType = ref('');
const sortBy = ref('createdAt');

const showCreateDialogLocal = ref(props.showCreateDialog);
const showPreviewDialog = ref(false);
const editingTemplate = ref(null);
const previewingTemplate = ref(null);
// 当前学习模式ID
const currentLearningModeId = ref(globalPlaybackService.getGlobalTemplateId());
// 设备检测
const isMobile = computed(() => isMobileDevice());

// 监听 props 变化
watch(() => props.showCreateDialog, (newVal) => {
  showCreateDialogLocal.value = newVal;
});

// 监听本地状态变化
watch(showCreateDialogLocal, (newVal) => {
  emit('update:showCreateDialog', newVal);
});

// Computed
const filteredTemplates = computed(() => {
  const templates = [...templateStore.allTemplates];

  // 根据类型筛选
  let filtered = templates;
  if (filterType.value === 'user') {
    filtered = templates.filter(t => t.type === 'user' && t.userId === userStore.user?.id);
  } else if (filterType.value === 'system') {
    filtered = templates.filter(t => t.type === 'system');
  } else if (filterType.value === 'public') {
    filtered = templates.filter(t => t.type === 'user' && t.isPublic && t.userId !== userStore.user?.id);
  }

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(t =>
      t.name.toLowerCase().includes(keyword) ||
      (t.description && t.description.toLowerCase().includes(keyword))
    );
  }

  // 排序
  filtered.sort((a, b) => {
    if (sortBy.value === 'name') {
      return a.name.localeCompare(b.name);
    } else {
      return new Date(b.createdAt) - new Date(a.createdAt);
    }
  });

  return filtered;
});



// Methods
const editTemplate = (template) => {
  editingTemplate.value = template;
  showCreateDialogLocal.value = true;
};

const previewTemplate = (template) => {
  previewingTemplate.value = template;
  showPreviewDialog.value = true;
};

const handleDuplicate = (template) => {
  if (isMobile.value) {
    ElMessage.warning('请在电脑端进行模式管理操作');
    return;
  }
  duplicateTemplate(template);
};

const handleEdit = (template) => {
  if (isMobile.value) {
    ElMessage.warning('请在电脑端进行模式管理操作');
    return;
  }
  editTemplate(template);
};

const handlePreview = (template) => {
  if (isMobile.value) {
    ElMessage.warning('请在电脑端进行模式管理操作');
    return;
  }
  previewTemplate(template);
};

const handleDelete = (template) => {
  if (isMobile.value) {
    ElMessage.warning('请在电脑端进行模式管理操作');
    return;
  }
  deleteTemplate(template);
};

const duplicateTemplate = async (template) => {
  if (!userStore.isLoggedIn) {
    promptLogin();
    return;
  }
  
  try {
    const { value: newName } = await ElMessageBox.prompt(
      '请输入新模板的名称',
      '复制模板',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: `${template.name} - 副本`,
        inputValidator: (value) => {
          if (!value || value.trim().length === 0) {
            return '模板名称不能为空';
          }
          if (value.trim().length > 100) {
            return '模板名称不能超过100个字符';
          }
          return true;
        }
      }
    );

    await templateStore.duplicateTemplate(template.id, newName.trim());
  } catch (error) {
    if (error !== 'cancel') {
      console.error('复制模板失败:', error);
    }
  }
};

// 提示登录
const promptLogin = () => {
  ElMessage.warning('请先登录后再进行此操作');
};

// 跳转到登录页面
const goToLogin = () => {
  router.push('/login?redirect=' + encodeURIComponent('/templates'));
};

const deleteTemplate = async (template) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板"${template.name}"吗？此操作不可撤销。`,
      '删除模板',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    await templateStore.deleteTemplate(template.id);
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除模板失败:', error);
    }
  }
};

const handleTemplateSaved = () => {
  showCreateDialogLocal.value = false;
  editingTemplate.value = null;

  // 不需要重新加载模板列表，因为 createTemplate 和 updateTemplate 已经更新了本地状态
  // templateStore.loadTemplates();
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

// 切换学习模式
const toggleLearningMode = (template) => {
  if (currentLearningModeId.value === template.id) {
    // 当前已选中，取消选择
    globalPlaybackService.setGlobalTemplate(null);
    currentLearningModeId.value = null;
    ElMessage.success('已取消默认学习模式');
  } else {
    // 未选中，设为默认
    globalPlaybackService.setGlobalTemplate(template.id);
    currentLearningModeId.value = template.id;
    ElMessage.success(`已将"${template.name}"设为默认学习模式`);
  }
};





// Lifecycle
onMounted(() => {
  templateStore.loadTemplates();
});
</script>

<style scoped>
.template-manager {
  padding: 1.5rem;
  width: 100%;
  box-sizing: border-box;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 4rem);
}

/* 移动端适配 */
@media (max-width: 48rem) {
  .template-manager {
    padding: 1rem;
    min-height: calc(100vh - 3rem);
  }
}



.current-mode-section {
  margin-bottom: 2rem;
}

.mode-card {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #f39c12;
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 4px 16px rgba(243, 156, 18, 0.2);
}

.mode-icon {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.mode-content {
  flex: 1;
}

.mode-content h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.125rem;
  color: #d68910;
  font-weight: 600;
}

.mode-name {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #b7950b;
}

.mode-desc {
  margin: 0;
  font-size: 0.875rem;
  color: #a0826d;
  line-height: 1.4;
}

/* 模板名称样式 */
.current-icon {
  color: #f59e0b;
  margin-right: 0.5rem;
  font-size: 1rem;
}

.template-name.is-current {
  color: #f59e0b;
  font-weight: 600;
}





.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 移动端筛选栏适配 */
@media (max-width: 48rem) {
  .filter-bar {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }
  
  .filter-left {
    width: 100%;
  }
  
  .filter-left .el-input {
    width: 100% !important;
  }
  
  .filter-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .filter-right .el-select {
    flex: 1;
    margin: 0 0.25rem;
  }
}

.filter-left {
  display: flex;
  align-items: center;
}

.filter-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 表格样式优化 */
.templates-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  table-layout: fixed;
}

:deep(.templates-table .el-table__header-wrapper) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

:deep(.templates-table .el-table__header th) {
  background: transparent;
  color: #495057;
  font-weight: 600;
  font-size: 0.875rem;
  border-bottom: 2px solid #dee2e6;
}

:deep(.templates-table .el-table__row:hover) {
  background-color: #f8f9ff;
}

.template-name-cell {
  padding: 0.5rem 0;
}

.name-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.template-name {
  font-weight: 600;
  color: #303133;
  font-size: 0.95rem;
  line-height: 1.4;
}

.description-text {
  color: #606266;
  font-size: 0.875rem;
  line-height: 1.4;
}

.creator-text {
  color: #606266;
  font-size: 0.875rem;
}

.date-text {
  color: #909399;
  font-size: 0.8rem;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0;
}

.default-btn {
  min-width: 90px;
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-text {
  margin-left: 0.25rem;
  font-size: 0.8rem;
}

.default-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: #f8f9fa;
  border-radius: 20px;
  padding: 0.25rem;
}

.action-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: none;
  background: transparent;
}

.action-btn:hover {
  background: rgba(64, 158, 255, 0.1);
  transform: scale(1.1);
}

:deep(.action-btn .el-icon) {
  font-size: 14px;
}

/* 移动端卡片列表 */
.mobile-template-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.template-card {
  background: white;
  border-radius: 0.75rem;
  padding: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.template-card.is-current {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
  box-shadow: 0 4px 16px rgba(245, 158, 11, 0.2);
}

.card-header {
  margin-bottom: 0.5rem;
}

.template-info {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
}

.template-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.template-name {
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
}

.template-name.is-current {
  color: #f59e0b;
}

.current-icon {
  color: #f59e0b;
  font-size: 1rem;
}

.template-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.card-description {
  font-size: 0.875rem;
  color: #606266;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: #909399;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f0f0f0;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
}

.card-actions .action-group {
  display: flex;
  gap: 0.25rem;
}

.card-actions .el-button {
  border-radius: 0.5rem;
}

/* 空状态样式 */
.empty-state {
  margin-top: 2rem;
  padding: 2rem;
  text-align: center;
}

.empty-tip {
  color: #909399;
  font-size: 0.875rem;
}

/* 移动端表格适配 */
@media (max-width: 48rem) {
  .templates-table {
    font-size: 0.875rem;
  }
  
  :deep(.templates-table .el-table__cell) {
    padding: 0.5rem 0.25rem;
  }
  
  .template-name-cell {
    padding: 0.25rem 0;
  }
  
  .name-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .action-buttons {
    gap: 0.25rem;
  }
  
  .default-btn {
    min-width: 80px;
    font-size: 0.75rem;
  }
  
  .btn-text {
    font-size: 0.7rem;
  }
  
  .action-group {
    gap: 0.125rem;
    padding: 0.125rem;
  }
  
  .action-btn {
    width: 24px;
    height: 24px;
  }
  
  :deep(.action-btn .el-icon) {
    font-size: 12px;
  }
}
</style>
