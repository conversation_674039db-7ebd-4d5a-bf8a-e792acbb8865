'use strict';

module.exports = (sequelize, DataTypes) => {
  const FeaturePermission = sequelize.define('FeaturePermission', {
    featureKey: {
      type: DataTypes.STRING(50),
      allowNull: false,
      field: 'feature_key',
      comment: '功能标识符，如 content_management, editor_access'
    },
    userId: {
      type: DataTypes.STRING(50),
      allowNull: false,
      field: 'user_id',
      comment: '用户ID'
    }
  }, {
    tableName: 'feature_permissions',
    underscored: true,
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['feature_key', 'user_id'],
        name: 'feature_permissions_feature_user_unique'
      }
    ]
  });

  FeaturePermission.associate = function(models) {
    // 与用户表关联
    FeaturePermission.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
  };

  return FeaturePermission;
};
