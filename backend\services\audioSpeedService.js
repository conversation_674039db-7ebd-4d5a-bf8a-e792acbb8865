/**
 * 音频倍速处理服务
 * 提供音频倍速处理和缓存管理功能
 */
const fs = require("fs");
const path = require("path");
const crypto = require("crypto");
const ffmpeg = require("fluent-ffmpeg");
const ffmpegPath = require("@ffmpeg-installer/ffmpeg").path;
const axios = require("axios");
const { promisify } = require("util");
const { pipeline } = require("stream");

// 设置 ffmpeg 路径
ffmpeg.setFfmpegPath(ffmpegPath);

// 异步 pipeline
const pipelineAsync = promisify(pipeline);

class AudioSpeedService {
  constructor() {
    // 缓存目录 - 使用项目内路径
    this.cacheDir = path.join(__dirname, "../cache/audio-speed");
    this.tempDir = path.join(__dirname, "../temp");

    // 确保目录存在
    this.ensureDirectories();
  }

  /**
   * 确保必要的目录存在
   */
  ensureDirectories() {
    const dirs = [this.cacheDir, this.tempDir];
    dirs.forEach((dir) => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`创建目录: ${dir}`);
      }
    });
  }

  /**
   * 生成缓存文件路径
   * @param {string} audioPath - 音频文件路径
   * @param {number} speed - 倍速值
   * @returns {string} 缓存文件路径
   */
  getCacheFilePath(audioPath, speed) {
    const pathInfo = path.parse(audioPath);
    const fileName = `${pathInfo.name}_${speed}x${pathInfo.ext}`;
    const cacheSubDir = path.join(this.cacheDir, pathInfo.dir);

    // 确保缓存子目录存在
    if (!fs.existsSync(cacheSubDir)) {
      fs.mkdirSync(cacheSubDir, { recursive: true });
    }

    return path.join(cacheSubDir, fileName);
  }

  /**
   * 检查缓存是否存在
   * @param {string} audioPath - 音频文件路径
   * @param {number} speed - 倍速值
   * @returns {boolean} 缓存是否存在
   */
  hasCachedFile(audioPath, speed) {
    const cacheFile = this.getCacheFilePath(audioPath, speed);
    return fs.existsSync(cacheFile);
  }

  /**
   * 获取缓存文件的读取流
   * @param {string} audioPath - 音频文件路径
   * @param {number} speed - 倍速值
   * @returns {fs.ReadStream} 文件读取流
   */
  getCachedFileStream(audioPath, speed) {
    const cacheFile = this.getCacheFilePath(audioPath, speed);
    return fs.createReadStream(cacheFile);
  }

  /**
   * 从URL下载音频文件
   * @param {string} url - 音频文件URL
   * @returns {Promise<Buffer>} 音频文件Buffer
   */
  async downloadAudio(url) {
    try {
      console.log(`下载音频文件: ${url}`);
      const response = await axios.get(url, {
        responseType: "arraybuffer",
        timeout: 30000, // 30秒超时
        headers: {
          "User-Agent": "EchoLab-AudioProcessor/1.0",
        },
      });

      if (response.status !== 200) {
        throw new Error(`下载失败，状态码: ${response.status}`);
      }

      console.log(`音频下载完成，大小: ${response.data.length} bytes`);
      return Buffer.from(response.data);
    } catch (error) {
      console.error(`下载音频失败: ${url}`, error.message);
      throw new Error(`下载音频失败: ${error.message}`);
    }
  }

  /**
   * 处理音频倍速
   * @param {Buffer} audioBuffer - 原始音频Buffer
   * @param {number} speed - 倍速值
   * @returns {Promise<Buffer>} 处理后的音频Buffer
   */
  async processAudioSpeed(audioBuffer, speed) {
    return new Promise((resolve, reject) => {
      // 生成临时文件路径
      const tempInputFile = path.join(
        this.tempDir,
        `input_${Date.now()}_${Math.random().toString(36).substring(2, 11)}.mp3`
      );
      const tempOutputFile = path.join(
        this.tempDir,
        `output_${Date.now()}_${Math.random()
          .toString(36)
          .substring(2, 11)}.mp3`
      );

      try {
        // 写入临时输入文件
        fs.writeFileSync(tempInputFile, audioBuffer);
        console.log(`临时输入文件创建: ${tempInputFile}`);

        // 使用 ffmpeg 处理倍速
        ffmpeg(tempInputFile)
          .audioFilters(`atempo=${speed}`)
          .audioCodec("libmp3lame")
          .audioBitrate("128k")
          .format("mp3")
          .on("start", (commandLine) => {
            console.log(`开始处理音频倍速 ${speed}x: ${commandLine}`);
          })
          .on("progress", (progress) => {
            if (progress.percent) {
              console.log(`处理进度: ${Math.round(progress.percent)}%`);
            }
          })
          .on("end", () => {
            try {
              console.log(`音频倍速处理完成: ${speed}x`);

              // 读取处理后的文件
              const processedBuffer = fs.readFileSync(tempOutputFile);
              console.log(`处理后音频大小: ${processedBuffer.length} bytes`);

              // 清理临时文件
              this.cleanupTempFiles([tempInputFile, tempOutputFile]);

              resolve(processedBuffer);
            } catch (readError) {
              console.error("读取处理后的音频文件失败:", readError);
              this.cleanupTempFiles([tempInputFile, tempOutputFile]);
              reject(
                new Error(`读取处理后的音频文件失败: ${readError.message}`)
              );
            }
          })
          .on("error", (error) => {
            console.error("ffmpeg 处理失败:", error);
            this.cleanupTempFiles([tempInputFile, tempOutputFile]);
            reject(new Error(`音频倍速处理失败: ${error.message}`));
          })
          .save(tempOutputFile);
      } catch (error) {
        console.error("音频倍速处理异常:", error);
        this.cleanupTempFiles([tempInputFile, tempOutputFile]);
        reject(new Error(`音频倍速处理异常: ${error.message}`));
      }
    });
  }

  /**
   * 清理临时文件
   * @param {string[]} files - 要清理的文件路径数组
   */
  cleanupTempFiles(files) {
    files.forEach((file) => {
      try {
        if (fs.existsSync(file)) {
          fs.unlinkSync(file);
          console.log(`清理临时文件: ${file}`);
        }
      } catch (error) {
        console.warn(`清理临时文件失败: ${file}`, error.message);
      }
    });
  }

  /**
   * 保存处理后的音频到缓存
   * @param {string} audioPath - 音频文件路径
   * @param {number} speed - 倍速值
   * @param {Buffer} audioBuffer - 处理后的音频Buffer
   */
  async saveCachedFile(audioPath, speed, audioBuffer) {
    try {
      const cacheFile = this.getCacheFilePath(audioPath, speed);
      await fs.promises.writeFile(cacheFile, audioBuffer);
      console.log(`缓存文件已保存: ${cacheFile}`);
    } catch (error) {
      console.error("保存缓存文件失败:", error);
      throw new Error(`保存缓存文件失败: ${error.message}`);
    }
  }
}

module.exports = new AudioSpeedService();
