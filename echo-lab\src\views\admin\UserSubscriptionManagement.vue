<template>
  <div class="user-subscription-management">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <h2>用户订阅管理</h2>
          <el-button type="primary" @click="openSubscriptionDialog()">添加订阅</el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="用户ID/邮箱">
          <el-input v-model="searchForm.userQuery" placeholder="用户ID或邮箱" clearable />
        </el-form-item>
        <el-form-item label="等级">
          <el-select v-model="searchForm.level" placeholder="全部等级" clearable>
            <el-option 
              v-for="level in levels" 
              :key="level.level" 
              :label="level.name" 
              :value="level.level" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
            <el-option label="有效" value="active" />
            <el-option label="过期" value="expired" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchSubscriptions">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 订阅列表 -->
      <el-table v-loading="loading" :data="subscriptions" border stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="user.email" label="用户邮箱" min-width="180" show-overflow-tooltip />
        <el-table-column prop="level" label="等级" width="100">
          <template #default="scope">
            {{ getLevelName(scope.row.level) }}
          </template>
        </el-table-column>
        <el-table-column prop="startDate" label="开始日期" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.startDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="endDate" label="结束日期" width="180">
          <template #default="scope">
            <span v-if="scope.row.endDate">{{ formatDate(scope.row.endDate) }}</span>
            <el-tag v-else type="success">永久</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag 
              :type="getStatusType(scope.row.status)" 
              :effect="scope.row.status === 'active' ? 'dark' : 'plain'"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button 
              v-if="scope.row.status === 'active'" 
              size="small" 
              type="warning" 
              @click="cancelSubscription(scope.row)"
            >
              取消
            </el-button>
            <el-button 
              v-if="scope.row.status !== 'active'" 
              size="small" 
              type="success" 
              @click="reactivateSubscription(scope.row)"
            >
              重新激活
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteSubscription(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加订阅对话框 -->
    <el-dialog 
      v-model="subscriptionDialog.visible" 
      title="添加订阅"
      width="500px"
    >
      <el-form 
        ref="subscriptionFormRef" 
        :model="subscriptionDialog.form" 
        :rules="subscriptionDialog.rules" 
        label-width="100px"
      >
        <el-form-item label="用户" prop="userId">
          <el-select 
            v-model="subscriptionDialog.form.userId" 
            filterable 
            remote 
            reserve-keyword 
            placeholder="搜索用户" 
            :remote-method="searchUsers" 
            :loading="subscriptionDialog.userSearchLoading"
          >
            <el-option 
              v-for="user in subscriptionDialog.userOptions" 
              :key="user.id" 
              :label="user.email" 
              :value="user.id" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="等级" prop="level">
          <el-select v-model="subscriptionDialog.form.level" placeholder="选择等级">
            <el-option 
              v-for="level in levels" 
              :key="level.level" 
              :label="level.name" 
              :value="level.level" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="开始日期" prop="startDate">
          <el-date-picker 
            v-model="subscriptionDialog.form.startDate" 
            type="datetime" 
            placeholder="选择开始日期"
            value-format="YYYY-MM-DDTHH:mm:ss.SSSZ"
          />
        </el-form-item>
        <el-form-item label="结束日期">
          <div class="end-date-container">
            <el-date-picker 
              v-model="subscriptionDialog.form.endDate" 
              type="datetime" 
              placeholder="选择结束日期"
              value-format="YYYY-MM-DDTHH:mm:ss.SSSZ"
              :disabled="subscriptionDialog.form.isLifetime"
            />
            <div class="lifetime-checkbox">
              <el-checkbox v-model="subscriptionDialog.form.isLifetime">永久</el-checkbox>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="支付ID">
          <el-input v-model="subscriptionDialog.form.paymentId" placeholder="可选" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="subscriptionDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="saveSubscription">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  getAllLevels, 
  getSubscriptions, 
  createSubscription,
  updateSubscriptionStatus,
  deleteSubscriptionById,
  searchUsersByQuery
} from '@/services/adminService';

// 数据和状态
const loading = ref(false);
const levels = ref([]);
const subscriptions = ref([]);

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
});

// 搜索表单
const searchForm = reactive({
  userQuery: '',
  level: '',
  status: ''
});

// 订阅对话框
const subscriptionDialog = reactive({
  visible: false,
  userSearchLoading: false,
  userOptions: [],
  form: {
    userId: '',
    level: '',
    startDate: new Date().toISOString(),
    endDate: '',
    isLifetime: false,
    paymentId: ''
  },
  rules: {
    userId: [{ required: true, message: '请选择用户', trigger: 'change' }],
    level: [{ required: true, message: '请选择等级', trigger: 'change' }],
    startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }]
  }
});

// 监听永久选项变化
watch(() => subscriptionDialog.form.isLifetime, (newVal) => {
  if (newVal) {
    subscriptionDialog.form.endDate = '';
  }
});

// 获取所有等级
async function fetchLevels() {
  try {
    const result = await getAllLevels();
    levels.value = result;
  } catch (error) {
    console.error('获取等级列表失败:', error);
    ElMessage.error('获取等级列表失败');
  }
}

// 获取订阅列表
async function fetchSubscriptions() {
  loading.value = true;
  try {
    const result = await getSubscriptions({
      page: pagination.page,
      pageSize: pagination.pageSize,
      userQuery: searchForm.userQuery || undefined,
      level: searchForm.level || undefined,
      status: searchForm.status || undefined
    });
    
    subscriptions.value = result.subscriptions;
    pagination.total = result.pagination.total;
  } catch (error) {
    console.error('获取订阅列表失败:', error);
    ElMessage.error('获取订阅列表失败');
  } finally {
    loading.value = false;
  }
}

// 搜索订阅
function searchSubscriptions() {
  pagination.page = 1;
  fetchSubscriptions();
}

// 重置搜索
function resetSearch() {
  searchForm.userQuery = '';
  searchForm.level = '';
  searchForm.status = '';
  searchSubscriptions();
}

// 处理分页变化
function handleSizeChange() {
  fetchSubscriptions();
}

function handleCurrentChange() {
  fetchSubscriptions();
}

// 获取等级名称
function getLevelName(level) {
  const levelObj = levels.value.find(l => l.level === level);
  return levelObj ? levelObj.name : `等级 ${level}`;
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN');
}

// 获取状态文本
function getStatusText(status) {
  const statusMap = {
    active: '有效',
    expired: '已过期',
    cancelled: '已取消'
  };
  return statusMap[status] || status;
}

// 获取状态类型
function getStatusType(status) {
  const typeMap = {
    active: 'success',
    expired: 'info',
    cancelled: 'danger'
  };
  return typeMap[status] || 'info';
}

// 打开订阅对话框
function openSubscriptionDialog() {
  subscriptionDialog.form = {
    userId: '',
    level: '',
    startDate: new Date().toISOString(),
    endDate: '',
    isLifetime: false,
    paymentId: ''
  };
  subscriptionDialog.userOptions = [];
  subscriptionDialog.visible = true;
}

// 搜索用户
async function searchUsers(query) {
  if (query.length < 2) return;
  
  subscriptionDialog.userSearchLoading = true;
  try {
    const users = await searchUsersByQuery(query);
    subscriptionDialog.userOptions = users;
  } catch (error) {
    console.error('搜索用户失败:', error);
  } finally {
    subscriptionDialog.userSearchLoading = false;
  }
}

// 保存订阅
async function saveSubscription() {
  try {
    await createSubscription({
      userId: subscriptionDialog.form.userId,
      level: subscriptionDialog.form.level,
      startDate: subscriptionDialog.form.startDate,
      endDate: subscriptionDialog.form.isLifetime ? null : subscriptionDialog.form.endDate,
      paymentId: subscriptionDialog.form.paymentId || undefined
    });
    
    ElMessage.success('订阅创建成功');
    subscriptionDialog.visible = false;
    fetchSubscriptions();
  } catch (error) {
    console.error('创建订阅失败:', error);
    ElMessage.error('创建订阅失败: ' + (error.message || '未知错误'));
  }
}

// 取消订阅
async function cancelSubscription(subscription) {
  try {
    await ElMessageBox.confirm(
      `确定要取消用户 ${subscription.user.email} 的订阅吗？`,
      '取消订阅',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    await updateSubscriptionStatus(subscription.id, 'cancelled');
    ElMessage.success('订阅已取消');
    fetchSubscriptions();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消订阅失败:', error);
      ElMessage.error('取消订阅失败');
    }
  }
}

// 重新激活订阅
async function reactivateSubscription(subscription) {
  try {
    await ElMessageBox.confirm(
      `确定要重新激活用户 ${subscription.user.email} 的订阅吗？`,
      '重新激活订阅',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success'
      }
    );
    
    await updateSubscriptionStatus(subscription.id, 'active');
    ElMessage.success('订阅已重新激活');
    fetchSubscriptions();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重新激活订阅失败:', error);
      ElMessage.error('重新激活订阅失败');
    }
  }
}

// 删除订阅
async function deleteSubscription(subscription) {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 ${subscription.user.email} 的订阅记录吗？此操作不可恢复。`,
      '删除订阅',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'danger'
      }
    );
    
    await deleteSubscriptionById(subscription.id);
    ElMessage.success('订阅已删除');
    fetchSubscriptions();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除订阅失败:', error);
      ElMessage.error('删除订阅失败');
    }
  }
}

onMounted(() => {
  fetchLevels();
  fetchSubscriptions();
});
</script>

<style scoped>
.user-subscription-management {
  padding: 1rem;
}

.page-card {
  margin-bottom: 1.5rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 1.5rem;
}

.pagination-container {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
}

.end-date-container {
  display: flex;
  align-items: center;
}

.lifetime-checkbox {
  margin-left: 1rem;
}
</style>
