import { createRouter, createWebHistory } from "vue-router";
import { isMobileDevice, isDesktopOnlyPath } from "../utils/deviceDetector";
import { isLoggedIn } from "../services/authService";
import { checkFeaturePermission } from "../services/featurePermissionService";
import { checkUserLevel } from "../services/userLevelService";
import { useUserStore } from "../stores/userStore";
import { trackPageView } from "../utils/analytics";
import adminRoutes from "./adminRoutes";

const routes = [
  // 引导流程路由
  {
    path: "/onboarding/language",
    name: "LanguageSelection",
    component: () => import("../views/onboarding/LanguageSelection.vue"),
    meta: { requiresAuth: false, isOnboarding: true },
  },
  {
    path: "/onboarding/level",
    name: "LevelSelection",
    component: () => import("../views/onboarding/LevelSelection.vue"),
    meta: { requiresAuth: false, isOnboarding: true },
  },

  // 首页
  {
    path: "/",
    name: "Home",
    component: () => import("../views/Home.vue"),
    meta: { requiresAuth: false }, // 明确设置为不需要认证
  },

  // 内容管理和编辑器 (使用统一的content_creation权限)
  {
    path: "/content",
    name: "ContentList",
    component: () => import("../views/ContentList.vue"),
    meta: {
      requiresAuth: true,
      requiredFeature: "content_creation",
    },
  },
  {
    path: "/editor/:id?",
    name: "EditContent",
    component: () => import("../views/Editor.vue"),
    props: true,
    meta: {
      requiresAuth: true,
      requiredFeature: "content_creation",
    },
  },

  // 视频播放页面
  {
    path: "/player/:id",
    name: "Player",
    component: () => import("../views/Player.vue"),
    props: true,
    meta: { requiresAuth: false }, // 明确设置为不需要认证
  },

  // 内容信息页面
  {
    path: "/content-info",
    name: "ContentInfo",
    component: () => import("../views/ContentInfo.vue"),
    meta: { requiresAuth: false }, // 明确设置为不需要认证
  },

  // 登录页面
  {
    path: "/login",
    name: "Login",
    component: () => import("../views/Login.vue"),
    meta: { requiresAuth: false },
  },

  // 个人信息页面
  {
    path: "/profile",
    name: "Profile",
    component: () => import("../views/Profile.vue"),
    meta: { requiresAuth: true },
  },

  // 设置页面
  {
    path: "/settings",
    name: "Settings",
    component: () => import("../views/Settings.vue"),
    meta: { requiresAuth: false }, // 不需要登录
  },

  // 用户反馈页面
  {
    path: "/feedback",
    name: "Feedback",
    component: () => import("../views/Feedback.vue"),
    meta: { requiresAuth: true }, // 需要登录
  },

  // 移动设备提示页面
  {
    path: "/mobile-tip",
    name: "MobileTip",
    component: () => import("../views/MobileTip.vue"),
    meta: { requiresAuth: false }, // 明确设置为不需要认证
  },

  // 未授权页面
  {
    path: "/unauthorized",
    name: "Unauthorized",
    component: () => import("../views/Unauthorized.vue"),
    meta: { requiresAuth: false }, // 明确设置为不需要认证
  },

  // 会员升级页面
  {
    path: "/upgrade",
    name: "Upgrade",
    component: () => import("../views/Upgrade.vue"),
    meta: { requiresAuth: true }, // 需要登录
  },

  // 收藏页面
  {
    path: "/favorites",
    name: "Favorites",
    component: () => import("../views/Favorites.vue"),
    meta: { requiresAuth: true }, // 需要登录
  },

  // 模板管理页面（学习模式）
  {
    path: "/templates",
    name: "Templates",
    component: () => import("../views/TemplateManagementView.vue"),
    meta: { requiresAuth: false }, // 未登录用户也可以访问
  },

  // 安装指南页面
  {
    path: "/install-guide",
    name: "InstallGuide",
    component: () => import("../views/InstallGuide.vue"),
    meta: { requiresAuth: false }, // 不需要登录
  },

  // 用户等级设置页面
  {
    path: "/level-settings",
    name: "LevelSettings",
    component: () => import("../views/LevelSettings.vue"),
    meta: { requiresAuth: false }, // 不需要登录
  },

  // 签到页面
  {
    path: "/checkin",
    name: "CheckIn",
    component: () => import("../views/CheckIn.vue"),
    meta: { requiresAuth: true }, // 需要登录
  },

  // 用户空间
  {
    path: "/user/:id",
    name: "UserSpace",
    component: () => import("../views/UserSpace.vue"),
    meta: { requiresAuth: false }, // 公开访问
  },

  // 公开合集列表
  {
    path: "/collections",
    name: "Collections",
    component: () => import("../views/Collections.vue"),
    meta: { requiresAuth: false }, // 公开访问
  },

  // 合集详情
  {
    path: "/collection/:id",
    name: "CollectionDetail",
    component: () => import("../views/CollectionDetail.vue"),
    meta: { requiresAuth: false }, // 公开访问
  },

  // 合集编辑
  {
    path: "/collection/:id/edit",
    name: "CollectionEdit",
    component: () => import("../views/CollectionEdit.vue"),
    meta: { requiresAuth: true }, // 需要登录
  },

  // 404页面
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: () => import("../views/NotFound.vue"),
    meta: { requiresAuth: false }, // 明确设置为不需要认证
  },
];

// 开发环境下添加测试页面
if (import.meta.env.DEV) {
  routes.push({
    path: "/error-test",
    name: "ErrorTest",
    component: () => import("../views/ErrorTestPage.vue"),
    meta: { requiresAuth: false },
  });
}

// 合并管理后台路由
const allRoutes = [...routes, ...adminRoutes];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes: allRoutes,
});

// 添加全局导航守卫
router.beforeEach(async (to, from, next) => {
  // 动态导入设置工具（避免循环依赖）
  const { getLearningLanguage, isOnboardingCompleted } = await import("@/utils/userSettings");

  // 检查引导流程
  const isOnboardingRoute = to.meta?.isOnboarding;
  const hasLanguageSet = !!getLearningLanguage();
  const hasCompletedOnboarding = isOnboardingCompleted();

  // 如果没有设置语言且不在引导页面，重定向到语言选择
  if (!hasLanguageSet && !isOnboardingRoute) {
    console.log("未设置学习语言，重定向到语言选择页面");
    next("/onboarding/language");
    return;
  }

  // 如果已完成引导但访问引导页面，重定向到首页
  if (hasCompletedOnboarding && isOnboardingRoute) {
    console.log("已完成引导流程，重定向到首页");
    next("/");
    return;
  }

  // 检查是否是移动设备且访问的是仅桌面路径
  if (isMobileDevice() && isDesktopOnlyPath(to.path)) {
    // 如果不是已经在移动设备提示页面，则重定向到提示页面
    if (to.name !== "MobileTip") {
      next({ name: "MobileTip" });
      return;
    }
  }

  // 检查路由是否需要认证
  const requiresAuth = to.matched.some(
    (record) => record.meta.requiresAuth !== false
  );

  // 如果路由需要认证且用户未登录，则重定向到登录页面
  if (requiresAuth && !isLoggedIn()) {
    next({
      path: "/login",
      query: { redirect: to.fullPath }, // 保存原始请求路径
    });
    return;
  }

  // 检查路由是否需要特定功能权限
  const requiredFeature = to.matched.find(
    (record) => record.meta.requiredFeature
  )?.meta.requiredFeature;

  if (requiredFeature && isLoggedIn()) {
    try {
      // 检查用户是否有权限访问该功能
      const hasPermission = await checkFeaturePermission(requiredFeature);

      if (!hasPermission) {
        // 如果没有权限，重定向到未授权页面
        next({
          name: "Unauthorized",
          params: { feature: requiredFeature },
        });
        return;
      }
    } catch (error) {
      console.error("检查功能权限失败:", error);
      // 出错时默认拒绝访问
      next({ name: "Unauthorized" });
      return;
    }
  }

  // 检查路由是否需要特定用户等级
  const requiredLevel = to.matched.find(
    (record) => record.meta.requiredLevel !== undefined
  )?.meta.requiredLevel;

  if (requiredLevel !== undefined && isLoggedIn()) {
    try {
      // 检查用户是否达到所需等级
      const hasRequiredLevel = await checkUserLevel(requiredLevel);

      if (!hasRequiredLevel) {
        // 如果没有所需等级，重定向到会员升级页面
        next({
          name: "Upgrade",
          query: {
            redirect: to.fullPath,
            requiredLevel: requiredLevel,
          },
        });
        return;
      }
    } catch (error) {
      console.error("检查用户等级失败:", error);
      // 出错时默认拒绝访问
      next({ name: "Unauthorized" });
      return;
    }
  }

  // 通过所有检查，允许访问
  next();
});

// 添加路由后置守卫，用于页面浏览跟踪
router.afterEach((to) => {
  // 排除管理后台页面的访问跟踪
  if (to.path.startsWith("/admin")) {
    return;
  }

  // 延迟跟踪页面浏览，确保 GA 已加载
  setTimeout(() => {
    trackPageView(to.path, to.meta?.title || to.name).catch(() => {
      // 静默处理错误，避免影响用户体验
    });
  }, 1000);
});

export default router;
