/**
 * 权限服务
 * 提供权限检查和管理功能
 */
const db = require("../models");
const { Op } = require("sequelize");

/**
 * 检查用户是否有权限访问特定功能
 * @param {string} userId - 用户ID
 * @param {string} featureKey - 功能标识符
 * @returns {Promise<boolean>} 是否有权限
 */
async function checkPermission(userId, featureKey) {
  try {
    // 视频播放功能始终允许访问
    if (featureKey === "player_access") {
      return true;
    }

    // 获取用户信息
    const user = await db.User.findByPk(userId);

    if (!user) {
      return false;
    }

    // 管理员始终有权限
    if (user.role === "admin") {
      return true;
    }

    // 检查功能是否全局启用
    const featureFlag = await db.FeatureFlag.findOne({
      where: { featureKey, isEnabled: true },
    });

    if (featureFlag) {
      return true;
    }

    // 检查用户等级是否有权限
    const levelPermission = await db.LevelPermission.findOne({
      where: {
        level: user.level,
        featureKey,
      },
    });

    if (levelPermission) {
      return true;
    }

    // 检查用户是否有特定权限
    const userPermission = await db.FeaturePermission.findOne({
      where: {
        userId,
        featureKey,
      },
    });

    return !!userPermission;
  } catch (error) {
    console.error("检查权限失败:", error);
    return false;
  }
}

/**
 * 获取用户可用的所有功能
 * @param {string} userId - 用户ID
 * @returns {Promise<Array<string>>} 功能标识符列表
 */
async function getUserFeatures(userId) {
  try {
    // 获取用户信息
    const user = await db.User.findByPk(userId);

    if (!user) {
      return [];
    }

    // 管理员拥有所有功能
    if (user.role === "admin") {
      // 获取所有功能标识符
      const allFeatures = await db.FeatureFlag.findAll({
        attributes: ["featureKey"],
      });
      return allFeatures.map((feature) => feature.featureKey);
    }

    // 获取全局启用的功能
    const globalFeatures = await db.FeatureFlag.findAll({
      where: { isEnabled: true },
      attributes: ["featureKey"],
    });

    // 获取用户等级拥有的功能
    const levelFeatures = await db.LevelPermission.findAll({
      where: { level: user.level },
      attributes: ["featureKey"],
    });

    // 获取用户特定权限
    const userFeatures = await db.FeaturePermission.findAll({
      where: { userId },
      attributes: ["featureKey"],
    });

    // 合并所有功能并去重
    const features = [
      ...globalFeatures.map((f) => f.featureKey),
      ...levelFeatures.map((f) => f.featureKey),
      ...userFeatures.map((f) => f.featureKey),
    ];

    return [...new Set(features)];
  } catch (error) {
    console.error("获取用户功能失败:", error);
    return [];
  }
}

/**
 * 检查用户是否达到所需等级
 * @param {string} userId - 用户ID
 * @param {number} requiredLevel - 所需等级
 * @returns {Promise<boolean>} 是否达到所需等级
 */
async function checkUserLevel(userId, requiredLevel) {
  try {
    // 获取用户信息
    const user = await db.User.findByPk(userId);

    if (!user) {
      return false;
    }

    // 管理员始终有权限
    if (user.role === "admin") {
      return true;
    }

    // 检查用户等级
    return user.level >= requiredLevel;
  } catch (error) {
    console.error("检查用户等级失败:", error);
    return false;
  }
}

/**
 * 获取用户功能使用情况
 * @param {string} userId - 用户ID
 * @param {string} featureKey - 功能标识符
 * @returns {Promise<Object>} 使用情况
 */
async function getFeatureUsage(userId, featureKey) {
  try {
    // 获取用户信息
    const user = await db.User.findByPk(userId);

    if (!user) {
      return { error: "用户不存在" };
    }

    // 获取功能使用限制
    const usageLimit = await db.FeatureUsageLimit.findOne({
      where: {
        level: user.level,
        featureKey,
      },
    });

    // 如果没有限制，返回无限制
    if (!usageLimit) {
      return {
        hasLimit: false,
        dailyLimit: null,
        monthlyLimit: null,
        dailyUsage: 0,
        monthlyUsage: 0,
      };
    }

    // 获取今日使用次数
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const dailyUsage = await db.FeatureUsageRecord.count({
      where: {
        userId,
        featureKey,
        usedAt: {
          [Op.gte]: today,
        },
      },
    });

    // 获取本月使用次数
    const firstDayOfMonth = new Date();
    firstDayOfMonth.setDate(1);
    firstDayOfMonth.setHours(0, 0, 0, 0);

    const monthlyUsage = await db.FeatureUsageRecord.count({
      where: {
        userId,
        featureKey,
        usedAt: {
          [Op.gte]: firstDayOfMonth,
        },
      },
    });

    return {
      hasLimit: true,
      dailyLimit: usageLimit.dailyLimit,
      monthlyLimit: usageLimit.monthlyLimit,
      dailyUsage,
      monthlyUsage,
      dailyRemaining: usageLimit.dailyLimit
        ? Math.max(0, usageLimit.dailyLimit - dailyUsage)
        : null,
      monthlyRemaining: usageLimit.monthlyLimit
        ? Math.max(0, usageLimit.monthlyLimit - monthlyUsage)
        : null,
    };
  } catch (error) {
    console.error("获取功能使用情况失败:", error);
    return { error: "获取使用情况失败" };
  }
}

module.exports = {
  checkPermission,
  getUserFeatures,
  checkUserLevel,
  getFeatureUsage,
};
