<!--
  完整的用户引导流程组件
  包含语言选择和等级选择两个步骤
-->
<template>
  <div class="onboarding-flow">
    <!-- 第一步：语言选择 -->
    <LanguageGuide
      v-model="showLanguageGuide"
      v-model:selected-language="selectedLanguage"
      @next="handleLanguageNext"
    />
    
    <!-- 第二步：等级选择 -->
    <LevelGuide
      v-model="showLevelGuide"
      :current-language="selectedLanguage"
      v-model:selected-level="selectedLevel"
      :show-back-button="true"
      @completed="handleLevelCompleted"
      @back="handleLevelBack"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import LanguageGuide from './LanguageGuide.vue';
import LevelGuide from '@/components/common/LevelGuide.vue';
import { useOnboardingFlow } from '@/composables/useOnboardingFlow';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'completed']);

// 使用引导流程管理器
const {
  currentStep,
  selectedLanguage,
  selectedLevel,
  shouldShowOnboarding,
  ONBOARDING_STEPS,
  nextStep,
  previousStep,
  completeOnboarding
} = useOnboardingFlow();

// 控制各步骤的显示
const showLanguageGuide = computed({
  get: () => props.modelValue && currentStep.value === ONBOARDING_STEPS.LANGUAGE_SELECTION,
  set: (value) => {
    if (!value && currentStep.value === ONBOARDING_STEPS.LANGUAGE_SELECTION) {
      emit('update:modelValue', false);
    }
  }
});

const showLevelGuide = computed({
  get: () => props.modelValue && currentStep.value === ONBOARDING_STEPS.LEVEL_SELECTION,
  set: (value) => {
    if (!value && currentStep.value === ONBOARDING_STEPS.LEVEL_SELECTION) {
      emit('update:modelValue', false);
    }
  }
});

/**
 * 处理语言选择完成，进入等级选择
 */
function handleLanguageNext() {
  console.log('语言选择完成:', selectedLanguage.value);
  const success = nextStep();
  if (success) {
    console.log('进入等级选择步骤');
  }
}

/**
 * 处理等级选择返回语言选择
 */
function handleLevelBack() {
  console.log('返回语言选择');
  previousStep();
}

/**
 * 处理等级选择完成，完成整个引导流程
 */
function handleLevelCompleted(level) {
  console.log('等级选择完成:', level);
  
  // 完成引导流程
  const success = completeOnboarding();
  if (success) {
    console.log('引导流程完成');
    emit('update:modelValue', false);
    emit('completed', {
      language: selectedLanguage.value,
      level: level
    });
  }
}

// 组件挂载时检查是否需要显示引导
onMounted(() => {
  if (shouldShowOnboarding.value) {
    console.log('检测到首次访问，显示引导流程');
    emit('update:modelValue', true);
  }
});

// 暴露给父组件的方法
defineExpose({
  shouldShowOnboarding,
  resetOnboarding: () => {
    // 可以用于测试或重新引导
    const { resetOnboarding } = useOnboardingFlow();
    resetOnboarding();
  }
});
</script>

<script>
export default {
  name: 'OnboardingFlow'
};
</script>

<style scoped>
.onboarding-flow {
  /* 这个组件主要是逻辑容器，不需要特殊样式 */
}
</style>
