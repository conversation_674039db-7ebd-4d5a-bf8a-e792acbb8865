/**
 * 合集模型
 * 用于存储用户创建的合集信息
 */
module.exports = (sequelize, DataTypes) => {
  const Collection = sequelize.define(
    "Collection",
    {
      // 主键ID，字符串类型
      id: {
        type: DataTypes.STRING(21),
        primaryKey: true,
        allowNull: false,
      },

      // 合集名称
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: "合集名称",
      },

      // 合集描述
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "合集描述",
      },

      // 封面图片URL
      coverImageUrl: {
        type: DataTypes.STRING(255),
        allowNull: true,
        field: "cover_image_url",
        comment: "封面图片URL",
      },

      // 用户ID(必填)
      userId: {
        type: DataTypes.STRING(50),
        allowNull: false,
        field: "user_id",
        comment: "创建者ID",
      },

      // 是否公开
      isPublic: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: "is_public",
        comment: "是否公开",
      },

      // 标签，逗号分隔
      tags: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: "标签，逗号分隔",
      },

      // 状态：draft(草稿), published(已发布)
      status: {
        type: DataTypes.ENUM("draft", "published"),
        allowNull: false,
        defaultValue: "draft",
        comment: "合集状态",
      },

      // 浏览次数
      viewCount: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        field: "view_count",
        comment: "浏览次数",
      },

      // 收藏次数
      favoriteCount: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        field: "favorite_count",
        comment: "收藏次数",
      },
    },
    {
      // 表名
      tableName: "collections",
      // 时间戳
      timestamps: true,
      underscored: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      // 索引
      indexes: [
        {
          fields: ["user_id"],
        },
        {
          fields: ["status"],
        },
        {
          fields: ["is_public"],
        },
        {
          fields: ["created_at"],
        },
      ],
    }
  );

  Collection.associate = function (models) {
    // 与用户表关联（暂时移除外键约束，避免数据类型不匹配问题）
    Collection.belongsTo(models.User, {
      foreignKey: "userId",
      as: "creator",
      constraints: false, // 禁用外键约束
    });

    // 与合集内容关联表关联
    Collection.hasMany(models.CollectionItem, {
      foreignKey: "collectionId",
      as: "items",
      onDelete: "CASCADE",
    });

    // 与收藏表关联
    Collection.hasMany(models.CollectionFavorite, {
      foreignKey: "collectionId",
      as: "favorites",
      onDelete: "CASCADE",
    });

    // 与收藏表关联（用于查询用户收藏的合集）
    Collection.hasOne(models.CollectionFavorite, {
      foreignKey: "collectionId",
      as: "favoriteInfo",
    });
  };

  return Collection;
};
