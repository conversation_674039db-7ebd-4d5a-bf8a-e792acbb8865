/**
 * 统一的公开内容API路由
 * 合并了原来分散的多个接口
 */
const express = require("express");
const router = express.Router();
const db = require("../models");
const { Op } = require("sequelize");

/**
 * 统一的公开内容接口
 * GET /api/public/contents
 * 
 * 查询参数：
 * - type: 内容类型 (all|hot|latest|recommended)
 * - search: 搜索关键词
 * - page: 页码 (默认1)
 * - pageSize: 每页数量 (默认10)
 * - limit: 限制数量（用于type=hot|latest时，默认6）
 * - sortBy: 排序字段 (默认updatedAt)
 * - sortOrder: 排序方向 (默认DESC)
 * - category: 分类过滤
 * - tags: 标签过滤（逗号分隔）
 */
router.get("/", async (req, res) => {
  try {
    const type = req.query.type || 'all';
    const search = req.query.search;
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || parseInt(req.query.limit) || (type === 'hot' || type === 'latest' ? 6 : 10);
    const category = req.query.category;

    // 从请求头获取用户学习语言和等级
    const userLearningLanguage = req.headers['x-user-language'];
    const userLevel = req.headers['x-user-level'];

    // 构建基础查询条件
    const where = {
      status: "published",
    };

    // 添加学习语言过滤
    if (userLearningLanguage) {
      where.learningLanguage = userLearningLanguage;
    }

    // 根据类型设置特定的过滤和排序
    let orderBy = [["updatedAt", "DESC"]];

    switch (type) {
      case 'hot':
        // 热门内容：按浏览量排序
        orderBy = [[
          db.sequelize.literal(`(SELECT COUNT(*) FROM content_views WHERE content_views.content_id = Content.id)`),
          "DESC"
        ]];
        break;
        
      case 'latest':
        // 最新内容：按创建时间排序
        orderBy = [["createdAt", "DESC"]];
        break;
        
      case 'recommended':
        // 推荐内容：基于用户等级
        if (userLevel) {
          const levelMap = {
            'N5': ['N5', 'N4'], 'N4': ['N5', 'N4', 'N3'], 'N3': ['N4', 'N3', 'N2'],
            'N2': ['N3', 'N2', 'N1'], 'N1': ['N2', 'N1'],
            'A1': ['A1', 'A2'], 'A2': ['A1', 'A2', 'B1'], 'B1': ['A2', 'B1', 'B2'],
            'B2': ['B1', 'B2', 'C1'], 'C1': ['B2', 'C1', 'C2'], 'C2': ['C1', 'C2'],
            'HSK1': ['HSK1', 'HSK2'], 'HSK2': ['HSK1', 'HSK2', 'HSK3'], 'HSK3': ['HSK2', 'HSK3', 'HSK4'],
            'HSK4': ['HSK3', 'HSK4', 'HSK5'], 'HSK5': ['HSK4', 'HSK5', 'HSK6'], 'HSK6': ['HSK5', 'HSK6']
          };
          const recommendedLevels = levelMap[userLevel] || [userLevel];
          where.tags = {
            [Op.or]: recommendedLevels.map(level => ({
              [Op.like]: `%${level}%`
            }))
          };
        }
        break;
        
      case 'all':
      default:
        // 自定义排序
        if (req.query.sortBy) {
          orderBy = [[req.query.sortBy, req.query.sortOrder || "DESC"]];
        }
        break;
    }

    // 搜索过滤
    if (search && search.trim()) {
      where[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } },
        { tags: { [Op.like]: `%${search}%` } }
      ];
    }

    // 分类过滤
    if (category) {
      const categoryCondition = { [Op.like]: `%${category}%` };
      where.tags = where.tags ? 
        { [Op.and]: [where.tags, categoryCondition] } :
        categoryCondition;
    }

    // 标签过滤（多选）
    if (req.query.tags) {
      const tags = req.query.tags.split(',');
      const tagCondition = {
        [Op.or]: tags.map(tag => ({
          [Op.like]: `%${tag.trim()}%`
        }))
      };
      where.tags = where.tags ? 
        { [Op.and]: [where.tags, tagCondition] } :
        tagCondition;
    }

    // 执行查询
    const queryOptions = {
      where,
      attributes: [
        "id", "name", "description", ["thumbnail_url", "thumbnailUrl"],
        "tags", "learningLanguage", "status", 
        ["created_at", "createdAt"], ["updated_at", "updatedAt"],
        [
          db.sequelize.literal(
            `(SELECT COUNT(*) FROM content_views WHERE content_views.content_id = Content.id)`
          ),
          "viewCount"
        ],
      ],
      include: [
        {
          model: db.User,
          as: "creator",
          attributes: ["id", "username"],
        },
      ],
      order: orderBy,
    };

    // 分页处理
    if (type === 'hot' || type === 'latest') {
      // 对于热门和最新，只返回指定数量，不分页
      queryOptions.limit = pageSize;
      const contents = await db.Content.findAll(queryOptions);
      
      res.json({
        success: true,
        contents,
        type,
        total: contents.length,
      });
    } else {
      // 对于all、recommended和search，使用分页
      queryOptions.limit = pageSize;
      queryOptions.offset = (page - 1) * pageSize;
      
      const { count, rows } = await db.Content.findAndCountAll(queryOptions);
      
      res.json({
        success: true,
        contents: rows,
        pagination: {
          total: count,
          page,
          pageSize,
        },
        type,
      });
    }

  } catch (error) {
    console.error("获取公开内容失败:", error);
    res.status(500).json({
      success: false,
      error: `获取公开内容失败: ${error.message}`,
    });
  }
});

module.exports = router;
