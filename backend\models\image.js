/**
 * 图片模型
 * 用于存储图片文件的元数据和OSS访问信息
 */
module.exports = (sequelize, DataTypes) => {
  const Image = sequelize.define(
    "Image",
    {
      // 图片ID
      id: {
        type: DataTypes.STRING(25), // 增加长度以容纳前缀+nanoid
        primaryKey: true,
        allowNull: false,
      },

      // 图片类型 (cover, background等)
      type: {
        type: DataTypes.STRING(20),
        allowNull: false,
      },

      // 原始文件名
      originalName: {
        type: DataTypes.STRING(255),
        allowNull: true,
        field: "original_name",
      },

      // OSS访问URL
      ossUrl: {
        type: DataTypes.STRING(255),
        allowNull: false,
        field: "oss_url",
      },

      // OSS存储键，用于管理
      ossKey: {
        type: DataTypes.STRING(255),
        allowNull: false,
        field: "oss_key",
      },

      // 图片宽度
      width: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },

      // 图片高度
      height: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },

      // 图片格式 (jpg, png等)
      format: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },

      // 文件大小(字节)
      size: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },

      // 元数据(JSON)
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
      },
    },
    {
      tableName: "images",
      underscored: true,
      timestamps: true,
      createdAt: "created_at",
      updatedAt: "updated_at",
      indexes: [
        {
          name: "idx_type",
          fields: ["type"],
        },
        {
          name: "idx_created_at",
          fields: ["created_at"],
        },
      ],
    }
  );

  return Image;
};
