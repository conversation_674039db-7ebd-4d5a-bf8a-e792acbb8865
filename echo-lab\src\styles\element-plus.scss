// Element Plus 按需导入样式
// 这个文件会被自动导入到使用Element Plus组件的地方

// 导入基础样式 - 使用CSS变量方式
@import "element-plus/theme-chalk/el-var.css";
@import "element-plus/theme-chalk/base.css";

// 手动导入一些关键组件的样式以避免样式丢失
@import "element-plus/theme-chalk/el-message-box.css";
@import "element-plus/theme-chalk/el-message.css";
@import "element-plus/theme-chalk/el-loading.css";
@import "element-plus/theme-chalk/el-table-v2.css";
@import "element-plus/theme-chalk/el-drawer.css";

// 注意：其他组件的样式由 unplugin-vue-components 自动处理
// 只在这里导入一些核心组件和可能出现样式问题的组件样式
