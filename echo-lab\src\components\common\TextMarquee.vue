<template>
  <div class="marquee-container" :style="containerStyle">
    <div class="marquee-content" ref="contentRef" :style="contentStyle">
      <span>{{ text }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onBeforeUnmount, watch } from 'vue';

const props = defineProps({
  // 滚动文本内容
  text: {
    type: String,
    default: ''
  },
  // 滚动速度（rem/秒）- 使用相对单位，而非像素
  speed: {
    type: Number,
    default: 3
  },
  // 背景颜色
  backgroundColor: {
    type: String,
    default: 'transparent'
  },
  // 文本颜色
  textColor: {
    type: String,
    default: '#606266'
  },
  // 容器高度
  height: {
    type: String,
    default: '40px'
  }
});

const contentRef = ref(null);
const textWidth = ref(0);
const containerWidth = ref(0);
const animationDuration = ref(0);

// 计算动画持续时间
const calculateAnimationDuration = () => {
  if (!contentRef.value) return;

  // 获取文本实际宽度
  const textElement = contentRef.value;
  textWidth.value = textElement.scrollWidth;

  // 获取容器宽度
  const container = textElement.parentElement;
  containerWidth.value = container.clientWidth;

  // 计算总移动距离（容器宽度 * 1.5 + 文本宽度）
  // 使用1.5倍容器宽度而不是完整宽度，使文本更快进入视野
  const totalDistance = (containerWidth.value * 1.5) + textWidth.value;

  // 获取根元素的字体大小（用于rem到px的转换）
  const rootFontSize = parseFloat(getComputedStyle(document.documentElement).fontSize);

  // 将速度从rem/秒转换为px/秒
  const speedInPixels = props.speed * rootFontSize;

  // 计算动画持续时间（总距离 / 速度）
  // 确保最小持续时间不低于3秒，防止文本滚动过快
  animationDuration.value = Math.max(totalDistance / speedInPixels, 3);

  // 立即应用动画，不等待下一个渲染周期
  contentRef.value.style.animationDuration = `${animationDuration.value}s`;
};

// 容器样式
const containerStyle = computed(() => ({
  backgroundColor: props.backgroundColor,
  color: props.textColor,
  height: props.height
}));

// 内容样式
const contentStyle = computed(() => ({
  animationDuration: `${animationDuration.value}s`
}));

// 监听文本变化
watch(() => props.text, () => {
  // 文本变化时重新计算
  setTimeout(calculateAnimationDuration, 50);
});

// 组件挂载后计算动画时间
onMounted(() => {
  // 立即计算一次
  calculateAnimationDuration();

  // 短暂延迟后再次计算，确保DOM完全渲染
  setTimeout(calculateAnimationDuration, 50);

  // 监听窗口大小变化
  window.addEventListener('resize', calculateAnimationDuration);

  // 添加可见性变化监听，确保在标签页切换回来时动画正常
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      calculateAnimationDuration();
    }
  });
});

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', calculateAnimationDuration);
  document.removeEventListener('visibilitychange', calculateAnimationDuration);
});
</script>

<style scoped>
.marquee-container {
  width: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
}

.marquee-content {
  display: inline-block;
  white-space: nowrap;
  animation-name: marquee;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  padding: 0 8px;
  font-size: 14px;
  position: relative;
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

@keyframes marquee {
  0% {
    transform: translateX(50%);
  }

  100% {
    transform: translateX(-100%);
  }
}
</style>
