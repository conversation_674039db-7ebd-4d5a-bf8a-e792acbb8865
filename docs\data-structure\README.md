# Echo Lab 数据结构

本文档概述了 Echo Lab 中的主要数据结构，包括编辑器 JSON 数据、时间线数据和播放数据。

## 数据结构概述

Echo Lab 使用多种数据结构来表示和处理内容，主要包括：

1. **编辑器 JSON 数据**：表示编辑器中的节点、连接和资源
2. **时间线数据**：表示内容的播放时间线
3. **播放数据**：表示播放器的状态和控制

这些数据结构之间存在转换关系，编辑器 JSON 数据通过时间线生成器转换为时间线数据，时间线数据用于控制播放器。

## 数据流转换

Echo Lab 中的数据流转换过程如下：

```
编辑器 JSON 数据 -> 时间线生成器 -> 时间线数据 -> 播放器
```

1. 用户在编辑器中创建和配置节点，生成编辑器 JSON 数据
2. 时间线生成器根据编辑器 JSON 数据和环节设置，生成时间线数据
3. 播放器根据时间线数据播放内容

## 数据持久化

Echo Lab 中的数据持久化方式包括：

1. **前端临时存储**：使用 localStorage 存储编辑器状态
2. **后端数据库存储**：使用 MySQL 数据库存储内容数据
3. **文件存储**：使用阿里云 OSS 存储音频和图片文件

## 数据模型关系

Echo Lab 中的数据模型关系如下：

```
User -1---n-> Content -1---1-> JSON 数据
                |
                +-1---n-> Resource
                |
                +-1---n-> Feedback
```

1. 一个用户可以创建多个内容
2. 一个内容包含一个 JSON 数据
3. 一个内容可以关联多个资源
4. 一个用户可以提交多个反馈

## 主要数据结构

Echo Lab 中的主要数据结构包括：

1. [编辑器 JSON 数据](./json-data-structure.md)：表示编辑器中的节点、连接和资源
2. [时间线数据](./timeline-data-structure.md)：表示内容的播放时间线
3. [播放数据](./player-data-structure.md)：表示播放器的状态和控制

## 数据验证

Echo Lab 使用以下方法验证数据：

1. **前端验证**：使用 JavaScript 验证用户输入
2. **后端验证**：使用 Express 中间件验证 API 请求
3. **数据库验证**：使用 Sequelize 模型验证数据

## 数据安全

Echo Lab 采取以下措施保护数据安全：

1. **认证**：使用 JWT 进行用户认证
2. **授权**：使用中间件检查用户权限
3. **加密**：使用 HTTPS 加密传输数据
4. **输入验证**：验证和清理用户输入
5. **错误处理**：使用统一的错误处理机制
