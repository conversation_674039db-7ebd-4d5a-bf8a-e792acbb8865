"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();

    // 如果表已存在，则跳过创建
    if (!tables.includes("images")) {
      // 创建图片表
      await queryInterface.createTable("images", {
        id: {
          type: Sequelize.STRING(21),
          primaryKey: true,
          allowNull: false,
        },
        type: {
          type: Sequelize.STRING(20),
          allowNull: false,
        },
        oss_url: {
          type: Sequelize.STRING(255),
          allowNull: false,
        },
        oss_key: {
          type: Sequelize.STRING(255),
          allowNull: false,
        },
        original_name: {
          type: Sequelize.STRING(255),
          allowNull: true,
        },
        format: {
          type: Sequelize.STRING(10),
          allowNull: true,
        },
        size: {
          type: Sequelize.INTEGER,
          allowNull: true,
        },
        width: {
          type: Sequelize.INTEGER,
          allowNull: true,
        },
        height: {
          type: Sequelize.INTEGER,
          allowNull: true,
        },
        metadata: {
          type: Sequelize.JSON,
          allowNull: true,
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
        },
      });

      // 添加索引
      await queryInterface.addIndex("images", ["type"], {
        name: "idx_type",
      });
      await queryInterface.addIndex("images", ["created_at"], {
        name: "idx_created_at",
      });
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("images");
  },
};
