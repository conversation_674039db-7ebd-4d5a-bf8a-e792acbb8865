import { defineStore } from "pinia";
import contentService from "@/services/contentService";

export const useContentStore = defineStore("content", {
  state: () => ({
    loading: false,
    error: null,
    contents: [],
    pagination: {
      total: 0,
    },
    filters: {
      search: "",
      status: "all",
      sortBy: "updatedAt",
      sortOrder: "desc",
    },
  }),

  getters: {
    filteredContents(state) {
      let result = [...state.contents];

      // 搜索过滤
      if (state.filters.search) {
        const searchQuery = state.filters.search.toLowerCase();
        result = result.filter(
          (item) =>
            item.name.toLowerCase().includes(searchQuery) ||
            (item.description &&
              item.description.toLowerCase().includes(searchQuery))
        );
      }

      // 状态过滤
      if (state.filters.status !== "all") {
        result = result.filter((item) => item.status === state.filters.status);
      }

      // 排序
      result.sort((a, b) => {
        const key = state.filters.sortBy;
        const orderFactor = state.filters.sortOrder === "desc" ? -1 : 1;
        return orderFactor * (a[key] < b[key] ? -1 : 1);
      });

      // 更新总数
      this.pagination.total = result.length;

      return result;
    },
  },

  actions: {
    // 获取内容列表
    async fetchContents() {
      this.loading = true;
      try {
        const response = await contentService.getAllContent();
        if (response && response.success) {
          this.contents = response.contents || [];
        } else {
          throw new Error(response?.error || "获取内容列表失败");
        }
      } catch (err) {
        console.error("获取内容列表失败:", err);
        this.error = err.message;
      } finally {
        this.loading = false;
      }
    },

    // 删除内容
    async deleteContent(id) {
      try {
        const response = await contentService.deleteContent(id);
        if (response && response.success) {
          this.contents = this.contents.filter((item) => item.id !== id);
        } else {
          throw new Error(response?.error || "删除失败");
        }
      } catch (err) {
        console.error("删除内容失败:", err);
        throw err;
      }
    },

    // 发布内容（上线）
    async publishContent(id) {
      try {
        const response = await contentService.publishContent(id);
        if (response && response.success) {
          // 更新内容状态
          const index = this.contents.findIndex((item) => item.id === id);
          if (index !== -1) {
            this.contents[index].status = "published";
          }
          return response.content;
        } else {
          throw new Error(response?.error || "发布失败");
        }
      } catch (err) {
        console.error("发布内容失败:", err);
        throw err;
      }
    },

    // 下架内容
    async unpublishContent(id) {
      try {
        const response = await contentService.unpublishContent(id);
        if (response && response.success) {
          // 更新内容状态
          const index = this.contents.findIndex((item) => item.id === id);
          if (index !== -1) {
            this.contents[index].status = "draft";
          }
          return response.content;
        } else {
          throw new Error(response?.error || "下架失败");
        }
      } catch (err) {
        console.error("下架内容失败:", err);
        throw err;
      }
    },

    // 更新过滤器
    updateFilters(filters) {
      this.filters = { ...this.filters, ...filters };
    },

    // 更新分页
    updatePagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination };
    },
  },
});
