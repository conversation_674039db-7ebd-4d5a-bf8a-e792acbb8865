<template>
  <div class="announcement-container" :class="{ 'with-icon': showIcon }">
    <div v-if="showIcon" class="announcement-icon">
      <el-icon :color="iconColor">
        <component :is="icon" />
      </el-icon>
    </div>
    <TextMarquee :text="text" :speed="speed" :backgroundColor="backgroundColor" :textColor="textColor"
      :height="height" />
  </div>
</template>

<script setup>
import { computed } from 'vue';
import TextMarquee from './TextMarquee.vue';
// 图标现在通过 unplugin-icons 自动导入，无需手动导入

const props = defineProps({
  // 公告文本
  text: {
    type: String,
    default: ''
  },
  // 滚动速度（秒）
  speed: {
    type: Number,
    default: 15
  },
  // 公告类型
  type: {
    type: String,
    default: 'info',
    validator: (value) => ['info', 'success', 'warning', 'error'].includes(value)
  },
  // 是否显示图标
  showIcon: {
    type: Boolean,
    default: true
  },
  // 自定义图标
  icon: {
    type: String,
    default: ''
  },
  // 自定义背景色
  backgroundColor: {
    type: String,
    default: ''
  },
  // 自定义文本颜色
  textColor: {
    type: String,
    default: ''
  },
  // 容器高度
  height: {
    type: String,
    default: '40px'
  }
});

// 根据类型计算图标
const computedIcon = computed(() => {
  if (props.icon) return props.icon;

  switch (props.type) {
    case 'success':
      return 'i-ep-success-filled';
    case 'warning':
      return 'i-ep-warning-filled';
    case 'error':
      return 'i-ep-circle-close-filled';
    case 'info':
    default:
      return 'i-ep-info-filled';
  }
});

// 根据类型计算背景色
const computedBackgroundColor = computed(() => {
  if (props.backgroundColor) return props.backgroundColor;

  switch (props.type) {
    case 'success':
      return '#f0f9eb';
    case 'warning':
      return '#fdf6ec';
    case 'error':
      return '#fef0f0';
    case 'info':
    default:
      return '#f4f4f5';
  }
});

// 根据类型计算文本颜色
const computedTextColor = computed(() => {
  if (props.textColor) return props.textColor;

  switch (props.type) {
    case 'success':
      return '#67c23a';
    case 'warning':
      return '#e6a23c';
    case 'error':
      return '#f56c6c';
    case 'info':
    default:
      return '#909399';
  }
});

// 根据类型计算图标颜色
const iconColor = computed(() => {
  return computedTextColor.value;
});
</script>

<style scoped>
.announcement-container {
  display: flex;
  align-items: center;
  border-radius: 4px;
  overflow: hidden;
  background-color: v-bind(computedBackgroundColor);
  color: v-bind(computedTextColor);
  width: 100%;
}

.announcement-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  font-size: 16px;
}

.with-icon {
  padding-left: 0;
}
</style>
